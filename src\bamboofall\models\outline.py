"""
大纲数据模型

定义小说大纲的树状结构
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
import uuid
import json


class OutlineNodeType(Enum):
    """大纲节点类型枚举"""
    ROOT = "root"                    # 根节点
    PART = "part"                    # 部分
    CHAPTER = "chapter"              # 章节
    SCENE = "scene"                  # 场景
    EVENT = "event"                  # 事件
    NOTE = "note"                    # 备注


class OutlineNodeStatus(Enum):
    """大纲节点状态枚举"""
    PLANNED = "planned"              # 已计划
    WRITING = "writing"              # 写作中
    COMPLETE = "complete"            # 已完成
    ARCHIVED = "archived"            # 已归档


@dataclass
class OutlineNode:
    """大纲节点模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    project_id: str = ""
    
    # 节点信息
    title: str = ""
    content: str = ""
    type: OutlineNodeType = OutlineNodeType.NOTE
    status: OutlineNodeStatus = OutlineNodeStatus.PLANNED
    
    # 树状结构
    parent_id: Optional[str] = None
    children_ids: List[str] = field(default_factory=list)
    order_index: int = 0
    level: int = 0
    
    # 关联信息
    chapter_id: Optional[str] = None
    scene_ids: List[str] = field(default_factory=list)
    character_ids: List[str] = field(default_factory=list)
    
    # 预估信息
    estimated_word_count: Optional[int] = None
    actual_word_count: int = 0
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # 标签
    tags: List[str] = field(default_factory=list)
    
    # 扩展属性
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 备注
    notes: str = ""
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()
    
    def add_child(self, child_id: str):
        """添加子节点"""
        if child_id not in self.children_ids:
            self.children_ids.append(child_id)
            self.update_timestamp()
    
    def remove_child(self, child_id: str):
        """移除子节点"""
        if child_id in self.children_ids:
            self.children_ids.remove(child_id)
            self.update_timestamp()
    
    def move_child(self, child_id: str, new_index: int):
        """移动子节点位置"""
        if child_id in self.children_ids:
            self.children_ids.remove(child_id)
            self.children_ids.insert(new_index, child_id)
            self.update_timestamp()
    
    def add_scene(self, scene_id: str):
        """添加场景"""
        if scene_id not in self.scene_ids:
            self.scene_ids.append(scene_id)
            self.update_timestamp()
    
    def remove_scene(self, scene_id: str):
        """移除场景"""
        if scene_id in self.scene_ids:
            self.scene_ids.remove(scene_id)
            self.update_timestamp()
    
    def add_character(self, character_id: str):
        """添加角色"""
        if character_id not in self.character_ids:
            self.character_ids.append(character_id)
            self.update_timestamp()
    
    def remove_character(self, character_id: str):
        """移除角色"""
        if character_id in self.character_ids:
            self.character_ids.remove(character_id)
            self.update_timestamp()
    
    def get_progress_percentage(self) -> float:
        """获取进度百分比"""
        if not self.estimated_word_count:
            return 100.0 if self.status == OutlineNodeStatus.COMPLETE else 0.0
        return min(100.0, (self.actual_word_count / self.estimated_word_count) * 100)
    
    def is_leaf(self) -> bool:
        """是否为叶子节点"""
        return len(self.children_ids) == 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "project_id": self.project_id,
            "title": self.title,
            "content": self.content,
            "type": self.type.value,
            "status": self.status.value,
            "parent_id": self.parent_id,
            "children_ids": self.children_ids,
            "order_index": self.order_index,
            "level": self.level,
            "chapter_id": self.chapter_id,
            "scene_ids": self.scene_ids,
            "character_ids": self.character_ids,
            "estimated_word_count": self.estimated_word_count,
            "actual_word_count": self.actual_word_count,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "tags": self.tags,
            "metadata": self.metadata,
            "notes": self.notes,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "OutlineNode":
        """从字典创建大纲节点"""
        # 处理枚举类型
        node_type = OutlineNodeType(data.get("type", "note"))
        status = OutlineNodeStatus(data.get("status", "planned"))
        
        # 处理时间
        created_at = datetime.fromisoformat(data["created_at"]) if data.get("created_at") else datetime.now()
        updated_at = datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else datetime.now()
        
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            project_id=data.get("project_id", ""),
            title=data.get("title", ""),
            content=data.get("content", ""),
            type=node_type,
            status=status,
            parent_id=data.get("parent_id"),
            children_ids=data.get("children_ids", []),
            order_index=data.get("order_index", 0),
            level=data.get("level", 0),
            chapter_id=data.get("chapter_id"),
            scene_ids=data.get("scene_ids", []),
            character_ids=data.get("character_ids", []),
            estimated_word_count=data.get("estimated_word_count"),
            actual_word_count=data.get("actual_word_count", 0),
            created_at=created_at,
            updated_at=updated_at,
            tags=data.get("tags", []),
            metadata=data.get("metadata", {}),
            notes=data.get("notes", ""),
        )
    
    def __str__(self) -> str:
        return f"OutlineNode(id={self.id}, title={self.title}, type={self.type.value})"


@dataclass
class Outline:
    """大纲模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    project_id: str = ""
    name: str = ""
    description: str = ""
    
    # 根节点ID
    root_node_id: Optional[str] = None
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # 版本信息
    version: int = 1
    
    # 备注
    notes: str = ""
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "project_id": self.project_id,
            "name": self.name,
            "description": self.description,
            "root_node_id": self.root_node_id,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "version": self.version,
            "notes": self.notes,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Outline":
        """从字典创建大纲"""
        # 处理时间
        created_at = datetime.fromisoformat(data["created_at"]) if data.get("created_at") else datetime.now()
        updated_at = datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else datetime.now()
        
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            project_id=data.get("project_id", ""),
            name=data.get("name", ""),
            description=data.get("description", ""),
            root_node_id=data.get("root_node_id"),
            created_at=created_at,
            updated_at=updated_at,
            version=data.get("version", 1),
            notes=data.get("notes", ""),
        )
    
    def __str__(self) -> str:
        return f"Outline(id={self.id}, name={self.name})"