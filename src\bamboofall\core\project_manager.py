"""
项目管理服务

处理项目的创建、打开、保存等核心业务逻辑
"""

import logging
import shutil
from pathlib import Path
from typing import List, Optional, Dict, Any
from datetime import datetime
from functools import lru_cache
from ..models.project import Project, ProjectStatus, ProjectPriority
from ..database.repositories import ProjectRepository, CharacterRepository, ChapterRepository
from ..utils.logger import LoggerMixin
from ..utils.config_utils import get_config_manager
from .cache_manager import get_project_cache, get_cache_manager
from .performance_monitor import performance_monitor, memory_profiler
from .async_task_manager import get_task_manager, background_task

logger = logging.getLogger(__name__)


class ProjectManager(LoggerMixin):
    """项目管理器"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.current_project: Optional[Project] = None
        self.recent_projects: List[str] = []
        
        # 初始化仓储
        self.project_repo = ProjectRepository()
        self.character_repo = CharacterRepository()
        self.chapter_repo = ChapterRepository()
        
        # 缓存和性能优化
        self.cache_manager = get_cache_manager()
        self.project_cache = get_project_cache()
        self.task_manager = get_task_manager()
        
        # 项目缓存
        self._project_cache: Dict[str, Project] = {}
        self._max_cache_size = 10
        
        self.load_recent_projects()
    
    def _add_to_local_cache(self, project_id: str, project: Project):
        """添加到本地缓存"""
        if len(self._project_cache) >= self._max_cache_size:
            # LRU淘汰最旧的项目
            oldest_key = next(iter(self._project_cache))
            del self._project_cache[oldest_key]
        
        self._project_cache[project_id] = project
    
    @background_task
    def _preload_project_data(self, project_id: str):
        """异步预加载项目相关数据"""
        try:
            # 预加载角色数据
            characters = self.character_repo.get_by_project_id(project_id)
            self.cache_manager.set(f"characters_{project_id}", characters, ttl=600)
            
            # 预加载章节数据
            chapters = self.chapter_repo.get_by_project_id(project_id)
            self.cache_manager.set(f"chapters_{project_id}", chapters, ttl=600)
            
            self.logger.debug(f"项目数据预加载完成: {project_id}")
        except Exception as e:
            self.logger.error(f"预加载项目数据失败: {e}")
    
    @background_task
    def _schedule_backup(self, project: Project):
        """异步备份项目"""
        try:
            backup_data = {
                'project': project.to_dict(),
                'timestamp': datetime.now().isoformat(),
                'version': '1.0'
            }
            
            # 保存到磁盘缓存作为备份
            backup_key = f"backup_{project.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self.cache_manager.set(backup_key, backup_data, ttl=86400 * 7)  # 保存7天
            
            self.logger.debug(f"项目备份完成: {project.name}")
        except Exception as e:
            self.logger.error(f"项目备份失败: {e}")
    
    @performance_monitor(category="project")
    @memory_profiler
    def create_project(
        self, 
        name: str, 
        genre: str = "现代都市",
        description: str = "",
        author: str = "",
        target_audience: str = ""
    ) -> Project:
        """
        创建新项目
        
        Args:
            name: 项目名称
            genre: 项目类型
            description: 项目描述
            author: 作者名称
            target_audience: 目标读者
            
        Returns:
            创建的项目对象
            
        Raises:
            ValueError: 当项目名称无效时
        """
        self.logger.info(f"开始创建项目: {name}")
        
        # 验证项目名称
        if not name or not name.strip():
            raise ValueError("项目名称不能为空")
        
        if len(name.strip()) < 2:
            raise ValueError("项目名称至少需要2个字符")
        
        # 创建项目对象
        project = Project(
            name=name.strip(),
            genre=genre or "现代都市",
            status=ProjectStatus.DRAFT,
            priority=ProjectPriority.MEDIUM,
            description=description.strip() if description else "",
            author=author.strip() if author else ""
        )
        
        # 保存到数据库
        if self.project_repo.create(project):
            # 添加到最近项目
            self._add_to_recent_projects(project.id)
            
            self.logger.info(f"项目创建成功: {name} (ID: {project.id})")
            return project
        else:
            raise RuntimeError("项目创建失败")
    
    @performance_monitor(category="project")
    @memory_profiler
    def open_project(self, project_id: str) -> Project:
        """
        打开项目
        
        Args:
            project_id: 项目ID
            
        Returns:
            打开的项目对象
            
        Raises:
            FileNotFoundError: 当项目不存在时
        """
        self.logger.info(f"打开项目: {project_id}")
        
        # 先检查缓存
        cached_project = self.project_cache.get_project(project_id)
        if cached_project:
            self.logger.debug(f"从缓存加载项目: {project_id}")
            self.current_project = cached_project
            self._add_to_recent_projects(project_id)
            return cached_project
        
        # 从数据库加载项目
        project = self.project_repo.get_by_id(project_id)
        if not project:
            raise FileNotFoundError(f"项目不存在: {project_id}")
        
        # 更新最后打开时间
        project.update_timestamp()
        self.project_repo.update(project)
        
        # 缓存项目数据
        self.project_cache.cache_project(project_id, project)
        self._add_to_local_cache(project_id, project)
        
        # 设置为当前项目
        self.current_project = project
        
        # 添加到最近项目
        self._add_to_recent_projects(project_id)
        
        # 异步预加载相关数据
        self._preload_project_data(project_id)
        
        self.logger.info(f"项目打开成功: {project.name}")
        return project
    
    @performance_monitor(category="project")
    def save_project(self, project: Optional[Project] = None) -> bool:
        """
        保存项目
        
        Args:
            project: 要保存的项目，None表示保存当前项目
            
        Returns:
            保存是否成功
        """
        if project is None:
            project = self.current_project
        
        if not project:
            self.logger.warning("没有项目需要保存")
            return False
        
        try:
            # 更新时间戳
            project.update_timestamp()
            
            # 保存到数据库
            self.project_repo.update(project)
            
            # 更新缓存
            self.project_cache.cache_project(project.id, project)
            self._add_to_local_cache(project.id, project)
            
            # 异步备份项目
            self._schedule_backup(project)
            
            self.logger.info(f"项目保存成功: {project.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存项目失败: {e}")
            return False
    
    def close_project(self) -> bool:
        """
        关闭当前项目
        
        Returns:
            关闭是否成功
        """
        if not self.current_project:
            return True
        
        # 自动保存
        self.save_project()
        
        project_name = self.current_project.name
        self.current_project = None
        
        self.logger.info(f"项目已关闭: {project_name}")
        return True
    
    @performance_monitor(category="project")
    def delete_project(self, project_id: str) -> bool:
        """
        删除项目
        
        Args:
            project_id: 项目ID
            
        Returns:
            删除是否成功
        """
        try:
            project = self.project_repo.get_by_id(project_id)
            if not project:
                return False
            
            # 如果是当前项目，先关闭
            if self.current_project and self.current_project.id == project_id:
                self.close_project()
            
            # 从数据库删除
            self.project_repo.delete(project_id)
            
            # 从最近项目中移除
            if project_id in self.recent_projects:
                self.recent_projects.remove(project_id)
                self.save_recent_projects()
            
            self.logger.info(f"项目删除成功: {project.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除项目失败: {e}")
            return False
    
    @performance_monitor(category="project")
    def get_all_projects(self) -> List[Project]:
        """获取所有项目"""
        try:
            # 检查缓存
            cached_projects = self.cache_manager.get("all_projects")
            if cached_projects:
                return cached_projects
                
            # 从数据库获取
            projects = self.project_repo.get_all()
            
            # 缓存结果
            self.cache_manager.set("all_projects", projects, ttl=300)  # 5分钟缓存
            
            return projects
        except Exception as e:
            self.logger.error(f"获取项目列表失败: {e}")
            return []
    
    @performance_monitor(category="project")
    def get_recent_projects(self, limit: int = 10) -> List[Project]:
        """获取最近项目"""
        try:
            # 检查缓存
            cache_key = f"recent_projects_{limit}"
            cached_projects = self.cache_manager.get(cache_key)
            if cached_projects:
                return cached_projects
                
            # 从数据库获取
            projects = self.project_repo.get_recent(limit)
            
            # 缓存结果
            self.cache_manager.set(cache_key, projects, ttl=60)  # 1分钟缓存
            
            return projects
        except Exception as e:
            self.logger.error(f"获取最近项目失败: {e}")
            return []
    
    @performance_monitor(category="project")
    @lru_cache(maxsize=32)
    def search_projects(self, keyword: str) -> List[Project]:
        """搜索项目"""
        try:
            # 检查缓存
            cache_key = f"search_projects_{keyword}"
            cached_results = self.cache_manager.get(cache_key)
            if cached_results:
                return cached_results
                
            # 从数据库搜索
            results = self.project_repo.search(keyword)
            
            # 缓存结果
            self.cache_manager.set(cache_key, results, ttl=300)  # 5分钟缓存
            
            return results
        except Exception as e:
            self.logger.error(f"搜索项目失败: {e}")
            return []
    
    def clear_cache(self, project_id: Optional[str] = None):
        """清理缓存"""
        if project_id:
            # 清理特定项目的缓存
            self.project_cache.invalidate_project(project_id)
            self._project_cache.pop(project_id, None)
            
            # 清理相关数据缓存
            patterns = [
                f"characters_{project_id}",
                f"chapters_{project_id}",
                f"backup_{project_id}_*"
            ]
            for pattern in patterns:
                self.cache_manager.invalidate_pattern(pattern)
        else:
            # 清理所有项目缓存
            self.project_cache.clear()
            self._project_cache.clear()
            self.cache_manager.invalidate_pattern("all_projects")
            self.cache_manager.invalidate_pattern("recent_projects_*")
            self.cache_manager.invalidate_pattern("search_projects_*")
            
            # 清除LRU缓存
            if hasattr(self, 'search_projects'):
                self.search_projects.cache_clear()
                
        self.logger.info(f"项目缓存已清除: {project_id or '全部'}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = {
            'local_cache_size': len(self._project_cache),
            'max_cache_size': self._max_cache_size,
            'project_cache_stats': self.project_cache.get_stats(),
            'global_cache_stats': self.cache_manager.get_stats()
        }
        
        # 添加LRU缓存统计
        if hasattr(self, 'search_projects'):
            stats['search_cache'] = self.search_projects.cache_info()
            
        return stats
    
    def _get_default_project_path(self, project_name: str) -> str:
        """获取默认项目路径"""
        # 获取项目根目录
        config = self.config_manager.config
        if hasattr(config, 'projects_directory'):
            projects_dir = Path(config.projects_directory)
        else:
            # 使用用户文档目录
            import os
            if os.name == 'nt':  # Windows
                projects_dir = Path.home() / 'Documents' / 'BambooFall Projects'
            else:  # macOS/Linux
                projects_dir = Path.home() / 'BambooFall Projects'
        
        projects_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成唯一项目名称
        base_path = projects_dir / project_name
        project_path = base_path
        counter = 1
        
        while project_path.exists():
            project_path = projects_dir / f"{project_name}_{counter}"
            counter += 1
        
        return str(project_path)
    
    def _create_project_directory(self, project: Project):
        """创建项目目录结构"""
        project_dir = Path(project.project_path)
        project_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        subdirs = [
            'chapters',     # 章节文件
            'characters',   # 角色资料
            'scenes',       # 场景资料
            'outlines',     # 大纲文件
            'resources',    # 资源文件
            'exports',      # 导出文件
            'backups'       # 备份文件
        ]
        
        for subdir in subdirs:
            (project_dir / subdir).mkdir(exist_ok=True)
        
        self.logger.debug(f"项目目录结构创建完成: {project_dir}")
    
    def _save_project_file(self, project: Project):
        """保存项目文件"""
        if not project.project_path:
            return
        
        project_file = Path(project.project_path) / 'project.json'
        project.save_to_file(str(project_file))
        
        self.logger.debug(f"项目文件保存完成: {project_file}")
    
    def _add_to_recent_projects(self, project_id: str):
        """添加到最近项目"""
        if project_id in self.recent_projects:
            self.recent_projects.remove(project_id)
        
        self.recent_projects.insert(0, project_id)
        
        # 限制最近项目数量
        max_recent = 20
        if len(self.recent_projects) > max_recent:
            self.recent_projects = self.recent_projects[:max_recent]
        
        self.save_recent_projects()
    
    def load_recent_projects(self):
        """加载最近项目列表"""
        try:
            config_file = self.config_manager.config_dir / 'recent_projects.json'
            if config_file.exists():
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.recent_projects = json.load(f)
        except Exception as e:
            self.logger.error(f"加载最近项目失败: {e}")
            self.recent_projects = []
    
    def save_recent_projects(self):
        """保存最近项目列表"""
        try:
            config_file = self.config_manager.config_dir / 'recent_projects.json'
            import json
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.recent_projects, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存最近项目失败: {e}")


# 全局项目管理器实例
_project_manager: Optional[ProjectManager] = None


def get_project_manager() -> ProjectManager:
    """获取全局项目管理器实例"""
    global _project_manager
    if _project_manager is None:
        _project_manager = ProjectManager()
    return _project_manager