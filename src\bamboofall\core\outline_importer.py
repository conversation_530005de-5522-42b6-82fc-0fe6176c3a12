# -*- coding: utf-8 -*-
"""
大纲导入器

支持从多种格式导入大纲：JSON、Markdown、CSV、XML等
"""

import logging
import json
import os
import re
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
from pathlib import Path
from uuid import uuid4

from ..models.outline import OutlineNode, OutlineNodeType, OutlineNodeStatus
from ..exceptions.exceptions import ImportError, ValidationError

logger = logging.getLogger(__name__)


class OutlineImporter:
    """大纲导入器"""
    
    def __init__(self):
        self.supported_formats = [
            'json',
            'markdown', 'md',
            'txt', 'text',
            'csv',
            'xml',
            'opml'
        ]
        
    def import_outline(self, file_path: str, project_id: str, format_type: str = None) -> List[OutlineNode]:
        """导入大纲
        
        Args:
            file_path: 导入文件路径
            project_id: 项目ID
            format_type: 导入格式，如果为None则根据文件扩展名判断
            
        Returns:
            List[OutlineNode]: 导入的大纲节点列表
        """
        try:
            if not os.path.exists(file_path):
                raise ValidationError(f"文件不存在: {file_path}")
                
            # 确定导入格式
            if not format_type:
                format_type = self._get_format_from_extension(file_path)
                
            if format_type not in self.supported_formats:
                raise ValidationError(f"不支持的导入格式: {format_type}")
                
            # 根据格式导入
            if format_type == 'json':
                return self._import_json(file_path, project_id)
            elif format_type in ['markdown', 'md']:
                return self._import_markdown(file_path, project_id)
            elif format_type in ['txt', 'text']:
                return self._import_text(file_path, project_id)
            elif format_type == 'csv':
                return self._import_csv(file_path, project_id)
            elif format_type == 'xml':
                return self._import_xml(file_path, project_id)
            elif format_type == 'opml':
                return self._import_opml(file_path, project_id)
            else:
                raise ValidationError(f"未实现的导入格式: {format_type}")
                
        except Exception as e:
            logger.error(f"导入大纲失败: {e}")
            raise ImportError(f"导入失败: {e}")
            
    def _get_format_from_extension(self, file_path: str) -> str:
        """从文件扩展名获取格式"""
        ext = Path(file_path).suffix.lower().lstrip('.')
        return ext if ext in self.supported_formats else 'txt'
        
    def _import_json(self, file_path: str, project_id: str) -> List[OutlineNode]:
        """从JSON文件导入"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            nodes = []
            
            # 检查数据格式
            if 'nodes' in data:
                # 标准导出格式
                node_data_list = data['nodes']
            elif isinstance(data, list):
                # 简单节点列表
                node_data_list = data
            else:
                raise ValidationError("无效的JSON格式")
                
            # 创建节点映射
            id_mapping = {}  # 原ID -> 新ID
            
            # 第一遍：创建所有节点，建立ID映射
            for node_data in node_data_list:
                old_id = node_data.get('id')
                new_id = str(uuid4())
                id_mapping[old_id] = new_id
                
                # 解析节点类型和状态
                node_type = OutlineNodeType.CHAPTER
                if 'type' in node_data:
                    try:
                        node_type = OutlineNodeType(node_data['type'])
                    except ValueError:
                        pass
                        
                node_status = OutlineNodeStatus.DRAFT
                if 'status' in node_data:
                    try:
                        node_status = OutlineNodeStatus(node_data['status'])
                    except ValueError:
                        pass
                        
                # 创建节点
                node = OutlineNode(
                    id=new_id,
                    project_id=project_id,
                    title=node_data.get('title', '未命名'),
                    content=node_data.get('content', ''),
                    type=node_type,
                    status=node_status,
                    level=node_data.get('level', 0),
                    order_index=node_data.get('order_index', 0),
                    estimated_word_count=node_data.get('estimated_word_count'),
                    actual_word_count=node_data.get('actual_word_count'),
                    tags=node_data.get('tags', []),
                    metadata=node_data.get('metadata', {}),
                    notes=node_data.get('notes', '')
                )
                
                nodes.append(node)
                
            # 第二遍：设置父子关系
            for i, node_data in enumerate(node_data_list):
                node = nodes[i]
                
                # 设置父节点
                old_parent_id = node_data.get('parent_id')
                if old_parent_id and old_parent_id in id_mapping:
                    node.parent_id = id_mapping[old_parent_id]
                    
                # 设置子节点
                old_children_ids = node_data.get('children_ids', [])
                new_children_ids = []
                for old_child_id in old_children_ids:
                    if old_child_id in id_mapping:
                        new_children_ids.append(id_mapping[old_child_id])
                node.children_ids = new_children_ids
                
                # 设置关联ID
                node.chapter_id = node_data.get('chapter_id')
                node.scene_ids = node_data.get('scene_ids', [])
                node.character_ids = node_data.get('character_ids', [])
                
            return nodes
            
        except Exception as e:
            logger.error(f"导入JSON失败: {e}")
            raise ImportError(f"JSON导入失败: {e}")
            
    def _import_markdown(self, file_path: str, project_id: str) -> List[OutlineNode]:
        """从Markdown文件导入"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            nodes = []
            lines = content.split('\n')
            
            current_node = None
            current_content = []
            node_stack = []  # 用于跟踪层级关系
            
            for line in lines:
                line = line.strip()
                
                # 检查是否是标题行
                if line.startswith('#'):
                    # 保存之前的节点
                    if current_node:
                        current_node.content = '\n'.join(current_content).strip()
                        nodes.append(current_node)
                        current_content = []
                        
                    # 解析标题级别
                    level = 0
                    while level < len(line) and line[level] == '#':
                        level += 1
                        
                    title = line[level:].strip()
                    
                    # 创建新节点
                    node_id = str(uuid4())
                    current_node = OutlineNode(
                        id=node_id,
                        project_id=project_id,
                        title=title,
                        type=self._guess_node_type_from_title(title),
                        status=OutlineNodeStatus.DRAFT,
                        level=level - 1,  # 转换为0开始的级别
                        order_index=len(nodes)
                    )
                    
                    # 设置父子关系
                    while node_stack and node_stack[-1]['level'] >= current_node.level:
                        node_stack.pop()
                        
                    if node_stack:
                        parent_node = node_stack[-1]['node']
                        current_node.parent_id = parent_node.id
                        parent_node.children_ids.append(current_node.id)
                        
                    node_stack.append({
                        'node': current_node,
                        'level': current_node.level
                    })
                    
                elif current_node and line:
                    # 解析节点属性
                    if line.startswith('**类型**:'):
                        type_str = line.replace('**类型**:', '').strip()
                        try:
                            current_node.type = OutlineNodeType(type_str)
                        except ValueError:
                            pass
                    elif line.startswith('**状态**:'):
                        status_str = line.replace('**状态**:', '').strip()
                        try:
                            current_node.status = OutlineNodeStatus(status_str)
                        except ValueError:
                            pass
                    elif line.startswith('**预计字数**:'):
                        word_count_str = line.replace('**预计字数**:', '').strip()
                        try:
                            current_node.estimated_word_count = int(word_count_str)
                        except ValueError:
                            pass
                    elif line.startswith('**实际字数**:'):
                        word_count_str = line.replace('**实际字数**:', '').strip()
                        try:
                            current_node.actual_word_count = int(word_count_str)
                        except ValueError:
                            pass
                    elif line.startswith('**标签**:'):
                        tags_str = line.replace('**标签**:', '').strip()
                        current_node.tags = [tag.strip() for tag in tags_str.split(',') if tag.strip()]
                    elif not line.startswith('**') and not line.startswith('---'):
                        current_content.append(line)
                        
            # 保存最后一个节点
            if current_node:
                current_node.content = '\n'.join(current_content).strip()
                nodes.append(current_node)
                
            return nodes
            
        except Exception as e:
            logger.error(f"导入Markdown失败: {e}")
            raise ImportError(f"Markdown导入失败: {e}")
            
    def _import_text(self, file_path: str, project_id: str) -> List[OutlineNode]:
        """从纯文本文件导入"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            nodes = []
            lines = content.split('\n')
            
            current_node = None
            current_content = []
            node_stack = []
            
            for line in lines:
                # 检查是否是编号标题行（如：1. 标题、1.1 标题等）
                match = re.match(r'^(\s*)(\d+(?:\.\d+)*)\.\s+(.+)$', line)
                if match:
                    # 保存之前的节点
                    if current_node:
                        current_node.content = '\n'.join(current_content).strip()
                        nodes.append(current_node)
                        current_content = []
                        
                    indent = len(match.group(1))
                    number = match.group(2)
                    title = match.group(3)
                    
                    # 计算层级
                    level = number.count('.')
                    
                    # 创建新节点
                    node_id = str(uuid4())
                    current_node = OutlineNode(
                        id=node_id,
                        project_id=project_id,
                        title=title,
                        type=self._guess_node_type_from_title(title),
                        status=OutlineNodeStatus.DRAFT,
                        level=level,
                        order_index=len(nodes)
                    )
                    
                    # 设置父子关系
                    while node_stack and node_stack[-1]['level'] >= current_node.level:
                        node_stack.pop()
                        
                    if node_stack:
                        parent_node = node_stack[-1]['node']
                        current_node.parent_id = parent_node.id
                        parent_node.children_ids.append(current_node.id)
                        
                    node_stack.append({
                        'node': current_node,
                        'level': current_node.level
                    })
                    
                elif current_node and line.strip():
                    current_content.append(line.strip())
                    
            # 保存最后一个节点
            if current_node:
                current_node.content = '\n'.join(current_content).strip()
                nodes.append(current_node)
                
            return nodes
            
        except Exception as e:
            logger.error(f"导入文本失败: {e}")
            raise ImportError(f"文本导入失败: {e}")
            
    def _import_csv(self, file_path: str, project_id: str) -> List[OutlineNode]:
        """从CSV文件导入"""
        try:
            import csv
            
            nodes = []
            id_mapping = {}  # 原ID -> 新ID
            
            with open(file_path, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                
                # 第一遍：创建所有节点
                rows = list(reader)
                for row in rows:
                    old_id = row.get('ID', '')
                    new_id = str(uuid4())
                    id_mapping[old_id] = new_id
                    
                    # 解析节点类型和状态
                    node_type = OutlineNodeType.CHAPTER
                    if '类型' in row:
                        try:
                            node_type = OutlineNodeType(row['类型'])
                        except ValueError:
                            pass
                            
                    node_status = OutlineNodeStatus.DRAFT
                    if '状态' in row:
                        try:
                            node_status = OutlineNodeStatus(row['状态'])
                        except ValueError:
                            pass
                            
                    # 创建节点
                    node = OutlineNode(
                        id=new_id,
                        project_id=project_id,
                        title=row.get('标题', '未命名'),
                        content=row.get('内容', ''),
                        type=node_type,
                        status=node_status,
                        level=int(row.get('层级', 0)),
                        order_index=int(row.get('排序', 0)),
                        estimated_word_count=int(row.get('预计字数', 0)) or None,
                        actual_word_count=int(row.get('实际字数', 0)) or None,
                        tags=row.get('标签', '').split(',') if row.get('标签') else [],
                        notes=row.get('备注', '')
                    )
                    
                    nodes.append(node)
                    
                # 第二遍：设置父子关系
                for i, row in enumerate(rows):
                    node = nodes[i]
                    
                    # 设置父节点
                    old_parent_id = row.get('父节点ID', '')
                    if old_parent_id and old_parent_id in id_mapping:
                        node.parent_id = id_mapping[old_parent_id]
                        
                    # 设置关联信息
                    node.chapter_id = row.get('关联章节', '') or None
                    
                    character_str = row.get('相关角色', '')
                    if character_str:
                        node.character_ids = [c.strip() for c in character_str.split(',')]
                        
            return nodes
            
        except Exception as e:
            logger.error(f"导入CSV失败: {e}")
            raise ImportError(f"CSV导入失败: {e}")
            
    def _import_xml(self, file_path: str, project_id: str) -> List[OutlineNode]:
        """从XML文件导入"""
        try:
            import xml.etree.ElementTree as ET
            
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            nodes = []
            id_mapping = {}  # 原ID -> 新ID
            
            def parse_node(xml_node, parent_id=None, level=0):
                old_id = xml_node.get('id', '')
                new_id = str(uuid4())
                id_mapping[old_id] = new_id
                
                # 解析节点属性
                title = xml_node.findtext('title', '未命名')
                content = xml_node.findtext('content', '')
                
                node_type = OutlineNodeType.CHAPTER
                type_text = xml_node.findtext('type', '')
                if type_text:
                    try:
                        node_type = OutlineNodeType(type_text)
                    except ValueError:
                        pass
                        
                node_status = OutlineNodeStatus.DRAFT
                status_text = xml_node.findtext('status', '')
                if status_text:
                    try:
                        node_status = OutlineNodeStatus(status_text)
                    except ValueError:
                        pass
                        
                # 创建节点
                node = OutlineNode(
                    id=new_id,
                    project_id=project_id,
                    title=title,
                    content=content,
                    type=node_type,
                    status=node_status,
                    parent_id=parent_id,
                    level=level,
                    order_index=int(xml_node.findtext('order_index', 0)),
                    estimated_word_count=int(xml_node.findtext('estimated_word_count', 0)) or None,
                    actual_word_count=int(xml_node.findtext('actual_word_count', 0)) or None,
                    notes=xml_node.findtext('notes', '')
                )
                
                # 解析标签
                tags_elem = xml_node.find('tags')
                if tags_elem is not None:
                    node.tags = [tag.text for tag in tags_elem.findall('tag') if tag.text]
                    
                # 解析角色ID
                char_ids_elem = xml_node.find('character_ids')
                if char_ids_elem is not None:
                    node.character_ids = [char_id.text for char_id in char_ids_elem.findall('character_id') if char_id.text]
                    
                # 设置关联章节
                node.chapter_id = xml_node.findtext('chapter_id') or None
                
                nodes.append(node)
                
                # 递归处理子节点
                children_elem = xml_node.find('children')
                if children_elem is not None:
                    for child_xml in children_elem.findall('node'):
                        child_node = parse_node(child_xml, new_id, level + 1)
                        node.children_ids.append(child_node.id)
                        
                return node
                
            # 解析所有根节点
            nodes_elem = root.find('nodes')
            if nodes_elem is not None:
                for node_xml in nodes_elem.findall('node'):
                    parse_node(node_xml)
                    
            return nodes
            
        except Exception as e:
            logger.error(f"导入XML失败: {e}")
            raise ImportError(f"XML导入失败: {e}")
            
    def _import_opml(self, file_path: str, project_id: str) -> List[OutlineNode]:
        """从OPML文件导入"""
        try:
            import xml.etree.ElementTree as ET
            
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            nodes = []
            
            def parse_outline(outline_elem, parent_id=None, level=0):
                # 获取属性
                title = outline_elem.get('text', outline_elem.get('title', '未命名'))
                
                # 创建节点
                node_id = str(uuid4())
                node = OutlineNode(
                    id=node_id,
                    project_id=project_id,
                    title=title,
                    type=self._guess_node_type_from_title(title),
                    status=OutlineNodeStatus.DRAFT,
                    parent_id=parent_id,
                    level=level,
                    order_index=len(nodes)
                )
                
                # 解析其他属性
                if 'note' in outline_elem.attrib:
                    node.content = outline_elem.get('note')
                if '_note' in outline_elem.attrib:
                    node.notes = outline_elem.get('_note')
                    
                nodes.append(node)
                
                # 递归处理子节点
                for child_outline in outline_elem.findall('outline'):
                    child_node = parse_outline(child_outline, node_id, level + 1)
                    node.children_ids.append(child_node.id)
                    
                return node
                
            # 查找body元素
            body = root.find('body')
            if body is not None:
                for outline in body.findall('outline'):
                    parse_outline(outline)
                    
            return nodes
            
        except Exception as e:
            logger.error(f"导入OPML失败: {e}")
            raise ImportError(f"OPML导入失败: {e}")
            
    def _guess_node_type_from_title(self, title: str) -> OutlineNodeType:
        """根据标题猜测节点类型"""
        title_lower = title.lower()
        
        if any(keyword in title_lower for keyword in ['章', 'chapter']):
            return OutlineNodeType.CHAPTER
        elif any(keyword in title_lower for keyword in ['节', 'section', '场景', 'scene']):
            return OutlineNodeType.SCENE
        elif any(keyword in title_lower for keyword in ['角色', 'character', '人物']):
            return OutlineNodeType.CHARACTER
        elif any(keyword in title_lower for keyword in ['情节', 'plot', '剧情']):
            return OutlineNodeType.PLOT
        elif any(keyword in title_lower for keyword in ['设定', 'setting', '世界']):
            return OutlineNodeType.SETTING
        else:
            return OutlineNodeType.CHAPTER
            
    def get_supported_formats(self) -> List[str]:
        """获取支持的导入格式"""
        return self.supported_formats.copy()
        
    def get_format_description(self, format_type: str) -> str:
        """获取格式描述"""
        descriptions = {
            'json': 'JSON数据文件 (*.json)',
            'markdown': 'Markdown文档 (*.md)',
            'md': 'Markdown文档 (*.md)',
            'txt': '纯文本文件 (*.txt)',
            'text': '纯文本文件 (*.txt)',
            'csv': 'CSV表格文件 (*.csv)',
            'xml': 'XML数据文件 (*.xml)',
            'opml': 'OPML大纲文件 (*.opml)'
        }
        return descriptions.get(format_type, f'{format_type}文件')
        
    def validate_import_file(self, file_path: str, format_type: str = None) -> Tuple[bool, str]:
        """验证导入文件
        
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            if not os.path.exists(file_path):
                return False, "文件不存在"
                
            if not format_type:
                format_type = self._get_format_from_extension(file_path)
                
            if format_type not in self.supported_formats:
                return False, f"不支持的文件格式: {format_type}"
                
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size > 50 * 1024 * 1024:  # 50MB
                return False, "文件过大（超过50MB）"
                
            if file_size == 0:
                return False, "文件为空"
                
            # 基本格式验证
            if format_type == 'json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    json.load(f)
            elif format_type in ['xml', 'opml']:
                import xml.etree.ElementTree as ET
                ET.parse(file_path)
                
            return True, ""
            
        except json.JSONDecodeError:
            return False, "无效的JSON格式"
        except ET.ParseError:
            return False, "无效的XML格式"
        except Exception as e:
            return False, f"文件验证失败: {e}"


# 全局导入器实例
_outline_importer = None


def get_outline_importer() -> OutlineImporter:
    """获取大纲导入器实例"""
    global _outline_importer
    if _outline_importer is None:
        _outline_importer = OutlineImporter()
    return _outline_importer