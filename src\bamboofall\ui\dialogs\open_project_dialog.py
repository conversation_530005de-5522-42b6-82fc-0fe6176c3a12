"""
打开项目对话框

提供项目列表和最近项目的界面
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QLineEdit, QTabWidget, QWidget, QMessageBox,
    QFileDialog, QMenu, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QObject, QTimer
from PyQt6.QtGui import QFont, QAction, QIcon
from typing import List, Optional
import logging

from ...models.project import Project
from ...core.project_manager import get_project_manager
from ..widgets.modern_widgets import ModernButton, create_modern_button, ProjectCardWidget
from ..themes.theme_manager import get_theme_manager
from ...utils.logger import LoggerMixin

logger = logging.getLogger(__name__)


class ProjectLoadWorker(QObject):
    """项目加载工作线程"""
    
    finished = pyqtSignal(object)  # 完成信号
    
    def __init__(self, project_id: str):
        super().__init__()
        self.project_id = project_id
        self.project_manager = get_project_manager()
    
    def run(self):
        """执行项目加载"""
        try:
            project = self.project_manager.open_project(self.project_id)
            self.finished.emit(project)
        except Exception as e:
            logger.error(f"打开项目失败: {e}")
            self.finished.emit(e)


class ProjectListWidget(QWidget):
    """项目列表控件"""
    
    project_selected = pyqtSignal(str)    # 项目选择信号
    project_deleted = pyqtSignal(str)     # 项目删除信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.project_manager = get_project_manager()
        self.projects: List[Project] = []
        
        self.setup_ui()
        self.setup_style()
        self.load_projects()
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 搜索框
        search_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索项目...")
        self.search_edit.textChanged.connect(self.filter_projects)
        search_layout.addWidget(self.search_edit)
        
        self.refresh_button = create_modern_button("刷新", "secondary")
        self.refresh_button.clicked.connect(self.load_projects)
        search_layout.addWidget(self.refresh_button)
        
        layout.addLayout(search_layout)
        
        # 项目列表
        self.project_list = QListWidget()
        self.project_list.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.project_list.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.project_list.customContextMenuRequested.connect(self.show_context_menu)
        layout.addWidget(self.project_list)
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        self.project_list.setStyleSheet(f"""
            QListWidget {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 6px;
                color: {colors['text_primary']};
                outline: none;
            }}
            
            QListWidget::item {{
                padding: 0px;
                border: none;
                margin: 4px;
                border-radius: 8px;
            }}
            
            QListWidget::item:hover {{
                background-color: {colors['surface_hover']};
            }}
            
            QListWidget::item:selected {{
                background-color: {colors['primary']};
            }}
        """)
    
    def load_projects(self):
        """加载项目列表"""
        try:
            self.projects = self.project_manager.get_all_projects()
            self.refresh_list()
        except Exception as e:
            logger.error(f"加载项目列表失败: {e}")
    
    def refresh_list(self):
        """刷新列表显示"""
        self.project_list.clear()
        
        for project in self.projects:
            # 创建项目卡片
            card_data = {
                "id": project.id,
                "name": project.name,
                "description": project.description,
                "type": project.type.value,
                "word_count": project.word_count,
                "updated_at": project.updated_at.strftime("%Y-%m-%d %H:%M")
            }
            
            card = ProjectCardWidget(card_data)
            card.clicked.connect(self.project_selected.emit)
            
            # 创建列表项
            item = QListWidgetItem()
            item.setSizeHint(card.sizeHint())
            item.setData(Qt.ItemDataRole.UserRole, project.id)
            
            self.project_list.addItem(item)
            self.project_list.setItemWidget(item, card)
    
    def filter_projects(self, keyword: str):
        """过滤项目列表"""
        if not keyword.strip():
            # 显示所有项目
            for i in range(self.project_list.count()):
                item = self.project_list.item(i)
                item.setHidden(False)
        else:
            # 搜索匹配项目
            keyword = keyword.lower()
            for i in range(self.project_list.count()):
                item = self.project_list.item(i)
                project_id = item.data(Qt.ItemDataRole.UserRole)
                project = next((p for p in self.projects if p.id == project_id), None)
                
                if project:
                    match = (keyword in project.name.lower() or 
                            keyword in (project.description or "").lower() or
                            keyword in (project.author or "").lower())
                    item.setHidden(not match)
    
    def on_item_double_clicked(self, item: QListWidgetItem):
        """列表项双击事件"""
        project_id = item.data(Qt.ItemDataRole.UserRole)
        if project_id:
            self.project_selected.emit(project_id)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.project_list.itemAt(position)
        if not item:
            return
        
        project_id = item.data(Qt.ItemDataRole.UserRole)
        project = next((p for p in self.projects if p.id == project_id), None)
        if not project:
            return
        
        menu = QMenu(self)
        
        # 打开项目
        open_action = QAction("打开项目", self)
        open_action.triggered.connect(lambda: self.project_selected.emit(project_id))
        menu.addAction(open_action)
        
        menu.addSeparator()
        
        # 重命名项目
        rename_action = QAction("重命名", self)
        rename_action.triggered.connect(lambda: self.rename_project(project_id))
        menu.addAction(rename_action)
        
        # 项目属性
        properties_action = QAction("属性", self)
        properties_action.triggered.connect(lambda: self.show_project_properties(project_id))
        menu.addAction(properties_action)
        
        menu.addSeparator()
        
        # 删除项目
        delete_action = QAction("删除项目", self)
        delete_action.triggered.connect(lambda: self.delete_project(project_id))
        menu.addAction(delete_action)
        
        menu.exec(self.project_list.mapToGlobal(position))
    
    def rename_project(self, project_id: str):
        """重命名项目"""
        # TODO: 实现重命名功能
        QMessageBox.information(self, "功能开发中", "重命名功能正在开发中...")
    
    def show_project_properties(self, project_id: str):
        """显示项目属性"""
        # TODO: 实现属性对话框
        QMessageBox.information(self, "功能开发中", "项目属性功能正在开发中...")
    
    def delete_project(self, project_id: str):
        """删除项目"""
        project = next((p for p in self.projects if p.id == project_id), None)
        if not project:
            return
        
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除项目 '{project.name}' 吗？\n\n此操作将删除项目文件夹及所有内容，且无法恢复。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                if self.project_manager.delete_project(project_id):
                    self.project_deleted.emit(project_id)
                    self.load_projects()  # 重新加载列表
                    QMessageBox.information(self, "删除成功", f"项目 '{project.name}' 已删除")
                else:
                    QMessageBox.warning(self, "删除失败", "无法删除项目，请检查文件权限")
            except Exception as e:
                QMessageBox.critical(self, "删除失败", f"删除项目时发生错误:\n{str(e)}")


class OpenProjectDialog(QDialog, LoggerMixin):
    """打开项目对话框"""
    
    project_opened = pyqtSignal(object)  # 项目打开成功信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.project_manager = get_project_manager()
        
        # 工作线程
        self.worker_thread: Optional[QThread] = None
        self.worker: Optional[ProjectLoadWorker] = None
        
        self.setup_ui()
        self.setup_style()
        self.setup_signals()
        
        self.logger.info("打开项目对话框初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("打开项目")
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("选择项目")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 标签页
        self.tab_widget = QTabWidget()
        
        # 所有项目标签页
        self.all_projects_tab = ProjectListWidget()
        self.tab_widget.addTab(self.all_projects_tab, "所有项目")
        
        # 最近项目标签页
        self.recent_projects_tab = ProjectListWidget()
        self.tab_widget.addTab(self.recent_projects_tab, "最近项目")
        
        layout.addWidget(self.tab_widget)
        
        # 进度条（初始隐藏）
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setRange(0, 0)  # 不定进度
        layout.addWidget(self.progress_bar)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        
        self.browse_button = create_modern_button("浏览文件...", "secondary")
        self.browse_button.clicked.connect(self.browse_project_file)
        button_layout.addWidget(self.browse_button)
        
        button_layout.addStretch()
        
        self.cancel_button = create_modern_button("取消", "secondary")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        self.open_button = create_modern_button("打开", "primary")
        self.open_button.setEnabled(False)
        self.open_button.clicked.connect(self.open_selected_project)
        button_layout.addWidget(self.open_button)
        
        layout.addLayout(button_layout)
        
        # 加载最近项目
        self.load_recent_projects()
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {colors['background']};
                color: {colors['text_primary']};
            }}
            
            QTabWidget::pane {{
                border: 1px solid {colors['border']};
                background-color: {colors['surface']};
                border-radius: 6px;
            }}
            
            QTabBar::tab {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                color: {colors['text_secondary']};
            }}
            
            QTabBar::tab:hover {{
                background-color: {colors['surface_hover']};
            }}
            
            QTabBar::tab:selected {{
                background-color: {colors['primary']};
                color: white;
                border-bottom: none;
            }}
        """)
    
    def setup_signals(self):
        """设置信号连接"""
        # 项目选择信号
        self.all_projects_tab.project_selected.connect(self.on_project_selected)
        self.recent_projects_tab.project_selected.connect(self.on_project_selected)
        
        # 项目删除信号
        self.all_projects_tab.project_deleted.connect(self.on_project_deleted)
        self.recent_projects_tab.project_deleted.connect(self.on_project_deleted)
        
        # 主题变化
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def load_recent_projects(self):
        """加载最近项目"""
        try:
            recent_projects = self.project_manager.get_recent_projects(10)
            self.recent_projects_tab.projects = recent_projects
            self.recent_projects_tab.refresh_list()
        except Exception as e:
            logger.error(f"加载最近项目失败: {e}")
    
    def on_project_selected(self, project_id: str):
        """项目选择处理"""
        self.selected_project_id = project_id
        self.open_button.setEnabled(True)
    
    def on_project_deleted(self, project_id: str):
        """项目删除处理"""
        # 刷新两个标签页的列表
        self.all_projects_tab.load_projects()
        self.load_recent_projects()
        
        # 如果删除的是当前选中项目，禁用打开按钮
        if hasattr(self, 'selected_project_id') and self.selected_project_id == project_id:
            self.open_button.setEnabled(False)
    
    def browse_project_file(self):
        """浏览项目文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择项目文件",
            "",
            "项目文件 (*.json);;所有文件 (*)"
        )
        
        if file_path:
            try:
                # 尝试从文件加载项目
                from pathlib import Path
                project_file = Path(file_path)
                if project_file.name == 'project.json':
                    # 导入项目到数据库
                    project = Project.load_from_file(file_path)
                    saved_project = self.project_manager.project_repository.create(project)
                    
                    QMessageBox.information(
                        self,
                        "导入成功",
                        f"项目 '{saved_project.name}' 导入成功！"
                    )
                    
                    # 刷新列表
                    self.all_projects_tab.load_projects()
                    self.load_recent_projects()
                    
                else:
                    QMessageBox.warning(self, "文件格式错误", "请选择有效的项目文件 (project.json)")
                    
            except Exception as e:
                QMessageBox.critical(self, "导入失败", f"导入项目失败:\n{str(e)}")
    
    def open_selected_project(self):
        """打开选中的项目"""
        if not hasattr(self, 'selected_project_id'):
            return
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.setEnabled(False)
        
        # 创建工作线程
        self.worker_thread = QThread()
        self.worker = ProjectLoadWorker(self.selected_project_id)
        self.worker.moveToThread(self.worker_thread)
        
        # 连接信号
        self.worker_thread.started.connect(self.worker.run)
        self.worker.finished.connect(self.on_project_opened)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)
        
        # 启动线程
        self.worker_thread.start()
        
        self.logger.info(f"开始打开项目: {self.selected_project_id}")
    
    def on_project_opened(self, result):
        """项目打开完成处理"""
        # 恢复界面
        self.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        # 清理工作线程
        if self.worker_thread:
            self.worker_thread.quit()
            self.worker_thread.wait()
            self.worker_thread = None
            self.worker = None
        
        if isinstance(result, Exception):
            # 打开失败
            self.logger.error(f"项目打开失败: {result}")
            QMessageBox.critical(
                self,
                "打开失败",
                f"项目打开失败:\n{str(result)}"
            )
        else:
            # 打开成功
            self.logger.info(f"项目打开成功: {result.name}")
            
            # 发送信号
            self.project_opened.emit(result)
            
            # 关闭对话框
            self.accept()
    
    def on_theme_changed(self):
        """主题变化处理"""
        self.setup_style()