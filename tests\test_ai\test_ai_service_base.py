# -*- coding: utf-8 -*-
"""
AI服务基类测试
"""

import pytest
from unittest.mock import Mock, AsyncMock
from datetime import datetime

from bamboofall.ai.ai_service_base import (
    AIMessage, MessageRole, AIResponse, AIConfig, AIModelType,
    AIServiceError, APIKeyError
)


class TestAIMessage:
    """AIMessage测试类"""
    
    def test_create_message(self):
        """测试创建消息"""
        message = AIMessage(
            role=MessageRole.USER,
            content="测试消息"
        )
        
        assert message.role == MessageRole.USER
        assert message.content == "测试消息"
        assert message.id is not None
        assert message.timestamp > 0
        assert isinstance(message.metadata, dict)
    
    def test_message_to_dict(self):
        """测试消息转字典"""
        message = AIMessage(
            role=MessageRole.ASSISTANT,
            content="助手回复",
            metadata={"test": "value"}
        )
        
        data = message.to_dict()
        
        assert data["role"] == "assistant"
        assert data["content"] == "助手回复"
        assert "id" in data
        assert "timestamp" in data
        assert data["metadata"]["test"] == "value"
    
    def test_message_from_dict(self):
        """测试从字典创建消息"""
        data = {
            "role": "user",
            "content": "用户消息",
            "id": "test-id",
            "timestamp": 1234567890.0,
            "metadata": {"source": "test"}
        }
        
        message = AIMessage.from_dict(data)
        
        assert message.role == MessageRole.USER
        assert message.content == "用户消息"
        assert message.id == "test-id"
        assert message.timestamp == 1234567890.0
        assert message.metadata["source"] == "test"


class TestAIResponse:
    """AIResponse测试类"""
    
    def test_create_response(self):
        """测试创建响应"""
        response = AIResponse(
            content="AI响应内容",
            model="test-model",
            usage={"total_tokens": 100},
            finish_reason="stop"
        )
        
        assert response.content == "AI响应内容"
        assert response.model == "test-model"
        assert response.usage["total_tokens"] == 100
        assert response.finish_reason == "stop"
        assert response.id is not None
        assert response.timestamp > 0
    
    def test_response_to_dict(self):
        """测试响应转字典"""
        response = AIResponse(
            content="测试内容",
            model="gpt-3.5-turbo"
        )
        
        data = response.to_dict()
        
        assert data["content"] == "测试内容"
        assert data["model"] == "gpt-3.5-turbo"
        assert "id" in data
        assert "timestamp" in data


class TestAIConfig:
    """AIConfig测试类"""
    
    def test_create_config(self):
        """测试创建配置"""
        config = AIConfig(
            api_key="test-key",
            model="test-model",
            max_tokens=1000,
            temperature=0.7,
            timeout=30
        )
        
        assert config.api_key == "test-key"
        assert config.model == "test-model"
        assert config.max_tokens == 1000
        assert config.temperature == 0.7
        assert config.timeout == 30
    
    def test_config_defaults(self):
        """测试配置默认值"""
        config = AIConfig(api_key="test-key")
        
        assert config.api_key == "test-key"
        assert config.model is None
        assert config.max_tokens == 4096
        assert config.temperature == 0.7
        assert config.timeout == 30
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试有效配置
        config = AIConfig(
            api_key="valid-key",
            temperature=0.5,
            max_tokens=2000
        )
        assert config.validate() is True
        
        # 测试无效温度
        with pytest.raises(ValueError, match="temperature必须在0到2之间"):
            AIConfig(api_key="test", temperature=3.0)
        
        # 测试无效max_tokens
        with pytest.raises(ValueError, match="max_tokens必须大于0"):
            AIConfig(api_key="test", max_tokens=0)
        
        # 测试无效timeout
        with pytest.raises(ValueError, match="timeout必须大于0"):
            AIConfig(api_key="test", timeout=-1)


class TestAIServiceError:
    """AI服务错误测试类"""
    
    def test_ai_service_error(self):
        """测试AI服务错误"""
        error = AIServiceError("测试错误")
        assert str(error) == "测试错误"
        assert isinstance(error, Exception)
    
    def test_api_key_error(self):
        """测试API密钥错误"""
        error = APIKeyError("无效的API密钥")
        assert str(error) == "无效的API密钥"
        assert isinstance(error, AIServiceError)


class TestMessageRole:
    """MessageRole枚举测试"""
    
    def test_message_roles(self):
        """测试消息角色枚举"""
        assert MessageRole.SYSTEM.value == "system"
        assert MessageRole.USER.value == "user"
        assert MessageRole.ASSISTANT.value == "assistant"


class TestAIModelType:
    """AIModelType枚举测试"""
    
    def test_model_types(self):
        """测试模型类型枚举"""
        assert AIModelType.CHAT.value == "chat"
        assert AIModelType.COMPLETION.value == "completion"
        assert AIModelType.EMBEDDING.value == "embedding"