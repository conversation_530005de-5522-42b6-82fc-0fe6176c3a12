@echo off
chcp 65001 > nul
title 笔落App - AI辅助小说创作平台

echo.
echo 🌸 笔落App - AI辅助小说创作平台
echo ================================================
echo.

REM 检查Python是否安装
python --version > nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.9或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查是否在虚拟环境中
if defined VIRTUAL_ENV (
    echo 检测到虚拟环境: %VIRTUAL_ENV%
) else (
    echo 提示: 建议使用虚拟环境运行应用
    echo 创建虚拟环境: python -m venv bamboofall_env
    echo 激活虚拟环境: bamboofall_env\Scripts\activate
    echo.
)

REM 运行应用
echo 正在启动应用...
python run.py

if errorlevel 1 (
    echo.
    echo 应用启动失败，请检查错误信息
    pause
    exit /b 1
)

pause