# Flake8配置文件
[flake8]
max-line-length = 88
max-complexity = 10
select = E,W,F,C
ignore = 
    E203,  # whitespace before ':'
    E501,  # line too long (handled by black)
    W503,  # line break before binary operator
    F401,  # imported but unused (handled by autoflake)
    C901,  # too complex (handled by mccabe)

exclude =
    .git,
    __pycache__,
    .venv,
    venv,
    build,
    dist,
    *.egg-info,
    .tox,
    .mypy_cache,
    migrations,
    tests/fixtures

per-file-ignores =
    __init__.py: F401
    tests/*: F401,F811,F841
    setup.py: F401

# 扩展插件
extend-ignore = E203, E266, E501, W503, F403, F401
extend-exclude = 
    migrations,
    .venv,
    build,
    dist

# 文档字符串检查
docstring-convention = google
application-import-names = bamboofall
import-order-style = google