# -*- coding: utf-8 -*-
"""
角色模型测试
"""

import pytest
from datetime import datetime
from bamboofall.models.character import Character


class TestCharacter:
    """Character模型测试类"""
    
    def test_init_with_all_fields(self):
        """测试使用所有字段初始化"""
        character = Character(
            id="char-001",
            project_id="project-001",
            name="张三",
            description="故事的主人公，一个勇敢的年轻人",
            role="protagonist",
            age=25,
            gender="male",
            appearance="高大英俊，黑发黑眼",
            personality="勇敢、善良、有正义感",
            background="出生在小村庄，父母早逝",
            goals="拯救世界，找到真相",
            relationships={"李四": "好友", "王五": "师父"},
            tags=["主角", "英雄"],
            notes="重要角色，贯穿全文",
            created_at="2024-01-01T00:00:00",
            updated_at="2024-01-02T00:00:00"
        )
        
        assert character.id == "char-001"
        assert character.project_id == "project-001"
        assert character.name == "张三"
        assert character.description == "故事的主人公，一个勇敢的年轻人"
        assert character.role == "protagonist"
        assert character.age == 25
        assert character.gender == "male"
        assert character.appearance == "高大英俊，黑发黑眼"
        assert character.personality == "勇敢、善良、有正义感"
        assert character.background == "出生在小村庄，父母早逝"
        assert character.goals == "拯救世界，找到真相"
        assert character.relationships == {"李四": "好友", "王五": "师父"}
        assert character.tags == ["主角", "英雄"]
        assert character.notes == "重要角色，贯穿全文"
        assert character.created_at == "2024-01-01T00:00:00"
        assert character.updated_at == "2024-01-02T00:00:00"
    
    def test_init_with_minimal_fields(self):
        """测试使用最少字段初始化"""
        character = Character(
            id="char-minimal",
            project_id="project-001",
            name="最小角色"
        )
        
        assert character.id == "char-minimal"
        assert character.project_id == "project-001"
        assert character.name == "最小角色"
        assert character.description == ""
        assert character.role == ""
        assert character.age is None
        assert character.gender == ""
        assert character.appearance == ""
        assert character.personality == ""
        assert character.background == ""
        assert character.goals == ""
        assert character.relationships == {}
        assert character.tags == []
        assert character.notes == ""
        assert character.created_at is not None
        assert character.updated_at is not None
    
    def test_auto_generated_timestamps(self):
        """测试自动生成的时间戳"""
        character = Character(
            id="timestamp-test",
            project_id="project-001",
            name="时间戳测试"
        )
        
        # 验证时间戳格式
        assert isinstance(character.created_at, str)
        assert isinstance(character.updated_at, str)
        
        # 验证时间戳可以解析
        created_dt = datetime.fromisoformat(character.created_at.replace('Z', '+00:00'))
        updated_dt = datetime.fromisoformat(character.updated_at.replace('Z', '+00:00'))
        
        assert isinstance(created_dt, datetime)
        assert isinstance(updated_dt, datetime)
    
    def test_to_dict(self):
        """测试转换为字典"""
        character = Character(
            id="dict-test",
            project_id="project-001",
            name="字典测试",
            description="测试角色",
            role="supporting",
            age=30,
            gender="female",
            tags=["配角", "智者"]
        )
        
        character_dict = character.to_dict()
        
        assert isinstance(character_dict, dict)
        assert character_dict["id"] == "dict-test"
        assert character_dict["project_id"] == "project-001"
        assert character_dict["name"] == "字典测试"
        assert character_dict["description"] == "测试角色"
        assert character_dict["role"] == "supporting"
        assert character_dict["age"] == 30
        assert character_dict["gender"] == "female"
        assert character_dict["tags"] == ["配角", "智者"]
        assert "created_at" in character_dict
        assert "updated_at" in character_dict
    
    def test_from_dict(self):
        """测试从字典创建"""
        character_data = {
            "id": "from-dict-test",
            "project_id": "project-001",
            "name": "从字典创建",
            "description": "从字典数据创建的角色",
            "role": "antagonist",
            "age": 45,
            "gender": "male",
            "appearance": "阴险的面容",
            "personality": "狡猾、残忍",
            "relationships": {"张三": "敌人"},
            "tags": ["反派", "boss"],
            "notes": "最终boss"
        }
        
        character = Character.from_dict(character_data)
        
        assert character.id == "from-dict-test"
        assert character.project_id == "project-001"
        assert character.name == "从字典创建"
        assert character.description == "从字典数据创建的角色"
        assert character.role == "antagonist"
        assert character.age == 45
        assert character.gender == "male"
        assert character.appearance == "阴险的面容"
        assert character.personality == "狡猾、残忍"
        assert character.relationships == {"张三": "敌人"}
        assert character.tags == ["反派", "boss"]
        assert character.notes == "最终boss"
    
    def test_from_dict_missing_fields(self):
        """测试从不完整字典创建"""
        character_data = {
            "id": "incomplete-test",
            "project_id": "project-001",
            "name": "不完整角色"
        }
        
        character = Character.from_dict(character_data)
        
        assert character.id == "incomplete-test"
        assert character.project_id == "project-001"
        assert character.name == "不完整角色"
        assert character.description == ""
        assert character.age is None
        assert character.relationships == {}
        assert character.tags == []
    
    def test_add_relationship(self):
        """测试添加关系"""
        character = Character(
            id="relationship-test",
            project_id="project-001",
            name="关系测试"
        )
        
        # 添加新关系
        character.add_relationship("李四", "好友")
        assert character.relationships["李四"] == "好友"
        
        # 更新现有关系
        character.add_relationship("李四", "挚友")
        assert character.relationships["李四"] == "挚友"
        
        # 添加多个关系
        character.add_relationship("王五", "师父")
        character.add_relationship("赵六", "敌人")
        assert len(character.relationships) == 3
        assert character.relationships["王五"] == "师父"
        assert character.relationships["赵六"] == "敌人"
    
    def test_remove_relationship(self):
        """测试移除关系"""
        character = Character(
            id="remove-rel-test",
            project_id="project-001",
            name="移除关系测试",
            relationships={"李四": "好友", "王五": "师父", "赵六": "敌人"}
        )
        
        # 移除存在的关系
        character.remove_relationship("王五")
        assert "王五" not in character.relationships
        assert len(character.relationships) == 2
        
        # 移除不存在的关系
        character.remove_relationship("不存在")
        assert len(character.relationships) == 2
    
    def test_get_relationship(self):
        """测试获取关系"""
        character = Character(
            id="get-rel-test",
            project_id="project-001",
            name="获取关系测试",
            relationships={"李四": "好友", "王五": "师父"}
        )
        
        # 获取存在的关系
        assert character.get_relationship("李四") == "好友"
        assert character.get_relationship("王五") == "师父"
        
        # 获取不存在的关系
        assert character.get_relationship("不存在") is None
        assert character.get_relationship("不存在", "未知") == "未知"
    
    def test_add_tag(self):
        """测试添加标签"""
        character = Character(
            id="tag-test",
            project_id="project-001",
            name="标签测试"
        )
        
        # 添加新标签
        character.add_tag("主角")
        assert "主角" in character.tags
        
        # 添加重复标签
        character.add_tag("主角")
        assert character.tags.count("主角") == 1
        
        # 添加多个标签
        character.add_tag("英雄")
        character.add_tag("勇敢")
        assert len(character.tags) == 3
        assert "英雄" in character.tags
        assert "勇敢" in character.tags
    
    def test_remove_tag(self):
        """测试移除标签"""
        character = Character(
            id="remove-tag-test",
            project_id="project-001",
            name="移除标签测试",
            tags=["主角", "英雄", "勇敢"]
        )
        
        # 移除存在的标签
        character.remove_tag("英雄")
        assert "英雄" not in character.tags
        assert len(character.tags) == 2
        
        # 移除不存在的标签
        character.remove_tag("不存在")
        assert len(character.tags) == 2
    
    def test_has_tag(self):
        """测试检查标签"""
        character = Character(
            id="has-tag-test",
            project_id="project-001",
            name="检查标签测试",
            tags=["主角", "英雄"]
        )
        
        assert character.has_tag("主角") is True
        assert character.has_tag("英雄") is True
        assert character.has_tag("反派") is False
    
    def test_is_protagonist(self):
        """测试是否为主角"""
        protagonist = Character(
            id="protagonist-test",
            project_id="project-001",
            name="主角测试",
            role="protagonist"
        )
        
        supporting = Character(
            id="supporting-test",
            project_id="project-001",
            name="配角测试",
            role="supporting"
        )
        
        assert protagonist.is_protagonist() is True
        assert supporting.is_protagonist() is False
    
    def test_is_antagonist(self):
        """测试是否为反派"""
        antagonist = Character(
            id="antagonist-test",
            project_id="project-001",
            name="反派测试",
            role="antagonist"
        )
        
        protagonist = Character(
            id="protagonist-test",
            project_id="project-001",
            name="主角测试",
            role="protagonist"
        )
        
        assert antagonist.is_antagonist() is True
        assert protagonist.is_antagonist() is False
    
    def test_get_age_group(self):
        """测试获取年龄组"""
        child = Character(
            id="child",
            project_id="project-001",
            name="儿童",
            age=8
        )
        
        teenager = Character(
            id="teenager",
            project_id="project-001",
            name="青少年",
            age=16
        )
        
        adult = Character(
            id="adult",
            project_id="project-001",
            name="成年人",
            age=30
        )
        
        elder = Character(
            id="elder",
            project_id="project-001",
            name="老年人",
            age=70
        )
        
        no_age = Character(
            id="no-age",
            project_id="project-001",
            name="无年龄"
        )
        
        assert child.get_age_group() == "child"
        assert teenager.get_age_group() == "teenager"
        assert adult.get_age_group() == "adult"
        assert elder.get_age_group() == "elder"
        assert no_age.get_age_group() == "unknown"
    
    def test_validate(self):
        """测试验证"""
        # 有效角色
        valid_character = Character(
            id="valid-char",
            project_id="project-001",
            name="有效角色"
        )
        assert valid_character.validate() is True
        
        # 无效角色（空名称）
        invalid_character1 = Character(
            id="invalid1",
            project_id="project-001",
            name=""
        )
        assert invalid_character1.validate() is False
        
        # 无效角色（空项目ID）
        invalid_character2 = Character(
            id="invalid2",
            project_id="",
            name="无效角色2"
        )
        assert invalid_character2.validate() is False
        
        # 无效角色（负年龄）
        invalid_character3 = Character(
            id="invalid3",
            project_id="project-001",
            name="无效角色3",
            age=-5
        )
        assert invalid_character3.validate() is False
    
    def test_equality(self):
        """测试相等性比较"""
        character1 = Character(
            id="equal-test",
            project_id="project-001",
            name="相等测试"
        )
        
        character2 = Character(
            id="equal-test",
            project_id="project-001",
            name="相等测试"
        )
        
        character3 = Character(
            id="different-test",
            project_id="project-001",
            name="不同测试"
        )
        
        # 相同ID的角色应该相等
        assert character1 == character2
        
        # 不同ID的角色不相等
        assert character1 != character3
    
    def test_string_representation(self):
        """测试字符串表示"""
        character = Character(
            id="str-test",
            project_id="project-001",
            name="字符串测试",
            role="protagonist"
        )
        
        str_repr = str(character)
        assert "字符串测试" in str_repr
        assert "str-test" in str_repr
    
    def test_copy(self):
        """测试复制角色"""
        original = Character(
            id="original",
            project_id="project-001",
            name="原始角色",
            description="原始描述",
            age=25,
            relationships={"李四": "好友"},
            tags=["标签1", "标签2"]
        )
        
        # 复制角色
        copied = original.copy(
            new_id="copied",
            new_name="复制角色",
            new_project_id="project-002"
        )
        
        assert copied.id == "copied"
        assert copied.project_id == "project-002"
        assert copied.name == "复制角色"
        assert copied.description == "原始描述"
        assert copied.age == 25
        assert copied.relationships == {"李四": "好友"}
        assert copied.tags == ["标签1", "标签2"]
        
        # 验证是独立的副本
        copied.add_tag("新标签")
        assert "新标签" not in original.tags
    
    def test_export_data(self):
        """测试导出数据"""
        character = Character(
            id="export-test",
            project_id="project-001",
            name="导出测试",
            description="导出描述",
            role="protagonist",
            age=25,
            gender="male",
            relationships={"李四": "好友"},
            tags=["主角", "英雄"]
        )
        
        exported_data = character.export_data()
        
        # 验证导出的数据包含所有必要字段
        assert exported_data["id"] == "export-test"
        assert exported_data["project_id"] == "project-001"
        assert exported_data["name"] == "导出测试"
        assert exported_data["description"] == "导出描述"
        assert exported_data["role"] == "protagonist"
        assert exported_data["age"] == 25
        assert exported_data["gender"] == "male"
        assert exported_data["relationships"] == {"李四": "好友"}
        assert exported_data["tags"] == ["主角", "英雄"]
        assert "created_at" in exported_data
        assert "updated_at" in exported_data
    
    def test_import_data(self):
        """测试导入数据"""
        import_data = {
            "id": "import-test",
            "project_id": "project-001",
            "name": "导入测试",
            "description": "导入描述",
            "role": "antagonist",
            "age": 40,
            "gender": "female",
            "appearance": "美丽但危险",
            "personality": "狡猾、聪明",
            "relationships": {"张三": "敌人"},
            "tags": ["反派", "美女"],
            "created_at": "2024-01-01T00:00:00",
            "updated_at": "2024-01-02T00:00:00"
        }
        
        character = Character.import_data(import_data)
        
        assert character.id == "import-test"
        assert character.project_id == "project-001"
        assert character.name == "导入测试"
        assert character.description == "导入描述"
        assert character.role == "antagonist"
        assert character.age == 40
        assert character.gender == "female"
        assert character.appearance == "美丽但危险"
        assert character.personality == "狡猾、聪明"
        assert character.relationships == {"张三": "敌人"}
        assert character.tags == ["反派", "美女"]
        assert character.created_at == "2024-01-01T00:00:00"
        assert character.updated_at == "2024-01-02T00:00:00"
    
    def test_get_summary(self):
        """测试获取角色摘要"""
        character = Character(
            id="summary-test",
            project_id="project-001",
            name="摘要测试",
            description="一个测试角色",
            role="protagonist",
            age=25,
            gender="male"
        )
        
        summary = character.get_summary()
        
        assert isinstance(summary, str)
        assert "摘要测试" in summary
        assert "protagonist" in summary or "主角" in summary
        assert "25" in summary
        assert "male" in summary or "男" in summary
    
    def test_update_from_dict(self):
        """测试从字典更新角色"""
        character = Character(
            id="update-test",
            project_id="project-001",
            name="更新测试",
            age=20
        )
        
        update_data = {
            "description": "更新的描述",
            "age": 25,
            "gender": "female",
            "tags": ["新标签"]
        }
        
        character.update_from_dict(update_data)
        
        assert character.name == "更新测试"  # 未更新的字段保持不变
        assert character.description == "更新的描述"
        assert character.age == 25
        assert character.gender == "female"
        assert character.tags == ["新标签"]