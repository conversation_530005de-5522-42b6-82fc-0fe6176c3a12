# -*- coding: utf-8 -*-
"""
文件工具测试
"""

import pytest
import os
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, mock_open
from bamboofall.utils.file_utils import FileUtils


class TestFileUtils:
    """FileUtils测试类"""
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def temp_file(self, temp_dir):
        """创建临时文件"""
        temp_file = os.path.join(temp_dir, "test_file.txt")
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write("测试内容")
        return temp_file
    
    def test_read_file_success(self, temp_file):
        """测试成功读取文件"""
        content = FileUtils.read_file(temp_file)
        assert content == "测试内容"
    
    def test_read_file_not_found(self):
        """测试读取不存在的文件"""
        with pytest.raises(FileNotFoundError):
            FileUtils.read_file("/path/to/nonexistent/file.txt")
    
    def test_read_file_with_encoding(self, temp_dir):
        """测试指定编码读取文件"""
        file_path = os.path.join(temp_dir, "utf8_file.txt")
        content = "中文测试内容"
        
        # 写入UTF-8文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 读取UTF-8文件
        read_content = FileUtils.read_file(file_path, encoding='utf-8')
        assert read_content == content
    
    def test_write_file_success(self, temp_dir):
        """测试成功写入文件"""
        file_path = os.path.join(temp_dir, "write_test.txt")
        content = "写入测试内容"
        
        FileUtils.write_file(file_path, content)
        
        # 验证文件是否正确写入
        with open(file_path, 'r', encoding='utf-8') as f:
            assert f.read() == content
    
    def test_write_file_create_directory(self, temp_dir):
        """测试写入文件时自动创建目录"""
        nested_path = os.path.join(temp_dir, "nested", "dir", "file.txt")
        content = "嵌套目录测试"
        
        FileUtils.write_file(nested_path, content)
        
        # 验证目录和文件都被创建
        assert os.path.exists(nested_path)
        with open(nested_path, 'r', encoding='utf-8') as f:
            assert f.read() == content
    
    def test_write_file_with_encoding(self, temp_dir):
        """测试指定编码写入文件"""
        file_path = os.path.join(temp_dir, "encoding_test.txt")
        content = "编码测试内容"
        
        FileUtils.write_file(file_path, content, encoding='utf-8')
        
        # 验证文件编码正确
        with open(file_path, 'r', encoding='utf-8') as f:
            assert f.read() == content
    
    def test_copy_file_success(self, temp_file, temp_dir):
        """测试成功复制文件"""
        dest_path = os.path.join(temp_dir, "copied_file.txt")
        
        FileUtils.copy_file(temp_file, dest_path)
        
        # 验证文件被复制
        assert os.path.exists(dest_path)
        with open(dest_path, 'r', encoding='utf-8') as f:
            assert f.read() == "测试内容"
    
    def test_copy_file_overwrite(self, temp_file, temp_dir):
        """测试覆盖复制文件"""
        dest_path = os.path.join(temp_dir, "existing_file.txt")
        
        # 创建目标文件
        with open(dest_path, 'w', encoding='utf-8') as f:
            f.write("原始内容")
        
        # 复制并覆盖
        FileUtils.copy_file(temp_file, dest_path, overwrite=True)
        
        # 验证文件被覆盖
        with open(dest_path, 'r', encoding='utf-8') as f:
            assert f.read() == "测试内容"
    
    def test_copy_file_no_overwrite(self, temp_file, temp_dir):
        """测试不覆盖复制文件"""
        dest_path = os.path.join(temp_dir, "existing_file.txt")
        
        # 创建目标文件
        with open(dest_path, 'w', encoding='utf-8') as f:
            f.write("原始内容")
        
        # 尝试复制但不覆盖
        with pytest.raises(FileExistsError):
            FileUtils.copy_file(temp_file, dest_path, overwrite=False)
    
    def test_move_file_success(self, temp_file, temp_dir):
        """测试成功移动文件"""
        dest_path = os.path.join(temp_dir, "moved_file.txt")
        
        FileUtils.move_file(temp_file, dest_path)
        
        # 验证文件被移动
        assert not os.path.exists(temp_file)
        assert os.path.exists(dest_path)
        with open(dest_path, 'r', encoding='utf-8') as f:
            assert f.read() == "测试内容"
    
    def test_delete_file_success(self, temp_file):
        """测试成功删除文件"""
        assert os.path.exists(temp_file)
        
        FileUtils.delete_file(temp_file)
        
        assert not os.path.exists(temp_file)
    
    def test_delete_file_not_found(self):
        """测试删除不存在的文件"""
        # 删除不存在的文件不应该抛出异常
        FileUtils.delete_file("/path/to/nonexistent/file.txt")
    
    def test_file_exists(self, temp_file):
        """测试检查文件是否存在"""
        assert FileUtils.file_exists(temp_file) is True
        assert FileUtils.file_exists("/path/to/nonexistent/file.txt") is False
    
    def test_get_file_size(self, temp_file):
        """测试获取文件大小"""
        size = FileUtils.get_file_size(temp_file)
        assert size > 0
        
        # 测试不存在的文件
        with pytest.raises(FileNotFoundError):
            FileUtils.get_file_size("/path/to/nonexistent/file.txt")
    
    def test_get_file_extension(self):
        """测试获取文件扩展名"""
        assert FileUtils.get_file_extension("test.txt") == ".txt"
        assert FileUtils.get_file_extension("test.tar.gz") == ".gz"
        assert FileUtils.get_file_extension("test") == ""
        assert FileUtils.get_file_extension("/path/to/file.json") == ".json"
    
    def test_get_file_name(self):
        """测试获取文件名"""
        assert FileUtils.get_file_name("/path/to/file.txt") == "file.txt"
        assert FileUtils.get_file_name("file.txt") == "file.txt"
        assert FileUtils.get_file_name("/path/to/file") == "file"
    
    def test_get_file_name_without_extension(self):
        """测试获取不带扩展名的文件名"""
        assert FileUtils.get_file_name_without_extension("/path/to/file.txt") == "file"
        assert FileUtils.get_file_name_without_extension("file.tar.gz") == "file.tar"
        assert FileUtils.get_file_name_without_extension("file") == "file"
    
    def test_get_directory_path(self):
        """测试获取目录路径"""
        assert FileUtils.get_directory_path("/path/to/file.txt") == "/path/to"
        assert FileUtils.get_directory_path("file.txt") == ""
        assert FileUtils.get_directory_path("/path/to/") == "/path/to"
    
    def test_create_directory(self, temp_dir):
        """测试创建目录"""
        new_dir = os.path.join(temp_dir, "new_directory")
        
        FileUtils.create_directory(new_dir)
        
        assert os.path.exists(new_dir)
        assert os.path.isdir(new_dir)
    
    def test_create_nested_directory(self, temp_dir):
        """测试创建嵌套目录"""
        nested_dir = os.path.join(temp_dir, "level1", "level2", "level3")
        
        FileUtils.create_directory(nested_dir)
        
        assert os.path.exists(nested_dir)
        assert os.path.isdir(nested_dir)
    
    def test_create_existing_directory(self, temp_dir):
        """测试创建已存在的目录"""
        # 创建已存在的目录不应该抛出异常
        FileUtils.create_directory(temp_dir)
        assert os.path.exists(temp_dir)
    
    def test_delete_directory(self, temp_dir):
        """测试删除目录"""
        # 在目录中创建一些文件
        test_file = os.path.join(temp_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        
        nested_dir = os.path.join(temp_dir, "nested")
        os.makedirs(nested_dir)
        
        FileUtils.delete_directory(temp_dir)
        
        assert not os.path.exists(temp_dir)
    
    def test_list_files(self, temp_dir):
        """测试列出文件"""
        # 创建测试文件
        files = ["file1.txt", "file2.json", "file3.py"]
        for file_name in files:
            file_path = os.path.join(temp_dir, file_name)
            with open(file_path, 'w') as f:
                f.write("test")
        
        # 创建子目录
        sub_dir = os.path.join(temp_dir, "subdir")
        os.makedirs(sub_dir)
        
        listed_files = FileUtils.list_files(temp_dir)
        
        # 验证只返回文件，不包括目录
        assert len(listed_files) == 3
        for file_name in files:
            assert any(file_name in f for f in listed_files)
    
    def test_list_files_with_extension(self, temp_dir):
        """测试按扩展名列出文件"""
        # 创建不同扩展名的文件
        files = {
            "file1.txt": "txt",
            "file2.json": "json",
            "file3.py": "py",
            "file4.txt": "txt"
        }
        
        for file_name in files:
            file_path = os.path.join(temp_dir, file_name)
            with open(file_path, 'w') as f:
                f.write("test")
        
        # 只列出.txt文件
        txt_files = FileUtils.list_files(temp_dir, extension=".txt")
        assert len(txt_files) == 2
        assert all(".txt" in f for f in txt_files)
    
    def test_list_directories(self, temp_dir):
        """测试列出目录"""
        # 创建测试目录
        dirs = ["dir1", "dir2", "dir3"]
        for dir_name in dirs:
            dir_path = os.path.join(temp_dir, dir_name)
            os.makedirs(dir_path)
        
        # 创建测试文件
        file_path = os.path.join(temp_dir, "file.txt")
        with open(file_path, 'w') as f:
            f.write("test")
        
        listed_dirs = FileUtils.list_directories(temp_dir)
        
        # 验证只返回目录，不包括文件
        assert len(listed_dirs) == 3
        for dir_name in dirs:
            assert any(dir_name in d for d in listed_dirs)
    
    def test_get_file_modified_time(self, temp_file):
        """测试获取文件修改时间"""
        import time
        
        modified_time = FileUtils.get_file_modified_time(temp_file)
        assert isinstance(modified_time, float)
        assert modified_time > 0
        
        # 修改文件
        time.sleep(0.1)  # 确保时间差异
        with open(temp_file, 'a') as f:
            f.write("追加内容")
        
        new_modified_time = FileUtils.get_file_modified_time(temp_file)
        assert new_modified_time > modified_time
    
    def test_is_file_newer(self, temp_dir):
        """测试比较文件新旧"""
        import time
        
        # 创建第一个文件
        file1 = os.path.join(temp_dir, "file1.txt")
        with open(file1, 'w') as f:
            f.write("file1")
        
        time.sleep(0.1)  # 确保时间差异
        
        # 创建第二个文件
        file2 = os.path.join(temp_dir, "file2.txt")
        with open(file2, 'w') as f:
            f.write("file2")
        
        assert FileUtils.is_file_newer(file2, file1) is True
        assert FileUtils.is_file_newer(file1, file2) is False
    
    def test_backup_file(self, temp_file, temp_dir):
        """测试备份文件"""
        backup_path = FileUtils.backup_file(temp_file)
        
        # 验证备份文件存在
        assert os.path.exists(backup_path)
        assert backup_path != temp_file
        assert ".bak" in backup_path or "backup" in backup_path
        
        # 验证备份内容正确
        with open(backup_path, 'r', encoding='utf-8') as f:
            assert f.read() == "测试内容"
    
    def test_restore_from_backup(self, temp_file, temp_dir):
        """测试从备份恢复文件"""
        # 创建备份
        backup_path = FileUtils.backup_file(temp_file)
        
        # 修改原文件
        with open(temp_file, 'w', encoding='utf-8') as f:
            f.write("修改后的内容")
        
        # 从备份恢复
        FileUtils.restore_from_backup(backup_path, temp_file)
        
        # 验证文件被恢复
        with open(temp_file, 'r', encoding='utf-8') as f:
            assert f.read() == "测试内容"
    
    def test_get_safe_filename(self):
        """测试获取安全文件名"""
        # 测试包含非法字符的文件名
        unsafe_name = "file<>:\"/|?*.txt"
        safe_name = FileUtils.get_safe_filename(unsafe_name)
        
        # 验证非法字符被替换或移除
        illegal_chars = '<>:"/|?*'
        for char in illegal_chars:
            assert char not in safe_name
        
        # 测试正常文件名
        normal_name = "normal_file.txt"
        assert FileUtils.get_safe_filename(normal_name) == normal_name
    
    def test_normalize_path(self):
        """测试路径标准化"""
        # 测试不同格式的路径
        paths = [
            "/path/to/../file.txt",
            "path\\to\\file.txt",
            "./path/to/file.txt",
            "path//to//file.txt"
        ]
        
        for path in paths:
            normalized = FileUtils.normalize_path(path)
            assert isinstance(normalized, str)
            # 标准化后的路径应该不包含 .. 或 重复的分隔符
            assert ".." not in normalized or normalized.count("..") == 0
    
    def test_join_paths(self):
        """测试路径连接"""
        path1 = "/base/path"
        path2 = "subdir"
        path3 = "file.txt"
        
        joined = FileUtils.join_paths(path1, path2, path3)
        
        # 验证路径正确连接
        assert "base" in joined
        assert "subdir" in joined
        assert "file.txt" in joined
        
        # 测试空路径
        assert FileUtils.join_paths("", "file.txt") == "file.txt"
    
    def test_calculate_file_hash(self, temp_file):
        """测试计算文件哈希"""
        hash_md5 = FileUtils.calculate_file_hash(temp_file, algorithm='md5')
        hash_sha256 = FileUtils.calculate_file_hash(temp_file, algorithm='sha256')
        
        assert isinstance(hash_md5, str)
        assert isinstance(hash_sha256, str)
        assert len(hash_md5) == 32  # MD5 hash length
        assert len(hash_sha256) == 64  # SHA256 hash length
        assert hash_md5 != hash_sha256
    
    def test_compare_files(self, temp_dir):
        """测试比较文件"""
        # 创建两个相同的文件
        file1 = os.path.join(temp_dir, "file1.txt")
        file2 = os.path.join(temp_dir, "file2.txt")
        content = "相同内容"
        
        with open(file1, 'w', encoding='utf-8') as f:
            f.write(content)
        with open(file2, 'w', encoding='utf-8') as f:
            f.write(content)
        
        assert FileUtils.compare_files(file1, file2) is True
        
        # 修改其中一个文件
        with open(file2, 'w', encoding='utf-8') as f:
            f.write("不同内容")
        
        assert FileUtils.compare_files(file1, file2) is False
    
    def test_compress_file(self, temp_file, temp_dir):
        """测试压缩文件"""
        compressed_path = os.path.join(temp_dir, "compressed.zip")
        
        FileUtils.compress_file(temp_file, compressed_path)
        
        # 验证压缩文件存在
        assert os.path.exists(compressed_path)
        assert os.path.getsize(compressed_path) > 0
    
    def test_decompress_file(self, temp_file, temp_dir):
        """测试解压文件"""
        # 先压缩文件
        compressed_path = os.path.join(temp_dir, "compressed.zip")
        FileUtils.compress_file(temp_file, compressed_path)
        
        # 创建解压目录
        extract_dir = os.path.join(temp_dir, "extracted")
        
        FileUtils.decompress_file(compressed_path, extract_dir)
        
        # 验证文件被解压
        assert os.path.exists(extract_dir)
        extracted_files = os.listdir(extract_dir)
        assert len(extracted_files) > 0
    
    def test_watch_file_changes(self, temp_file):
        """测试监控文件变化"""
        # 这个测试可能需要使用文件系统监控库
        # 这里只测试基本功能
        if hasattr(FileUtils, 'watch_file_changes'):
            watcher = FileUtils.watch_file_changes(temp_file)
            assert watcher is not None
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试各种错误情况
        with pytest.raises(FileNotFoundError):
            FileUtils.read_file("/nonexistent/path/file.txt")
        
        with pytest.raises(PermissionError):
            # 尝试写入只读位置（在某些系统上）
            if os.name == 'nt':  # Windows
                FileUtils.write_file("C:\\Windows\\test.txt", "test")
            else:  # Unix-like
                FileUtils.write_file("/root/test.txt", "test")
    
    def test_unicode_support(self, temp_dir):
        """测试Unicode支持"""
        unicode_content = "测试中文内容 🎉 émojis and spëcial chars"
        unicode_filename = "测试文件_🎉.txt"
        
        file_path = os.path.join(temp_dir, unicode_filename)
        
        # 写入Unicode内容
        FileUtils.write_file(file_path, unicode_content)
        
        # 读取并验证
        read_content = FileUtils.read_file(file_path)
        assert read_content == unicode_content
    
    def test_large_file_handling(self, temp_dir):
        """测试大文件处理"""
        large_file = os.path.join(temp_dir, "large_file.txt")
        
        # 创建一个较大的文件（1MB）
        large_content = "A" * (1024 * 1024)
        
        FileUtils.write_file(large_file, large_content)
        
        # 验证文件大小
        size = FileUtils.get_file_size(large_file)
        assert size >= 1024 * 1024
        
        # 读取大文件
        read_content = FileUtils.read_file(large_file)
        assert len(read_content) == len(large_content)
    
    def test_concurrent_access(self, temp_dir):
        """测试并发访问"""
        import threading
        import time
        
        file_path = os.path.join(temp_dir, "concurrent_test.txt")
        results = []
        
        def write_worker(worker_id):
            try:
                content = f"Worker {worker_id} content"
                FileUtils.write_file(file_path, content)
                results.append(f"write_{worker_id}_success")
            except Exception as e:
                results.append(f"write_{worker_id}_error: {e}")
        
        def read_worker(worker_id):
            try:
                time.sleep(0.1)  # 等待写入完成
                content = FileUtils.read_file(file_path)
                results.append(f"read_{worker_id}_success")
            except Exception as e:
                results.append(f"read_{worker_id}_error: {e}")
        
        # 创建多个线程
        threads = []
        for i in range(3):
            t1 = threading.Thread(target=write_worker, args=(i,))
            t2 = threading.Thread(target=read_worker, args=(i,))
            threads.extend([t1, t2])
        
        # 启动所有线程
        for t in threads:
            t.start()
        
        # 等待所有线程完成
        for t in threads:
            t.join()
        
        # 验证没有严重错误
        assert len(results) > 0
        # 至少应该有一些成功的操作
        success_count = len([r for r in results if "success" in r])
        assert success_count > 0