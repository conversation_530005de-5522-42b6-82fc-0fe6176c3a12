# 笔落App - AI辅助小说创作平台（Python版本）

<div align="center">

![笔落App Logo](resources/icons/app_icon.png)

**让AI为您的创作插上翅膀**

[![Python版本](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![PyQt6](https://img.shields.io/badge/PyQt6-6.6+-green.svg)](https://www.riverbankcomputing.com/software/pyqt/)
[![许可证](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![开发状态](https://img.shields.io/badge/Status-Development-orange.svg)]()

</div>

## 📖 项目简介

笔落App是一款基于Python技术栈开发的专业AI辅助小说创作平台。采用现代化的PyQt6界面框架，集成多种AI大模型，为小说作者提供智能、美观、高效的创作环境。

### ✨ 核心特性

- 🎨 **现代化界面**: 基于PyQt6的Material Design风格界面
- 🤖 **AI智能辅助**: 集成OpenAI、Claude、Deepseek等主流AI模型
- 📚 **故事圣经系统**: 完整的角色、场景、大纲管理体系
- 📝 **专业编辑器**: 支持Markdown语法高亮的现代化文本编辑器
- 🌓 **主题系统**: 支持深色/浅色主题自动切换
- 💾 **本地优先**: 数据本地存储，保护隐私安全
- 📤 **多格式导出**: 支持PDF、DOCX、EPUB等多种格式导出

## 🚀 快速开始

### 系统要求

- **Python**: 3.9 或更高版本
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **内存**: 至少 4GB RAM（推荐 8GB+）
- **存储**: 至少 2GB 可用空间

### 安装步骤

#### 1. 克隆项目
```bash
git clone https://github.com/yourusername/bamboofall_cc.git
cd bamboofall_cc
```

#### 2. 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv bamboofall_env

# 激活虚拟环境
# Windows:
bamboofall_env\Scripts\activate
# macOS/Linux:
source bamboofall_env/bin/activate
```

#### 3. 安装依赖

**方案一：自动安装脚本（推荐）**
```bash
# 运行自动安装脚本
python install_env.py
```

**方案二：手动安装**
```bash
# 升级pip
python -m pip install --upgrade pip

# 先安装核心依赖
pip install -r requirements-minimal.txt

# 再安装完整依赖
pip install -r requirements.txt

# 安装项目（开发模式）
pip install -e .
```

**方案三：问题解决**

如果遇到依赖安装问题，请尝试以下解决方案：

```bash
# 1. 问题：qdarktheme版本不存在
# 解决：使用备选主题库
pip install qdarkstyle>=3.0.0  # 替代qdarktheme

# 2. 问题：PyQt6安装失败
# 解决：尝试较旧版本或PyQt5
pip install PyQt6==6.4.0
# 或者
pip install PyQt5>=5.15.0

# 3. 问题：版本冲突
# 解决：强制安装指定版本
pip install --force-reinstall -r requirements-minimal.txt

# 4. 问题：网络连接问题
# 解决：使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 5. 问题：Windows编译错误
# 解决：安装预编译版本
pip install --only-binary=all -r requirements.txt
```

#### 4. 运行应用
```bash
# 方式1: 直接运行主文件
python src/bamboofall/main.py

# 方式2: 使用已安装的命令
bamboofall

# 方式3: 使用模块方式
python -m bamboofall
```

### 配置AI服务

首次启动后，请在设置中配置AI服务API密钥：

1. 点击菜单 "工具" → "设置"
2. 在"AI服务"标签页中输入相应的API密钥
3. 选择默认使用的AI模型

支持的AI服务：
- **OpenAI**: GPT-3.5, GPT-4系列
- **Anthropic**: Claude系列
- **Deepseek**: Deepseek系列
- **本地模型**: 支持Ollama等本地部署

## 📁 项目结构

```
bamboofall_cc/
├── src/
│   └── bamboofall/           # 主应用代码
│       ├── __init__.py
│       ├── main.py           # 应用入口
│       ├── ui/               # 用户界面
│       │   ├── main_window.py
│       │   ├── themes/       # 主题系统
│       │   └── widgets/      # 自定义控件
│       ├── core/             # 核心业务逻辑
│       ├── models/           # 数据模型
│       ├── database/         # 数据库层
│       ├── ai/               # AI集成
│       └── utils/            # 工具函数
├── resources/                # 资源文件
│   ├── icons/               # 图标
│   ├── themes/              # 主题资源
│   └── templates/           # 项目模板
├── tests/                   # 测试文件
├── docs/                    # 文档
│   ├── Python版本-需求文档.md
│   ├── Python版本-技术文档.md
│   ├── Python版本-开发计划.md
│   └── Python版本-代码规范.md
├── requirements.txt         # 生产依赖
├── requirements-dev.txt     # 开发依赖
├── pyproject.toml          # 项目配置
└── README.md               # 项目说明
```

## 🔧 常见问题解决

### 安装问题

**Q1: 出现 "Could not find a version that satisfies the requirement qdarktheme>=2.1.0"错误**

A: 这是版本兼容性问题，请尝试以下解决方案：

```bash
# 方案1：使用备选主题库
pip install qdarkstyle>=3.0.0

# 方案2：安装较旧版本
pip install qdarktheme>=1.0.0

# 方案3：使用精简版依赖
pip install -r requirements-minimal.txt
```

**Q2: PyQt6安装失败**

A: 尝试以下解决方案：

```bash
# Windows系统：安装预编译版本
pip install PyQt6 --only-binary=all

# 或者使用PyQt5作为替代
pip install PyQt5>=5.15.0

# 或者安装较旧版本
pip install PyQt6==6.4.0
```

**Q3: 网络连接超时**

A: 使用国内镜像加速下载：

```bash
# 使用清华镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或使用阿里云镜像
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
```

### 运行问题

**Q4: 应用启动后闪退**

A: 请检查以下项目：

```bash
# 1. 检查Python版本
python --version  # 需要 3.9+

# 2. 检查关键依赖
python -c "import PyQt6.QtWidgets; print('PyQt6 OK')"
python -c "import sqlalchemy; print('SQLAlchemy OK')"

# 3. 查看详细错误信息
python src/bamboofall/main.py
```

**Q5: AI服务无法连接**

A: 请确认：

1. API密钥配置正确
2. 网络连接正常
3. API额度充足
4. 防火墙设置正确

### 性能优化

**提高启动速度：**

```bash
# 关闭不必要的功能
python src/bamboofall/main.py --minimal

# 优化数据库
vacuum  # 在应用中运行
```

**减少内存使用：**

1. 关闭实时预览
2. 减少同时打开的项目数量
3. 定期清理缓存

## 🛠️ 开发指南

### 开发环境设置

1. **安装开发依赖**:
```bash
pip install -r requirements-dev.txt
```

2. **设置pre-commit钩子**:
```bash
pre-commit install
```

3. **运行代码检查**:
```bash
# 代码格式化
black .

# 代码检查
flake8 .

# 类型检查
mypy .

# 运行测试
pytest
```

### 代码规范

项目遵循严格的代码规范，详见 [代码规范文档](docs/Python版本-代码规范和开发标准.md)。

主要规范：
- 使用Black进行代码格式化
- 遵循PEP 8编码规范
- 要求类型注解
- 完整的文档字符串

### 技术栈

- **GUI框架**: PyQt6 6.6+
- **主题系统**: qdarktheme 2.1+
- **数据库**: SQLite + SQLAlchemy 2.0+
- **AI集成**: OpenAI, Anthropic APIs
- **可视化**: matplotlib, plotly
- **文档导出**: reportlab, python-docx, ebooklib

## 📦 打包和分发

### 使用PyInstaller打包

```bash
# 安装打包工具
pip install pyinstaller

# 打包应用（单文件）
pyinstaller --onefile --windowed src/bamboofall/main.py

# 打包应用（目录模式，推荐）
pyinstaller --onedir --windowed src/bamboofall/main.py
```

### 使用cx_Freeze打包

```bash
# 安装cx_Freeze
pip install cx_freeze

# 创建setup.py文件并运行
python setup.py build
```

### 平台特定打包

#### Windows
```bash
# 创建Windows安装包
pip install auto-py-to-exe
auto-py-to-exe
```

#### macOS
```bash
# 创建macOS应用包
pyinstaller --onefile --windowed --icon=resources/icons/app_icon.icns src/bamboofall/main.py
```

#### Linux
```bash
# 创建AppImage
pip install python-appimage
python-appimage build
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_models.py

# 运行带覆盖率的测试
pytest --cov=bamboofall

# 运行GUI测试
pytest tests/test_ui.py -v
```

### 测试类型

- **单元测试**: 测试各个模块的功能
- **集成测试**: 测试模块间的交互
- **UI测试**: 测试用户界面功能
- **性能测试**: 测试应用性能指标

## 📚 文档

详细文档请参考：

- [环境搭建指南](ENVIRONMENT_SETUP.md)
- [产品需求文档](docs/Python版本-笔落App终极需求文档.md)
- [技术开发文档](docs/Python版本-技术开发文档.md)
- [开发计划文档](docs/Python版本-开发计划和任务分解.md)
- [代码规范文档](docs/Python版本-代码规范和开发标准.md)

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解详情。

### 贡献方式

1. **报告Bug**: 在Issues中提交bug报告
2. **功能建议**: 提出新功能的建议
3. **代码贡献**: 提交Pull Request
4. **文档改进**: 完善项目文档
5. **翻译工作**: 帮助翻译界面和文档

### 开发流程

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用 MIT 许可证。详情请查看 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢以下开源项目和服务：

- [PyQt6](https://www.riverbankcomputing.com/software/pyqt/) - 强大的Python GUI框架
- [qdarktheme](https://github.com/5yutan5/qdarktheme) - 优雅的暗黑主题
- [SQLAlchemy](https://www.sqlalchemy.org/) - Python SQL工具包
- [OpenAI](https://openai.com/) - AI API服务
- [Anthropic](https://www.anthropic.com/) - Claude AI模型

## 📞 联系我们

- **项目主页**: https://github.com/yourusername/bamboofall_cc
- **问题反馈**: https://github.com/yourusername/bamboofall_cc/issues
- **讨论区**: https://github.com/yourusername/bamboofall_cc/discussions
- **邮箱**: <EMAIL>

## 🗺️ 路线图

### v1.0.0 (当前开发中)
- [x] 基础项目架构
- [x] 现代化UI框架
- [x] 主题系统
- [x] 数据模型设计
- [ ] 项目管理功能
- [ ] AI服务集成
- [ ] 故事圣经系统
- [ ] 文本编辑器

### v1.1.0 (计划中)
- [ ] 高级AI功能
- [ ] 可视化图表
- [ ] 插件系统
- [ ] 多语言支持

### v1.2.0 (规划中)
- [ ] 云同步功能
- [ ] 协作编辑
- [ ] 移动端支持
- [ ] Web版本

---

<div align="center">

**[⬆ 回到顶部](#笔落app---ai辅助小说创作平台python版本)**

Made with ❤️ by BambooFall Team

</div>