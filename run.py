#!/usr/bin/env python3
"""
笔落App 启动脚本

简化的启动入口，自动处理环境检查和依赖检测
"""

import sys
import os
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 9):
        print("错误: 需要Python 3.9或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True


def check_dependencies():
    """检查核心依赖"""
    required_packages = [
        "PyQt6",
        "sqlalchemy", 
        "pydantic",
        "qdarktheme"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("错误: 缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True


def setup_environment():
    """设置环境变量"""
    # 设置项目根目录
    project_root = Path(__file__).parent
    
    # 添加到Python路径
    src_path = project_root / "src"
    if str(src_path) not in sys.path:
        sys.path.insert(0, str(src_path))


def main():
    """主函数"""
    print("🌸 笔落App - AI辅助小说创作平台")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 检查依赖
    print("检查依赖包...")
    if not check_dependencies():
        return 1
    
    # 设置环境
    setup_environment()
    
    try:
        # 导入并启动应用
        print("启动应用程序...")
        from bamboofall.main import main as app_main
        return app_main()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已正确安装项目依赖")
        return 1
    
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())