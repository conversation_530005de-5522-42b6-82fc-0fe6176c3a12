"""主窗口类

应用程序的主窗口，提供项目管理和导航功能。
"""

from typing import Optional, List, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter, QTabWidget,
    QListWidget, QListWidgetItem, QLabel, QPushButton, QFrame,
    QStackedWidget, QToolButton, QMenu, QFileDialog, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QAction, QIcon, QPixmap
import os
from pathlib import Path

from .base_window import BaseWindow
from ..dashboard.dashboard_widget import StatCard
from ..widgets.modern_widgets import ModernButton
from ..dialogs.new_project_dialog import NewProjectDialog
from ..dialogs.open_project_dialog import OpenProjectDialog
from ...core.project_manager import ProjectManager
from ...utils.config_utils import get_config_manager
from ...exceptions import handle_exceptions, ProjectError


class MainWindow(BaseWindow):
    """主窗口类
    
    功能：
    - 项目管理（新建、打开、最近项目）
    - 仪表板显示
    - 窗口导航
    - 全局设置
    """
    
    # 信号
    project_opened = pyqtSignal(str)  # 项目路径
    project_created = pyqtSignal(str)  # 项目路径
    window_requested = pyqtSignal(str, dict)  # 窗口类型, 参数
    
    def __init__(self, parent=None):
        self.project_manager = ProjectManager()
        self.config_manager = get_config_manager()
        
        # UI组件
        self.central_widget = None
        self.main_splitter = None
        self.sidebar = None
        self.content_area = None
        self.dashboard = None
        self.recent_projects_list = None
        
        # 状态
        self.current_project_path = None
        
        super().__init__(parent)
    
    def setup_window_properties(self):
        """设置窗口属性"""
        self.setWindowTitle("笔落 - AI辅助小说创作平台")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 设置窗口图标（如果有的话）
        # self.setWindowIcon(QIcon("path/to/icon.png"))
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(self.central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建分割器
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(self.main_splitter)
        
        # 创建侧边栏
        self.setup_sidebar()
        
        # 创建内容区域
        self.setup_content_area()
        
        # 设置分割器比例
        self.main_splitter.setSizes([300, 1100])
        self.main_splitter.setCollapsible(0, False)
        self.main_splitter.setCollapsible(1, False)
    
    def setup_sidebar(self):
        """设置侧边栏"""
        self.sidebar = QFrame()
        self.sidebar.setFixedWidth(300)
        self.sidebar.setFrameStyle(QFrame.Shape.StyledPanel)
        
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(16, 16, 16, 16)
        sidebar_layout.setSpacing(16)
        
        # 标题
        title_label = QLabel("项目管理")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                padding: 8px 0;
            }
        """)
        sidebar_layout.addWidget(title_label)
        
        # 快速操作按钮
        self.setup_quick_actions(sidebar_layout)
        
        # 最近项目
        self.setup_recent_projects(sidebar_layout)
        
        # 添加弹性空间
        sidebar_layout.addStretch()
        
        self.main_splitter.addWidget(self.sidebar)
    
    def setup_quick_actions(self, layout):
        """设置快速操作按钮"""
        actions_frame = QFrame()
        actions_layout = QVBoxLayout(actions_frame)
        actions_layout.setSpacing(8)
        
        # 新建项目按钮
        new_project_btn = ModernButton("新建项目")
        new_project_btn.setMinimumHeight(40)
        new_project_btn.clicked.connect(self.on_new_project)
        actions_layout.addWidget(new_project_btn)
        
        # 打开项目按钮
        open_project_btn = ModernButton("打开项目")
        open_project_btn.setMinimumHeight(40)
        open_project_btn.clicked.connect(self.on_open_project)
        actions_layout.addWidget(open_project_btn)
        
        # 导入项目按钮
        import_project_btn = ModernButton("导入项目")
        import_project_btn.setMinimumHeight(40)
        import_project_btn.clicked.connect(self.on_import_project)
        actions_layout.addWidget(import_project_btn)
        
        layout.addWidget(actions_frame)
    
    def setup_recent_projects(self, layout):
        """设置最近项目列表"""
        recent_label = QLabel("最近项目")
        recent_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                padding: 8px 0;
            }
        """)
        layout.addWidget(recent_label)
        
        # 最近项目列表
        self.recent_projects_list = QListWidget()
        self.recent_projects_list.setMaximumHeight(200)
        self.recent_projects_list.itemDoubleClicked.connect(self.on_recent_project_selected)
        
        # 加载最近项目
        self.load_recent_projects()
        
        layout.addWidget(self.recent_projects_list)
    
    def setup_content_area(self):
        """设置内容区域"""
        self.content_area = QStackedWidget()
        
        # 创建仪表板
        self.setup_dashboard()
        
        self.main_splitter.addWidget(self.content_area)
    
    def setup_dashboard(self):
        """设置仪表板"""
        self.dashboard = QWidget()
        dashboard_layout = QVBoxLayout(self.dashboard)
        dashboard_layout.setContentsMargins(24, 24, 24, 24)
        dashboard_layout.setSpacing(24)
        
        # 欢迎标题
        welcome_label = QLabel("欢迎使用笔落")
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2c3e50;
                padding: 16px 0;
            }
        """)
        dashboard_layout.addWidget(welcome_label)
        
        # 统计卡片
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(16)
        
        # 项目统计
        project_count = len(self.get_recent_projects())
        project_card = StatCard("项目数量", str(project_count), "个")
        stats_layout.addWidget(project_card)
        
        # 字数统计（示例）
        word_card = StatCard("总字数", "0", "字")
        stats_layout.addWidget(word_card)
        
        # 章节统计（示例）
        chapter_card = StatCard("总章节", "0", "章")
        stats_layout.addWidget(chapter_card)
        
        # 今日写作（示例）
        today_card = StatCard("今日写作", "0", "字")
        stats_layout.addWidget(today_card)
        
        dashboard_layout.addLayout(stats_layout)
        
        # 快速开始区域
        quick_start_frame = QFrame()
        quick_start_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        quick_start_layout = QVBoxLayout(quick_start_frame)
        quick_start_layout.setContentsMargins(24, 24, 24, 24)
        
        quick_start_label = QLabel("快速开始")
        quick_start_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                padding: 8px 0;
            }
        """)
        quick_start_layout.addWidget(quick_start_label)
        
        # 快速开始按钮
        quick_buttons_layout = QHBoxLayout()
        
        new_novel_btn = ModernButton("创建新小说")
        new_novel_btn.setMinimumHeight(50)
        new_novel_btn.clicked.connect(self.on_new_project)
        quick_buttons_layout.addWidget(new_novel_btn)
        
        open_existing_btn = ModernButton("打开现有项目")
        open_existing_btn.setMinimumHeight(50)
        open_existing_btn.clicked.connect(self.on_open_project)
        quick_buttons_layout.addWidget(open_existing_btn)
        
        template_btn = ModernButton("使用模板")
        template_btn.setMinimumHeight(50)
        template_btn.clicked.connect(self.on_use_template)
        quick_buttons_layout.addWidget(template_btn)
        
        quick_start_layout.addLayout(quick_buttons_layout)
        dashboard_layout.addWidget(quick_start_frame)
        
        # 添加弹性空间
        dashboard_layout.addStretch()
        
        self.content_area.addWidget(self.dashboard)
    
    def setup_tool_bar(self):
        """设置工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        
        # 新建项目
        new_action = QAction("新建", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.on_new_project)
        toolbar.addAction(new_action)
        
        # 打开项目
        open_action = QAction("打开", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.on_open_project)
        toolbar.addAction(open_action)
        
        toolbar.addSeparator()
        
        # 设置
        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.on_settings)
        toolbar.addAction(settings_action)
    
    @handle_exceptions()
    def on_new_project(self):
        """新建项目"""
        dialog = NewProjectDialog(self)
        if dialog.exec() == dialog.DialogCode.Accepted:
            project_data = dialog.get_project_data()
            try:
                project_path = self.project_manager.create_project(
                    project_data['name'],
                    project_data['path'],
                    project_data.get('description', ''),
                    project_data.get('genre', ''),
                    project_data.get('template')
                )
                
                self.current_project_path = project_path
                self.add_to_recent_projects(project_path)
                self.project_created.emit(project_path)
                
                self.show_message("成功", f"项目已创建：{project_path}")
                
                # 打开项目窗口
                self.window_requested.emit("project", {"project_path": project_path})
                
            except ProjectError as e:
                self.show_message("错误", f"创建项目失败：{e}", "error")
    
    @handle_exceptions()
    def on_open_project(self):
        """打开项目"""
        dialog = OpenProjectDialog(self)
        if dialog.exec() == dialog.DialogCode.Accepted:
            project_path = dialog.get_selected_project()
            if project_path:
                self.open_project(project_path)
    
    @handle_exceptions()
    def open_project(self, project_path: str):
        """打开指定项目"""
        try:
            if self.project_manager.load_project(project_path):
                self.current_project_path = project_path
                self.add_to_recent_projects(project_path)
                self.project_opened.emit(project_path)
                
                # 打开项目窗口
                self.window_requested.emit("project", {"project_path": project_path})
            else:
                self.show_message("错误", "无法打开项目", "error")
                
        except ProjectError as e:
            self.show_message("错误", f"打开项目失败：{e}", "error")
    
    def on_import_project(self):
        """导入项目"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入项目", "", "项目文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            # TODO: 实现项目导入逻辑
            self.show_message("提示", "项目导入功能正在开发中")
    
    def on_use_template(self):
        """使用模板"""
        # TODO: 实现模板选择功能
        self.show_message("提示", "模板功能正在开发中")
    
    def on_settings(self):
        """打开设置"""
        self.window_requested.emit("settings", {})
    
    def on_recent_project_selected(self, item: QListWidgetItem):
        """选择最近项目"""
        project_path = item.data(Qt.ItemDataRole.UserRole)
        if project_path and os.path.exists(project_path):
            self.open_project(project_path)
        else:
            self.show_message("错误", "项目文件不存在", "error")
            self.remove_from_recent_projects(project_path)
    
    def get_recent_projects(self) -> List[str]:
        """获取最近项目列表"""
        return self.config_manager.get('recent_projects', [])
    
    def add_to_recent_projects(self, project_path: str):
        """添加到最近项目"""
        recent_projects = self.get_recent_projects()
        
        # 移除已存在的项目（避免重复）
        if project_path in recent_projects:
            recent_projects.remove(project_path)
        
        # 添加到开头
        recent_projects.insert(0, project_path)
        
        # 限制最大数量
        recent_projects = recent_projects[:10]
        
        # 保存配置
        self.config_manager.set('recent_projects', recent_projects)
        
        # 刷新列表
        self.load_recent_projects()
    
    def remove_from_recent_projects(self, project_path: str):
        """从最近项目中移除"""
        recent_projects = self.get_recent_projects()
        if project_path in recent_projects:
            recent_projects.remove(project_path)
            self.config_manager.set('recent_projects', recent_projects)
            self.load_recent_projects()
    
    def load_recent_projects(self):
        """加载最近项目列表"""
        if not self.recent_projects_list:
            return
            
        self.recent_projects_list.clear()
        
        recent_projects = self.get_recent_projects()
        for project_path in recent_projects:
            if os.path.exists(project_path):
                project_name = os.path.basename(project_path)
                item = QListWidgetItem(project_name)
                item.setData(Qt.ItemDataRole.UserRole, project_path)
                item.setToolTip(project_path)
                self.recent_projects_list.addItem(item)
    
    def update_dashboard_stats(self):
        """更新仪表板统计信息"""
        # TODO: 实现统计信息更新
        pass
    
    # 重写基类方法
    def on_new(self):
        """新建操作"""
        self.on_new_project()
    
    def on_open(self):
        """打开操作"""
        self.on_open_project()
    
    def on_save(self):
        """保存操作"""
        # 主窗口没有直接的保存操作
        pass
    
    def cleanup(self):
        """清理资源"""
        # 保存当前状态
        if self.current_project_path:
            self.config_manager.set('last_project', self.current_project_path)