"""
日志配置工具

设置应用程序的日志记录
"""

import logging
import logging.config
import sys
from pathlib import Path
from typing import Optional
from datetime import datetime

from .config_utils import get_config


def get_log_directory() -> Path:
    """获取日志目录"""
    config = get_config()
    if hasattr(config, 'log_directory'):
        log_dir = Path(config.log_directory)
    else:
        # 默认日志目录
        import os
        if os.name == 'nt':  # Windows
            log_dir = Path(os.getenv('APPDATA', '')) / 'BambooFall' / 'logs'
        else:  # macOS/Linux
            log_dir = Path.home() / '.bamboofall' / 'logs'
    
    log_dir.mkdir(parents=True, exist_ok=True)
    return log_dir


def setup_logging(log_level: Optional[str] = None, log_to_file: bool = True):
    """
    设置日志记录
    
    Args:
        log_level: 日志级别，默认从配置文件读取
        log_to_file: 是否记录到文件
    """
    config = get_config()
    if log_level is None:
        log_level = config.log_level
    
    # 日志格式
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 根记录器配置
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper(), logging.INFO))
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器
    if log_to_file:
        log_dir = get_log_directory()
        
        # 主日志文件
        log_file = log_dir / "bamboofall.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        # 错误日志文件
        error_log_file = log_dir / "error.log"
        error_handler = logging.FileHandler(error_log_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        root_logger.addHandler(error_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('openai').setLevel(logging.WARNING)
    logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
    
    # 在调试模式下启用SQLAlchemy详细日志
    if config.debug:
        logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)
    
    # 记录启动信息
    logger = logging.getLogger(__name__)
    logger.info(f"日志系统已初始化，级别: {log_level}")
    if log_to_file:
        logger.info(f"日志文件目录: {log_dir}")


def get_logger(name: str) -> logging.Logger:
    """
    获取命名记录器
    
    Args:
        name: 记录器名称
        
    Returns:
        配置好的记录器
    """
    return logging.getLogger(name)


class LoggerMixin:
    """日志记录混入类"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的记录器"""
        return logging.getLogger(self.__class__.__module__ + '.' + self.__class__.__name__)


def log_exception(logger: logging.Logger, message: str = "发生异常"):
    """
    记录异常信息
    
    Args:
        logger: 记录器
        message: 自定义消息
    """
    logger.exception(message)


def log_performance(func):
    """
    性能日志装饰器
    
    记录函数执行时间
    """
    import functools
    import time
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.debug(f"{func.__name__} 执行时间: {execution_time:.4f}秒")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"{func.__name__} 执行失败 (耗时: {execution_time:.4f}秒): {e}")
            raise
    
    return wrapper


def create_daily_log_file() -> Path:
    """
    创建按日期命名的日志文件
    
    Returns:
        日志文件路径
    """
    log_dir = get_log_directory()
    today = datetime.now().strftime("%Y-%m-%d")
    return log_dir / f"bamboofall-{today}.log"


def cleanup_old_logs(days_to_keep: int = 30):
    """
    清理旧日志文件
    
    Args:
        days_to_keep: 保留天数
    """
    import time
    
    log_dir = get_log_directory()
    if not log_dir.exists():
        return
    
    cutoff_time = time.time() - (days_to_keep * 24 * 60 * 60)
    
    for log_file in log_dir.glob("*.log"):
        if log_file.stat().st_mtime < cutoff_time:
            try:
                log_file.unlink()
                logging.getLogger(__name__).info(f"已删除旧日志文件: {log_file}")
            except Exception as e:
                logging.getLogger(__name__).error(f"删除日志文件失败 {log_file}: {e}")


# 配置默认的日志记录器
_default_logger = logging.getLogger('bamboofall')


def debug(message: str):
    """记录调试信息"""
    _default_logger.debug(message)


def info(message: str):
    """记录信息"""
    _default_logger.info(message)


def warning(message: str):
    """记录警告"""
    _default_logger.warning(message)


def error(message: str):
    """记录错误"""
    _default_logger.error(message)


def critical(message: str):
    """记录严重错误"""
    _default_logger.critical(message)