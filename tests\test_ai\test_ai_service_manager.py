# -*- coding: utf-8 -*-
"""
AI服务管理器测试
"""

import pytest
from unittest.mock import Mock, patch, MagicMock

from bamboofall.ai.ai_service_manager import AIServiceManager
from bamboofall.ai.ai_service_base import (
    AIConfig, AIMessage, MessageRole, AIResponse, AIModelType,
    AIServiceError, APIKeyError
)
from bamboofall.ai.openai_service import OpenAIService
from bamboofall.ai.anthropic_service import AnthropicService


class TestAIServiceManager:
    """AIServiceManager测试类"""
    
    @pytest.fixture
    def mock_config_manager(self):
        """Mock配置管理器"""
        config_manager = Mock()
        config_manager.get.side_effect = lambda key, default=None: {
            "openai_api_key": "test-openai-key",
            "anthropic_api_key": "test-anthropic-key",
            "default_ai_provider": "openai",
            "default_model": "gpt-3.5-turbo"
        }.get(key, default)
        return config_manager
    
    @pytest.fixture
    def service_manager(self, mock_config_manager):
        """AIServiceManager实例fixture"""
        with patch('bamboofall.ai.ai_service_manager.get_config_manager', return_value=mock_config_manager):
            manager = AIServiceManager()
            return manager
    
    def test_init(self, service_manager):
        """测试初始化"""
        # 检查提供商注册
        assert "openai" in service_manager.providers
        assert "anthropic" in service_manager.providers
        
        # 检查提供商信息
        openai_info = service_manager.providers["openai"]
        assert openai_info["display_name"] == "OpenAI"
        assert openai_info["service_class"] == OpenAIService
        assert "gpt-3.5-turbo" in openai_info["default_models"]
        
        anthropic_info = service_manager.providers["anthropic"]
        assert anthropic_info["display_name"] == "Anthropic"
        assert anthropic_info["service_class"] == AnthropicService
        assert "claude-3-sonnet-20240229" in anthropic_info["default_models"]
    
    def test_get_available_providers(self, service_manager):
        """测试获取可用提供商"""
        providers = service_manager.get_available_providers()
        
        assert "openai" in providers
        assert "anthropic" in providers
        assert providers["openai"]["display_name"] == "OpenAI"
        assert providers["anthropic"]["display_name"] == "Anthropic"
    
    def test_get_provider_models(self, service_manager):
        """测试获取提供商模型"""
        # 测试OpenAI模型
        openai_models = service_manager.get_provider_models("openai")
        assert "gpt-3.5-turbo" in openai_models
        assert "gpt-4" in openai_models
        
        # 测试Anthropic模型
        anthropic_models = service_manager.get_provider_models("anthropic")
        assert "claude-3-sonnet-20240229" in anthropic_models
        assert "claude-3-opus-20240229" in anthropic_models
        
        # 测试无效提供商
        invalid_models = service_manager.get_provider_models("invalid")
        assert invalid_models == []
    
    def test_create_service_openai(self, service_manager):
        """测试创建OpenAI服务"""
        service = service_manager.create_service("openai", "gpt-3.5-turbo")
        
        assert isinstance(service, OpenAIService)
        assert service.config.api_key == "test-openai-key"
        assert service.config.model == "gpt-3.5-turbo"
    
    def test_create_service_anthropic(self, service_manager):
        """测试创建Anthropic服务"""
        service = service_manager.create_service("anthropic", "claude-3-sonnet-20240229")
        
        assert isinstance(service, AnthropicService)
        assert service.config.api_key == "test-anthropic-key"
        assert service.config.model == "claude-3-sonnet-20240229"
    
    def test_create_service_with_custom_config(self, service_manager):
        """测试使用自定义配置创建服务"""
        custom_config = AIConfig(
            api_key="custom-key",
            model="gpt-4",
            temperature=0.5,
            max_tokens=2000
        )
        
        service = service_manager.create_service("openai", config=custom_config)
        
        assert isinstance(service, OpenAIService)
        assert service.config.api_key == "custom-key"
        assert service.config.model == "gpt-4"
        assert service.config.temperature == 0.5
        assert service.config.max_tokens == 2000
    
    def test_create_service_invalid_provider(self, service_manager):
        """测试创建无效提供商服务"""
        with pytest.raises(ValueError, match="不支持的AI提供商"):
            service_manager.create_service("invalid_provider", "some-model")
    
    def test_create_service_missing_api_key(self, mock_config_manager):
        """测试缺少API密钥时创建服务"""
        # 设置配置管理器返回空API密钥
        mock_config_manager.get.side_effect = lambda key, default=None: {
            "openai_api_key": "",  # 空API密钥
            "default_ai_provider": "openai",
            "default_model": "gpt-3.5-turbo"
        }.get(key, default)
        
        with patch('bamboofall.ai.ai_service_manager.get_config_manager', return_value=mock_config_manager):
            manager = AIServiceManager()
            
            with pytest.raises(APIKeyError, match="OpenAI API密钥不能为空"):
                manager.create_service("openai", "gpt-3.5-turbo")
    
    def test_get_default_service(self, service_manager):
        """测试获取默认服务"""
        service = service_manager.get_default_service()
        
        assert isinstance(service, OpenAIService)
        assert service.config.model == "gpt-3.5-turbo"
    
    def test_get_default_service_custom_provider(self, mock_config_manager):
        """测试获取自定义默认提供商服务"""
        # 设置Anthropic为默认提供商
        mock_config_manager.get.side_effect = lambda key, default=None: {
            "openai_api_key": "test-openai-key",
            "anthropic_api_key": "test-anthropic-key",
            "default_ai_provider": "anthropic",
            "default_model": "claude-3-sonnet-20240229"
        }.get(key, default)
        
        with patch('bamboofall.ai.ai_service_manager.get_config_manager', return_value=mock_config_manager):
            manager = AIServiceManager()
            service = manager.get_default_service()
            
            assert isinstance(service, AnthropicService)
            assert service.config.model == "claude-3-sonnet-20240229"
    
    async def test_chat_with_default_service(self, service_manager):
        """测试使用默认服务聊天"""
        # Mock默认服务的chat方法
        mock_response = AIResponse(
            content="测试回复",
            model="gpt-3.5-turbo"
        )
        
        with patch.object(service_manager, 'get_default_service') as mock_get_service:
            mock_service = Mock()
            mock_service.chat.return_value = mock_response
            mock_get_service.return_value = mock_service
            
            messages = [AIMessage(role=MessageRole.USER, content="你好")]
            response = await service_manager.chat(messages)
            
            assert response == mock_response
            mock_service.chat.assert_called_once_with(messages)
    
    async def test_chat_with_specific_provider(self, service_manager):
        """测试使用指定提供商聊天"""
        mock_response = AIResponse(
            content="Anthropic回复",
            model="claude-3-sonnet-20240229"
        )
        
        with patch.object(service_manager, 'create_service') as mock_create:
            mock_service = Mock()
            mock_service.chat.return_value = mock_response
            mock_create.return_value = mock_service
            
            messages = [AIMessage(role=MessageRole.USER, content="你好")]
            response = await service_manager.chat(
                messages, 
                provider="anthropic", 
                model="claude-3-sonnet-20240229"
            )
            
            assert response == mock_response
            mock_create.assert_called_once_with("anthropic", "claude-3-sonnet-20240229")
            mock_service.chat.assert_called_once_with(messages)
    
    async def test_stream_chat(self, service_manager):
        """测试流式聊天"""
        async def mock_stream():
            yield "chunk1"
            yield "chunk2"
            yield "chunk3"
        
        with patch.object(service_manager, 'get_default_service') as mock_get_service:
            mock_service = Mock()
            mock_service.stream_chat.return_value = mock_stream()
            mock_get_service.return_value = mock_service
            
            messages = [AIMessage(role=MessageRole.USER, content="你好")]
            chunks = []
            async for chunk in service_manager.stream_chat(messages):
                chunks.append(chunk)
            
            assert chunks == ["chunk1", "chunk2", "chunk3"]
            mock_service.stream_chat.assert_called_once_with(messages)
    
    async def test_get_embedding(self, service_manager):
        """测试获取embedding"""
        mock_embedding = [0.1, 0.2, 0.3, 0.4]
        
        with patch.object(service_manager, 'create_service') as mock_create:
            mock_service = Mock()
            mock_service.get_embedding.return_value = mock_embedding
            mock_create.return_value = mock_service
            
            result = await service_manager.get_embedding("测试文本", provider="openai")
            
            assert result == mock_embedding
            mock_create.assert_called_once_with("openai", None)
            mock_service.get_embedding.assert_called_once_with("测试文本")
    
    async def test_test_connection(self, service_manager):
        """测试连接测试"""
        with patch.object(service_manager, 'create_service') as mock_create:
            mock_service = Mock()
            mock_service.test_connection.return_value = True
            mock_create.return_value = mock_service
            
            result = await service_manager.test_connection("openai", "gpt-3.5-turbo")
            
            assert result is True
            mock_create.assert_called_once_with("openai", "gpt-3.5-turbo")
            mock_service.test_connection.assert_called_once()
    
    def test_get_model_info(self, service_manager):
        """测试获取模型信息"""
        mock_info = {
            "name": "GPT-3.5 Turbo",
            "max_tokens": 4096,
            "context_window": 4096
        }
        
        with patch.object(service_manager, 'create_service') as mock_create:
            mock_service = Mock()
            mock_service.get_model_info.return_value = mock_info
            mock_create.return_value = mock_service
            
            result = service_manager.get_model_info("openai", "gpt-3.5-turbo")
            
            assert result == mock_info
            mock_create.assert_called_once_with("openai", "gpt-3.5-turbo")
            mock_service.get_model_info.assert_called_once_with("gpt-3.5-turbo")
    
    def test_register_provider(self, service_manager):
        """测试注册新提供商"""
        # 创建一个mock服务类
        class MockAIService:
            provider_name = "mock"
            supported_models = ["mock-model-1", "mock-model-2"]
        
        # 注册新提供商
        service_manager.register_provider(
            provider_id="mock",
            display_name="Mock AI",
            service_class=MockAIService,
            default_models=["mock-model-1"]
        )
        
        # 验证注册成功
        assert "mock" in service_manager.providers
        provider_info = service_manager.providers["mock"]
        assert provider_info["display_name"] == "Mock AI"
        assert provider_info["service_class"] == MockAIService
        assert provider_info["default_models"] == ["mock-model-1"]
    
    def test_register_provider_duplicate(self, service_manager):
        """测试注册重复提供商"""
        class MockAIService:
            provider_name = "openai"  # 重复的提供商ID
        
        with pytest.raises(ValueError, match="提供商.*已存在"):
            service_manager.register_provider(
                provider_id="openai",
                display_name="Duplicate OpenAI",
                service_class=MockAIService
            )
    
    def test_error_handling(self, service_manager):
        """测试错误处理"""
        # 测试服务创建错误处理
        with patch.object(service_manager, '_get_api_key', side_effect=Exception("配置错误")):
            with pytest.raises(AIServiceError, match="创建AI服务失败"):
                service_manager.create_service("openai", "gpt-3.5-turbo")
    
    def test_service_caching(self, service_manager):
        """测试服务缓存机制"""
        # 第一次创建服务
        service1 = service_manager.create_service("openai", "gpt-3.5-turbo")
        
        # 第二次创建相同配置的服务（如果实现了缓存）
        service2 = service_manager.create_service("openai", "gpt-3.5-turbo")
        
        # 验证服务实例（根据实际实现可能相同或不同）
        assert isinstance(service1, OpenAIService)
        assert isinstance(service2, OpenAIService)
        assert service1.config.model == service2.config.model