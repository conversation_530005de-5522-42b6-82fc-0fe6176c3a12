"""
场景数据模型

定义小说中的场景信息结构
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import uuid
import json


class SceneType(Enum):
    """场景类型枚举"""
    INDOOR = "indoor"                # 室内
    OUTDOOR = "outdoor"              # 室外
    NATURAL = "natural"              # 自然环境
    URBAN = "urban"                  # 城市
    RURAL = "rural"                  # 乡村
    FANTASY = "fantasy"              # 奇幻
    FUTURISTIC = "futuristic"        # 未来
    HISTORICAL = "historical"        # 历史


class SceneAtmosphere(Enum):
    """场景氛围枚举"""
    PEACEFUL = "peaceful"            # 平静
    TENSE = "tense"                  # 紧张
    ROMANTIC = "romantic"            # 浪漫
    MYSTERIOUS = "mysterious"        # 神秘
    DANGEROUS = "dangerous"          # 危险
    MAGICAL = "magical"              # 魔幻
    MELANCHOLIC = "melancholic"      # 忧郁
    JOYFUL = "joyful"               # 欢乐


class TimeOfDay(Enum):
    """时间段枚举"""
    DAWN = "dawn"                    # 黎明
    MORNING = "morning"              # 上午
    NOON = "noon"                    # 正午
    AFTERNOON = "afternoon"          # 下午
    EVENING = "evening"              # 傍晚
    NIGHT = "night"                  # 夜晚
    MIDNIGHT = "midnight"            # 午夜


class Weather(Enum):
    """天气枚举"""
    SUNNY = "sunny"                  # 晴天
    CLOUDY = "cloudy"                # 多云
    RAINY = "rainy"                  # 雨天
    STORMY = "stormy"                # 暴风雨
    SNOWY = "snowy"                  # 雪天
    FOGGY = "foggy"                  # 雾天
    WINDY = "windy"                  # 风天


@dataclass
class SceneLocation:
    """场景位置信息"""
    name: str                        # 位置名称
    coordinates: Optional[Tuple[float, float]] = None  # 坐标(经度, 纬度)
    address: Optional[str] = None    # 地址
    region: Optional[str] = None     # 区域
    country: Optional[str] = None    # 国家
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "coordinates": list(self.coordinates) if self.coordinates else None,
            "address": self.address,
            "region": self.region,
            "country": self.country,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SceneLocation":
        """从字典创建"""
        coordinates = None
        if data.get("coordinates"):
            coordinates = tuple(data["coordinates"])
        
        return cls(
            name=data.get("name", ""),
            coordinates=coordinates,
            address=data.get("address"),
            region=data.get("region"),
            country=data.get("country"),
        )


@dataclass
class SceneObject:
    """场景物品"""
    name: str                        # 物品名称
    description: str                 # 物品描述
    importance: int = 1              # 重要性(1-10)
    is_interactive: bool = False     # 是否可交互
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "description": self.description,
            "importance": self.importance,
            "is_interactive": self.is_interactive,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SceneObject":
        """从字典创建"""
        return cls(**data)


@dataclass
class Scene:
    """场景模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    project_id: str = ""
    name: str = ""
    
    # 基本信息
    type: SceneType = SceneType.INDOOR
    atmosphere: SceneAtmosphere = SceneAtmosphere.PEACEFUL
    
    # 时间信息
    time_of_day: TimeOfDay = TimeOfDay.MORNING
    weather: Weather = Weather.SUNNY
    season: Optional[str] = None
    
    # 位置信息
    location: Optional[SceneLocation] = None
    
    # 描述信息
    description: str = ""
    visual_description: str = ""       # 视觉描述
    audio_description: str = ""        # 听觉描述
    smell_description: str = ""        # 嗅觉描述
    
    # 物品和设施
    objects: List[SceneObject] = field(default_factory=list)
    
    # 相关角色ID列表
    characters_present: List[str] = field(default_factory=list)
    
    # 事件记录
    events_occurred: List[str] = field(default_factory=list)
    
    # 相关章节ID列表
    related_chapters: List[str] = field(default_factory=list)
    
    # 使用频率
    usage_count: int = 0
    
    # 创建时间
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # 标签
    tags: List[str] = field(default_factory=list)
    
    # 参考图片路径
    reference_images: List[str] = field(default_factory=list)
    
    # 扩展属性
    custom_fields: Dict[str, Any] = field(default_factory=dict)
    
    # 备注
    notes: str = ""
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()
    
    def add_object(self, obj: SceneObject):
        """添加物品"""
        self.objects.append(obj)
        self.update_timestamp()
    
    def remove_object(self, object_name: str):
        """移除物品"""
        self.objects = [obj for obj in self.objects if obj.name != object_name]
        self.update_timestamp()
    
    def add_character(self, character_id: str):
        """添加角色"""
        if character_id not in self.characters_present:
            self.characters_present.append(character_id)
            self.update_timestamp()
    
    def remove_character(self, character_id: str):
        """移除角色"""
        if character_id in self.characters_present:
            self.characters_present.remove(character_id)
            self.update_timestamp()
    
    def add_event(self, event: str):
        """添加事件"""
        self.events_occurred.append(event)
        self.update_timestamp()
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count += 1
        self.update_timestamp()
    
    def get_objects_by_importance(self, min_importance: int = 1) -> List[SceneObject]:
        """根据重要性获取物品"""
        return [obj for obj in self.objects if obj.importance >= min_importance]
    
    def get_interactive_objects(self) -> List[SceneObject]:
        """获取可交互物品"""
        return [obj for obj in self.objects if obj.is_interactive]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "project_id": self.project_id,
            "name": self.name,
            "type": self.type.value,
            "atmosphere": self.atmosphere.value,
            "time_of_day": self.time_of_day.value,
            "weather": self.weather.value,
            "season": self.season,
            "location": self.location.to_dict() if self.location else None,
            "description": self.description,
            "visual_description": self.visual_description,
            "audio_description": self.audio_description,
            "smell_description": self.smell_description,
            "objects": [obj.to_dict() for obj in self.objects],
            "characters_present": self.characters_present,
            "events_occurred": self.events_occurred,
            "related_chapters": self.related_chapters,
            "usage_count": self.usage_count,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "tags": self.tags,
            "reference_images": self.reference_images,
            "custom_fields": self.custom_fields,
            "notes": self.notes,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Scene":
        """从字典创建场景"""
        # 处理枚举类型
        scene_type = SceneType(data.get("type", "indoor"))
        atmosphere = SceneAtmosphere(data.get("atmosphere", "peaceful"))
        time_of_day = TimeOfDay(data.get("time_of_day", "morning"))
        weather = Weather(data.get("weather", "sunny"))
        
        # 处理时间
        created_at = datetime.fromisoformat(data["created_at"]) if data.get("created_at") else datetime.now()
        updated_at = datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else datetime.now()
        
        # 处理位置
        location = None
        if data.get("location"):
            location = SceneLocation.from_dict(data["location"])
        
        # 处理物品
        objects_data = data.get("objects", [])
        objects = [SceneObject.from_dict(obj) for obj in objects_data]
        
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            project_id=data.get("project_id", ""),
            name=data.get("name", ""),
            type=scene_type,
            atmosphere=atmosphere,
            time_of_day=time_of_day,
            weather=weather,
            season=data.get("season"),
            location=location,
            description=data.get("description", ""),
            visual_description=data.get("visual_description", ""),
            audio_description=data.get("audio_description", ""),
            smell_description=data.get("smell_description", ""),
            objects=objects,
            characters_present=data.get("characters_present", []),
            events_occurred=data.get("events_occurred", []),
            related_chapters=data.get("related_chapters", []),
            usage_count=data.get("usage_count", 0),
            created_at=created_at,
            updated_at=updated_at,
            tags=data.get("tags", []),
            reference_images=data.get("reference_images", []),
            custom_fields=data.get("custom_fields", {}),
            notes=data.get("notes", ""),
        )
    
    def __str__(self) -> str:
        return f"Scene(id={self.id}, name={self.name}, type={self.type.value})"