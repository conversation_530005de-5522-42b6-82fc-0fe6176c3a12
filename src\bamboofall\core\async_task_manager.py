# -*- coding: utf-8 -*-
"""
异步任务管理器

提供异步任务执行、任务队列管理、任务调度等功能，提高系统响应性能
"""

import logging
import asyncio
import threading
import time
import uuid
from typing import Dict, List, Any, Optional, Callable, Union, Awaitable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future
from functools import wraps
import weakref
import json
from queue import Queue, PriorityQueue

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 3
    NORMAL = 2
    HIGH = 1
    URGENT = 0


@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    execution_time: Optional[float] = None
    
    @property
    def is_success(self) -> bool:
        return self.status == TaskStatus.COMPLETED
        
    @property
    def is_failed(self) -> bool:
        return self.status in [TaskStatus.FAILED, TaskStatus.TIMEOUT, TaskStatus.CANCELLED]
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            'task_id': self.task_id,
            'status': self.status.value,
            'result': self.result,
            'error': self.error,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'execution_time': self.execution_time
        }


@dataclass
class Task:
    """异步任务"""
    task_id: str
    name: str
    func: Callable
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    priority: TaskPriority = TaskPriority.NORMAL
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 0
    retry_delay: float = 1.0
    created_at: datetime = field(default_factory=datetime.now)
    scheduled_at: Optional[datetime] = None
    depends_on: List[str] = field(default_factory=list)
    callback: Optional[Callable] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __lt__(self, other):
        """用于优先级队列排序"""
        if not isinstance(other, Task):
            return NotImplemented
        return self.priority.value < other.priority.value
        
    def can_execute(self, completed_tasks: set) -> bool:
        """检查任务是否可以执行"""
        # 检查依赖任务是否完成
        for dep_id in self.depends_on:
            if dep_id not in completed_tasks:
                return False
                
        # 检查调度时间
        if self.scheduled_at and datetime.now() < self.scheduled_at:
            return False
            
        return True
        
    def should_retry(self) -> bool:
        """检查是否应该重试"""
        return self.retry_count < self.max_retries
        
    def to_dict(self) -> Dict[str, Any]:
        return {
            'task_id': self.task_id,
            'name': self.name,
            'priority': self.priority.value,
            'timeout': self.timeout,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'retry_delay': self.retry_delay,
            'created_at': self.created_at.isoformat(),
            'scheduled_at': self.scheduled_at.isoformat() if self.scheduled_at else None,
            'depends_on': self.depends_on,
            'metadata': self.metadata
        }


class TaskQueue:
    """任务队列"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self._queue = PriorityQueue(maxsize=max_size)
        self._pending_tasks: Dict[str, Task] = {}
        self._lock = threading.RLock()
        
    def put(self, task: Task) -> bool:
        """添加任务到队列"""
        with self._lock:
            if self._queue.full():
                return False
                
            self._queue.put(task)
            self._pending_tasks[task.task_id] = task
            return True
            
    def get(self, timeout: Optional[float] = None) -> Optional[Task]:
        """从队列获取任务"""
        try:
            task = self._queue.get(timeout=timeout)
            with self._lock:
                self._pending_tasks.pop(task.task_id, None)
            return task
        except:
            return None
            
    def remove(self, task_id: str) -> bool:
        """移除任务"""
        with self._lock:
            task = self._pending_tasks.pop(task_id, None)
            if task:
                # 从优先级队列中移除比较复杂，这里标记为已取消
                return True
            return False
            
    def size(self) -> int:
        """获取队列大小"""
        return self._queue.qsize()
        
    def is_empty(self) -> bool:
        """检查队列是否为空"""
        return self._queue.empty()
        
    def is_full(self) -> bool:
        """检查队列是否已满"""
        return self._queue.full()
        
    def get_pending_tasks(self) -> List[Task]:
        """获取待处理任务列表"""
        with self._lock:
            return list(self._pending_tasks.values())


class TaskWorker:
    """任务工作器"""
    
    def __init__(self, worker_id: str, task_manager: 'AsyncTaskManager'):
        self.worker_id = worker_id
        self.task_manager = task_manager
        self.is_running = False
        self.current_task: Optional[Task] = None
        self.processed_count = 0
        self.error_count = 0
        self.start_time: Optional[datetime] = None
        self._thread: Optional[threading.Thread] = None
        self._stop_event = threading.Event()
        
    def start(self):
        """启动工作器"""
        if self.is_running:
            return
            
        self.is_running = True
        self.start_time = datetime.now()
        self._stop_event.clear()
        self._thread = threading.Thread(target=self._work_loop, daemon=True)
        self._thread.start()
        
        logger.info(f"任务工作器 {self.worker_id} 已启动")
        
    def stop(self, timeout: float = 5.0):
        """停止工作器"""
        if not self.is_running:
            return
            
        self.is_running = False
        self._stop_event.set()
        
        if self._thread:
            self._thread.join(timeout=timeout)
            
        logger.info(f"任务工作器 {self.worker_id} 已停止")
        
    def _work_loop(self):
        """工作循环"""
        while self.is_running and not self._stop_event.is_set():
            try:
                # 获取任务
                task = self.task_manager.task_queue.get(timeout=1.0)
                if not task:
                    continue
                    
                # 检查任务是否可以执行
                if not task.can_execute(self.task_manager.completed_tasks):
                    # 重新放回队列
                    self.task_manager.task_queue.put(task)
                    time.sleep(0.1)
                    continue
                    
                # 执行任务
                self.current_task = task
                self._execute_task(task)
                self.current_task = None
                
            except Exception as e:
                logger.error(f"工作器 {self.worker_id} 执行任务时发生错误: {e}")
                self.error_count += 1
                
    def _execute_task(self, task: Task):
        """执行任务"""
        start_time = datetime.now()
        
        try:
            # 更新任务状态
            self.task_manager._update_task_status(task.task_id, TaskStatus.RUNNING)
            
            # 执行任务函数
            if asyncio.iscoroutinefunction(task.func):
                # 异步函数
                result = asyncio.run(task.func(*task.args, **task.kwargs))
            else:
                # 同步函数
                if task.timeout:
                    # 使用线程池执行带超时的任务
                    with ThreadPoolExecutor(max_workers=1) as executor:
                        future = executor.submit(task.func, *task.args, **task.kwargs)
                        result = future.result(timeout=task.timeout)
                else:
                    result = task.func(*task.args, **task.kwargs)
                    
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # 创建任务结果
            task_result = TaskResult(
                task_id=task.task_id,
                status=TaskStatus.COMPLETED,
                result=result,
                start_time=start_time,
                end_time=end_time,
                execution_time=execution_time
            )
            
            # 更新任务管理器
            self.task_manager._complete_task(task, task_result)
            
            # 执行回调
            if task.callback:
                try:
                    task.callback(task_result)
                except Exception as e:
                    logger.error(f"任务回调执行失败: {e}")
                    
            self.processed_count += 1
            logger.debug(f"任务 {task.task_id} 执行成功，耗时 {execution_time:.2f}s")
            
        except asyncio.TimeoutError:
            self._handle_task_timeout(task, start_time)
        except Exception as e:
            self._handle_task_error(task, start_time, e)
            
    def _handle_task_timeout(self, task: Task, start_time: datetime):
        """处理任务超时"""
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        task_result = TaskResult(
            task_id=task.task_id,
            status=TaskStatus.TIMEOUT,
            error=f"任务执行超时 ({task.timeout}s)",
            start_time=start_time,
            end_time=end_time,
            execution_time=execution_time
        )
        
        self._handle_task_failure(task, task_result)
        
    def _handle_task_error(self, task: Task, start_time: datetime, error: Exception):
        """处理任务错误"""
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        task_result = TaskResult(
            task_id=task.task_id,
            status=TaskStatus.FAILED,
            error=str(error),
            start_time=start_time,
            end_time=end_time,
            execution_time=execution_time
        )
        
        self._handle_task_failure(task, task_result)
        
    def _handle_task_failure(self, task: Task, task_result: TaskResult):
        """处理任务失败"""
        # 检查是否需要重试
        if task.should_retry():
            task.retry_count += 1
            task.scheduled_at = datetime.now() + timedelta(seconds=task.retry_delay)
            
            # 重新添加到队列
            self.task_manager.task_queue.put(task)
            logger.info(f"任务 {task.task_id} 将在 {task.retry_delay}s 后重试 (第 {task.retry_count}/{task.max_retries} 次)")
        else:
            # 任务最终失败
            self.task_manager._complete_task(task, task_result)
            
            # 执行回调
            if task.callback:
                try:
                    task.callback(task_result)
                except Exception as e:
                    logger.error(f"任务回调执行失败: {e}")
                    
        self.error_count += 1
        logger.error(f"任务 {task.task_id} 执行失败: {task_result.error}")
        
    def get_stats(self) -> Dict[str, Any]:
        """获取工作器统计信息"""
        uptime = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        
        return {
            'worker_id': self.worker_id,
            'is_running': self.is_running,
            'processed_count': self.processed_count,
            'error_count': self.error_count,
            'success_rate': (self.processed_count - self.error_count) / max(self.processed_count, 1),
            'uptime_seconds': uptime,
            'current_task': self.current_task.task_id if self.current_task else None
        }


class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self, max_workers: int = 4, queue_size: int = 1000):
        self.max_workers = max_workers
        self.task_queue = TaskQueue(queue_size)
        self.workers: Dict[str, TaskWorker] = {}
        self.tasks: Dict[str, Task] = {}
        self.task_results: Dict[str, TaskResult] = {}
        self.completed_tasks: set = set()
        self._lock = threading.RLock()
        self._scheduler_thread: Optional[threading.Thread] = None
        self._stop_scheduler = threading.Event()
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'cancelled_tasks': 0
        }
        
    def start(self):
        """启动任务管理器"""
        # 创建并启动工作器
        for i in range(self.max_workers):
            worker_id = f"worker_{i}"
            worker = TaskWorker(worker_id, self)
            self.workers[worker_id] = worker
            worker.start()
            
        # 启动调度器
        self._start_scheduler()
        
        logger.info(f"异步任务管理器已启动，工作器数量: {self.max_workers}")
        
    def stop(self, timeout: float = 10.0):
        """停止任务管理器"""
        # 停止调度器
        self._stop_scheduler.set()
        if self._scheduler_thread:
            self._scheduler_thread.join(timeout=5)
            
        # 停止所有工作器
        for worker in self.workers.values():
            worker.stop(timeout=timeout / len(self.workers))
            
        logger.info("异步任务管理器已停止")
        
    def _start_scheduler(self):
        """启动调度器"""
        if self._scheduler_thread and self._scheduler_thread.is_alive():
            return
            
        self._stop_scheduler.clear()
        self._scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self._scheduler_thread.start()
        
    def _scheduler_loop(self):
        """调度器循环"""
        while not self._stop_scheduler.wait(1.0):
            try:
                self._process_scheduled_tasks()
                self._cleanup_old_results()
            except Exception as e:
                logger.error(f"调度器循环错误: {e}")
                
    def _process_scheduled_tasks(self):
        """处理调度任务"""
        current_time = datetime.now()
        
        with self._lock:
            scheduled_tasks = []
            for task in self.tasks.values():
                if (task.scheduled_at and 
                    task.scheduled_at <= current_time and 
                    task.task_id not in self.completed_tasks):
                    scheduled_tasks.append(task)
                    
            # 将到期的调度任务添加到队列
            for task in scheduled_tasks:
                if task.can_execute(self.completed_tasks):
                    self.task_queue.put(task)
                    
    def _cleanup_old_results(self, max_age_hours: int = 24):
        """清理旧的任务结果"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        with self._lock:
            old_results = []
            for task_id, result in self.task_results.items():
                if result.end_time and result.end_time < cutoff_time:
                    old_results.append(task_id)
                    
            for task_id in old_results:
                self.task_results.pop(task_id, None)
                self.tasks.pop(task_id, None)
                
            if old_results:
                logger.info(f"清理了 {len(old_results)} 个旧任务结果")
                
    def submit_task(self, 
                   func: Callable, 
                   *args, 
                   name: str = None,
                   priority: TaskPriority = TaskPriority.NORMAL,
                   timeout: Optional[float] = None,
                   max_retries: int = 0,
                   retry_delay: float = 1.0,
                   depends_on: List[str] = None,
                   callback: Optional[Callable] = None,
                   scheduled_at: Optional[datetime] = None,
                   metadata: Dict[str, Any] = None,
                   **kwargs) -> str:
        """提交任务"""
        task_id = str(uuid.uuid4())
        
        task = Task(
            task_id=task_id,
            name=name or func.__name__,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            timeout=timeout,
            max_retries=max_retries,
            retry_delay=retry_delay,
            depends_on=depends_on or [],
            callback=callback,
            scheduled_at=scheduled_at,
            metadata=metadata or {}
        )
        
        with self._lock:
            self.tasks[task_id] = task
            self.stats['total_tasks'] += 1
            
        # 如果没有调度时间或已到时间，直接添加到队列
        if not scheduled_at or scheduled_at <= datetime.now():
            if not self.task_queue.put(task):
                raise RuntimeError("任务队列已满")
                
        logger.debug(f"任务 {task_id} ({task.name}) 已提交")
        return task_id
        
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
                
            # 如果任务还在队列中，尝试移除
            if self.task_queue.remove(task_id):
                task_result = TaskResult(
                    task_id=task_id,
                    status=TaskStatus.CANCELLED,
                    error="任务被取消"
                )
                
                self._complete_task(task, task_result)
                logger.info(f"任务 {task_id} 已取消")
                return True
                
            return False
            
    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """获取任务结果"""
        return self.task_results.get(task_id)
        
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        result = self.get_task_result(task_id)
        if result:
            return result.status
            
        # 检查任务是否还在队列中
        task = self.tasks.get(task_id)
        if task:
            return TaskStatus.PENDING
            
        return None
        
    def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> Optional[TaskResult]:
        """等待任务完成"""
        start_time = time.time()
        
        while True:
            result = self.get_task_result(task_id)
            if result:
                return result
                
            if timeout and (time.time() - start_time) > timeout:
                return None
                
            time.sleep(0.1)
            
    def _update_task_status(self, task_id: str, status: TaskStatus):
        """更新任务状态"""
        # 这里可以添加状态更新逻辑
        pass
        
    def _complete_task(self, task: Task, result: TaskResult):
        """完成任务"""
        with self._lock:
            self.task_results[task.task_id] = result
            self.completed_tasks.add(task.task_id)
            
            # 更新统计信息
            if result.status == TaskStatus.COMPLETED:
                self.stats['completed_tasks'] += 1
            elif result.status == TaskStatus.FAILED:
                self.stats['failed_tasks'] += 1
            elif result.status == TaskStatus.CANCELLED:
                self.stats['cancelled_tasks'] += 1
                
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            worker_stats = [worker.get_stats() for worker in self.workers.values()]
            
            return {
                'task_stats': self.stats.copy(),
                'queue_size': self.task_queue.size(),
                'queue_full': self.task_queue.is_full(),
                'active_workers': sum(1 for w in self.workers.values() if w.is_running),
                'total_workers': len(self.workers),
                'worker_stats': worker_stats,
                'pending_tasks': len([t for t in self.tasks.values() 
                                    if t.task_id not in self.completed_tasks])
            }
            
    def get_pending_tasks(self) -> List[Dict[str, Any]]:
        """获取待处理任务列表"""
        with self._lock:
            pending = []
            for task in self.tasks.values():
                if task.task_id not in self.completed_tasks:
                    pending.append(task.to_dict())
            return pending
            
    def get_completed_tasks(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取已完成任务列表"""
        with self._lock:
            completed = []
            for task_id in list(self.completed_tasks)[-limit:]:
                result = self.task_results.get(task_id)
                if result:
                    completed.append(result.to_dict())
            return completed


def async_task(priority: TaskPriority = TaskPriority.NORMAL,
              timeout: Optional[float] = None,
              max_retries: int = 0,
              retry_delay: float = 1.0):
    """异步任务装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            manager = get_task_manager()
            return manager.submit_task(
                func, *args,
                name=func.__name__,
                priority=priority,
                timeout=timeout,
                max_retries=max_retries,
                retry_delay=retry_delay,
                **kwargs
            )
        return wrapper
    return decorator


def background_task(func: Callable) -> Callable:
    """后台任务装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        manager = get_task_manager()
        return manager.submit_task(
            func, *args,
            name=f"background_{func.__name__}",
            priority=TaskPriority.LOW,
            **kwargs
        )
    return wrapper


def scheduled_task(scheduled_at: datetime):
    """调度任务装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            manager = get_task_manager()
            return manager.submit_task(
                func, *args,
                name=f"scheduled_{func.__name__}",
                scheduled_at=scheduled_at,
                **kwargs
            )
        return wrapper
    return decorator


# 全局任务管理器
_task_manager: Optional[AsyncTaskManager] = None


def get_task_manager() -> AsyncTaskManager:
    """获取任务管理器实例"""
    global _task_manager
    if _task_manager is None:
        _task_manager = AsyncTaskManager()
    return _task_manager


def initialize_task_manager(max_workers: int = 4, queue_size: int = 1000, auto_start: bool = True):
    """初始化任务管理器"""
    global _task_manager
    
    _task_manager = AsyncTaskManager(max_workers, queue_size)
    
    if auto_start:
        _task_manager.start()
        
    logger.info(f"异步任务管理器初始化完成，工作器数量: {max_workers}")


def shutdown_task_manager():
    """关闭任务管理器"""
    global _task_manager
    
    if _task_manager:
        _task_manager.stop()
        _task_manager = None
        
    logger.info("异步任务管理器已关闭")