"""
现代化文本编辑器核心组件

提供专业的文本编辑功能，支持Markdown语法高亮
"""

import re
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPlainTextEdit, QFrame, QLabel
)
from PyQt6.QtWidgets import QTextEdit
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QRect
from PyQt6.QtGui import (
    QFont, QFontMetrics, QTextCursor, QTextCharFormat, QColor,
    QSyntaxHighlighter, QTextDocument, QPainter
)
from typing import Optional
import logging

from ..themes.theme_manager import get_theme_manager
from ..widgets.modern_widgets import StatusIndicator
from ...utils.logger import LoggerMixin

logger = logging.getLogger(__name__)


class LineNumberArea(QWidget):
    """行号区域组件"""
    
    def __init__(self, editor):
        super().__init__(editor)
        self.editor = editor
        self.theme_manager = get_theme_manager()
        self.theme_manager.theme_changed.connect(self.update)
    
    def sizeHint(self):
        return self.editor.line_number_area_width(), 0
    
    def paintEvent(self, event):
        self.editor.line_number_area_paint_event(event)


class MarkdownHighlighter(QSyntaxHighlighter):
    """Markdown语法高亮器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.setup_highlighting_rules()
        self.theme_manager.theme_changed.connect(self.setup_highlighting_rules)
    
    def setup_highlighting_rules(self):
        """设置高亮规则"""
        self.highlighting_rules = []
        colors = self.theme_manager.current_colors
        
        # 标题格式
        title_format = QTextCharFormat()
        title_format.setForeground(QColor(colors['primary']))
        title_format.setFontWeight(QFont.Weight.Bold)
        
        # H1-H6标题
        for i in range(1, 7):
            pattern = f"^{'#' * i}\\s+.*$"
            self.highlighting_rules.append((
                re.compile(pattern, re.MULTILINE),
                title_format
            ))
        
        # 加粗文本 **text**
        bold_format = QTextCharFormat()
        bold_format.setForeground(QColor(colors['text_primary']))
        bold_format.setFontWeight(QFont.Weight.Bold)
        self.highlighting_rules.append((
            re.compile(r'\*\*([^*]+)\*\*'),
            bold_format
        ))
        
        # 斜体文本 *text*
        italic_format = QTextCharFormat()
        italic_format.setForeground(QColor(colors['text_primary']))
        italic_format.setFontItalic(True)
        self.highlighting_rules.append((
            re.compile(r'\*([^*]+)\*'),
            italic_format
        ))
        
        # 代码块 ```code```
        code_block_format = QTextCharFormat()
        code_block_format.setBackground(QColor(colors['surface_hover']))
        code_block_format.setForeground(QColor(colors['accent']))
        font = QFont("Consolas, Monaco, monospace")
        code_block_format.setFont(font)
        self.highlighting_rules.append((
            re.compile(r'```[^`]*```'),
            code_block_format
        ))
        
        # 行内代码 `code`
        inline_code_format = QTextCharFormat()
        inline_code_format.setBackground(QColor(colors['surface_hover']))
        inline_code_format.setForeground(QColor(colors['accent']))
        inline_code_format.setFont(QFont("Consolas, Monaco, monospace"))
        self.highlighting_rules.append((
            re.compile(r'`([^`]+)`'),
            inline_code_format
        ))
        
        # 链接 [text](url)
        link_format = QTextCharFormat()
        link_format.setForeground(QColor(colors['primary']))
        link_format.setUnderlineStyle(QTextCharFormat.UnderlineStyle.SingleUnderline)
        self.highlighting_rules.append((
            re.compile(r'\[([^\]]+)\]\([^)]+\)'),
            link_format
        ))
        
        # 引用 > text
        quote_format = QTextCharFormat()
        quote_format.setForeground(QColor(colors['text_secondary']))
        quote_format.setFontItalic(True)
        self.highlighting_rules.append((
            re.compile(r'^>.*$', re.MULTILINE),
            quote_format
        ))
        
        # 列表项 - item 或 * item 或 + item
        list_format = QTextCharFormat()
        list_format.setForeground(QColor(colors['primary']))
        list_format.setFontWeight(QFont.Weight.Bold)
        self.highlighting_rules.append((
            re.compile(r'^\s*[\-\*\+]\s+', re.MULTILINE),
            list_format
        ))
        
        # 有序列表 1. item
        ordered_list_format = QTextCharFormat()
        ordered_list_format.setForeground(QColor(colors['primary']))
        ordered_list_format.setFontWeight(QFont.Weight.Bold)
        self.highlighting_rules.append((
            re.compile(r'^\s*\d+\.\s+', re.MULTILINE),
            ordered_list_format
        ))
        
        # 分割线 ---
        hr_format = QTextCharFormat()
        hr_format.setForeground(QColor(colors['border']))
        hr_format.setFontWeight(QFont.Weight.Bold)
        self.highlighting_rules.append((
            re.compile(r'^---+$', re.MULTILINE),
            hr_format
        ))
        
        # 删除线 ~~text~~
        strikethrough_format = QTextCharFormat()
        strikethrough_format.setForeground(QColor(colors['text_secondary']))
        strikethrough_format.setFontStrikeOut(True)
        self.highlighting_rules.append((
            re.compile(r'~~([^~]+)~~'),
            strikethrough_format
        ))
    
    def highlightBlock(self, text):
        """高亮文本块"""
        for pattern, format_obj in self.highlighting_rules:
            for match in pattern.finditer(text):
                start = match.start()
                length = match.end() - start
                self.setFormat(start, length, format_obj)


class ModernTextEditor(QPlainTextEdit, LoggerMixin):
    """现代化文本编辑器"""
    
    content_changed = pyqtSignal()
    cursor_position_changed = pyqtSignal(int)
    save_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.show_line_numbers = True
        
        # 行号区域
        self.line_number_area = LineNumberArea(self)
        
        # 语法高亮器
        self.highlighter = MarkdownHighlighter(self.document())
        
        # 自动保存定时器
        self.auto_save_timer = QTimer()
        self.auto_save_timer.setSingleShot(True)
        self.auto_save_timer.timeout.connect(self.auto_save)
        
        self.setup_editor()
        self.setup_style()
        self.setup_signals()
        
        self.logger.info("现代化文本编辑器初始化完成")
    
    def setup_editor(self):
        """设置编辑器"""
        font = QFont(self.theme_manager.get_font("editor"))
        font.setPointSize(14)
        self.setFont(font)
        
        # 设置制表符宽度
        fm = QFontMetrics(font)
        self.setTabStopDistance(fm.horizontalAdvance('    '))
        
        self.setLineWrapMode(QPlainTextEdit.LineWrapMode.WidgetWidth)
        self.setCursorWidth(2)
        
        self.update_line_number_area_width(0)
        self.highlight_current_line()
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        self.setStyleSheet(f"""
            ModernTextEditor {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 8px;
                color: {colors['text_primary']};
                selection-background-color: {colors['primary']};
                selection-color: white;
                padding: 16px;
            }}
            
            ModernTextEditor:focus {{
                border-color: {colors['primary']};
                border-width: 2px;
            }}
        """)
    
    def setup_signals(self):
        """设置信号连接"""
        self.document().contentsChanged.connect(self.on_content_changed)
        self.cursorPositionChanged.connect(self.on_cursor_position_changed)
        self.document().blockCountChanged.connect(self.update_line_number_area_width)
        self.updateRequest.connect(self.update_line_number_area)
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def line_number_area_width(self):
        """计算行号区域宽度"""
        if not self.show_line_numbers:
            return 0
        
        digits = len(str(max(1, self.document().blockCount())))
        space = 3 + self.fontMetrics().horizontalAdvance('9') * digits
        return space
    
    def update_line_number_area_width(self, new_block_count):
        """更新行号区域宽度"""
        self.setViewportMargins(self.line_number_area_width(), 0, 0, 0)
    
    def update_line_number_area(self, rect, dy):
        """更新行号区域"""
        if dy:
            self.line_number_area.scroll(0, dy)
        else:
            self.line_number_area.update(0, rect.y(), self.line_number_area.width(), rect.height())
        
        if rect.contains(self.viewport().rect()):
            self.update_line_number_area_width(0)
    
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        cr = self.contentsRect()
        self.line_number_area.setGeometry(
            QRect(cr.left(), cr.top(), self.line_number_area_width(), cr.height())
        )
    
    def line_number_area_paint_event(self, event):
        """绘制行号区域"""
        if not self.show_line_numbers:
            return
        
        colors = self.theme_manager.current_colors
        painter = QPainter(self.line_number_area)
        painter.fillRect(event.rect(), QColor(colors['background']))
        
        block = self.firstVisibleBlock()
        block_number = block.blockNumber()
        top = self.blockBoundingGeometry(block).translated(self.contentOffset()).top()
        bottom = top + self.blockBoundingRect(block).height()
        
        font = self.font()
        font.setPointSize(font.pointSize() - 1)
        painter.setFont(font)
        painter.setPen(QColor(colors['text_disabled']))
        
        while block.isValid() and (top <= event.rect().bottom()):
            if block.isVisible() and (bottom >= event.rect().top()):
                number = str(block_number + 1)
                painter.drawText(
                    0, int(top), self.line_number_area.width() - 3, 
                    self.fontMetrics().height(), Qt.AlignmentFlag.AlignRight, number
                )
            
            block = block.next()
            top = bottom
            bottom = top + self.blockBoundingRect(block).height()
            block_number += 1
    
    def highlight_current_line(self):
        """高亮当前行"""
        colors = self.theme_manager.current_colors
        extra_selections = []
        
        if not self.isReadOnly():
            selection = QTextEdit.ExtraSelection()
            line_color = QColor(colors['surface_hover'])
            line_color.setAlpha(50)
            
            selection.format.setBackground(line_color)
            selection.format.setProperty(QTextCharFormat.Property.FullWidthSelection, True)
            selection.cursor = self.textCursor()
            selection.cursor.clearSelection()
            
            extra_selections.append(selection)
        
        self.setExtraSelections(extra_selections)
    
    def on_content_changed(self):
        """内容改变处理"""
        self.content_changed.emit()
        self.auto_save_timer.start(30000)  # 30秒后自动保存
    
    def on_cursor_position_changed(self):
        """光标位置改变处理"""
        self.highlight_current_line()
        cursor_position = self.textCursor().position()
        self.cursor_position_changed.emit(cursor_position)
    
    def auto_save(self):
        """自动保存"""
        self.save_requested.emit()
    
    def keyPressEvent(self, event):
        """键盘事件处理"""
        # Ctrl+S 保存
        if event.modifiers() == Qt.KeyboardModifier.ControlModifier and event.key() == Qt.Key.Key_S:
            self.save_requested.emit()
            return
        
        # Ctrl+B 加粗
        if event.modifiers() == Qt.KeyboardModifier.ControlModifier and event.key() == Qt.Key.Key_B:
            self.format_bold()
            return
        
        # Ctrl+I 斜体
        if event.modifiers() == Qt.KeyboardModifier.ControlModifier and event.key() == Qt.Key.Key_I:
            self.format_italic()
            return
        
        # Ctrl+K 插入链接
        if event.modifiers() == Qt.KeyboardModifier.ControlModifier and event.key() == Qt.Key.Key_K:
            self.insert_link()
            return
        
        # Ctrl+Shift+I 插入图片
        if (event.modifiers() == (Qt.KeyboardModifier.ControlModifier | Qt.KeyboardModifier.ShiftModifier) 
            and event.key() == Qt.Key.Key_I):
            self.insert_image()
            return
        
        # Ctrl+1-6 标题级别
        if event.modifiers() == Qt.KeyboardModifier.ControlModifier:
            if event.key() >= Qt.Key.Key_1 and event.key() <= Qt.Key.Key_6:
                level = event.key() - Qt.Key.Key_0
                self.format_header(level)
                return
        
        # Tab 缩进
        if event.key() == Qt.Key.Key_Tab:
            cursor = self.textCursor()
            cursor.insertText('    ')  # 4个空格
            return
        
        # Shift+Tab 反缩进
        if event.modifiers() == Qt.KeyboardModifier.ShiftModifier and event.key() == Qt.Key.Key_Tab:
            self.unindent_text()
            return
        
        # Enter 智能换行
        if event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
            self.smart_enter()
            return
        
        super().keyPressEvent(event)
    
    def format_bold(self):
        """加粗格式化"""
        cursor = self.textCursor()
        selected_text = cursor.selectedText()
        
        if selected_text:
            if selected_text.startswith('**') and selected_text.endswith('**'):
                new_text = selected_text[2:-2]
            else:
                new_text = f"**{selected_text}**"
            cursor.insertText(new_text)
        else:
            cursor.insertText("****")
            cursor.movePosition(QTextCursor.MoveOperation.Left, QTextCursor.MoveMode.MoveAnchor, 2)
            self.setTextCursor(cursor)
    
    def format_italic(self):
        """斜体格式化"""
        cursor = self.textCursor()
        selected_text = cursor.selectedText()
        
        if selected_text:
            if (selected_text.startswith('*') and selected_text.endswith('*') 
                and not selected_text.startswith('**')):
                new_text = selected_text[1:-1]
            else:
                new_text = f"*{selected_text}*"
            cursor.insertText(new_text)
        else:
            cursor.insertText("**")
            cursor.movePosition(QTextCursor.MoveOperation.Left, QTextCursor.MoveMode.MoveAnchor, 1)
            self.setTextCursor(cursor)
    
    def format_header(self, level: int):
        """标题格式化"""
        cursor = self.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.StartOfLine)
        cursor.movePosition(QTextCursor.MoveOperation.EndOfLine, QTextCursor.MoveMode.KeepAnchor)
        
        line_text = cursor.selectedText()
        clean_text = line_text.lstrip('#').strip()
        
        if level > 0:
            new_text = '#' * level + ' ' + clean_text
        else:
            new_text = clean_text
        
        cursor.insertText(new_text)
    
    def insert_link(self):
        """插入链接"""
        cursor = self.textCursor()
        selected_text = cursor.selectedText()
        
        if selected_text:
            link_text = f"[{selected_text}](url)"
        else:
            link_text = "[链接文本](url)"
        
        cursor.insertText(link_text)
        
        # 选中URL部分
        cursor.movePosition(QTextCursor.MoveOperation.Left, QTextCursor.MoveMode.MoveAnchor, 4)
        cursor.movePosition(QTextCursor.MoveOperation.Left, QTextCursor.MoveMode.KeepAnchor, 3)
        self.setTextCursor(cursor)
    
    def insert_image(self):
        """插入图片"""
        cursor = self.textCursor()
        selected_text = cursor.selectedText()
        
        if selected_text:
            image_text = f"![{selected_text}](image_url)"
        else:
            image_text = "![图片描述](image_url)"
        
        cursor.insertText(image_text)
        
        # 选中URL部分
        cursor.movePosition(QTextCursor.MoveOperation.Left, QTextCursor.MoveMode.MoveAnchor, 1)
        cursor.movePosition(QTextCursor.MoveOperation.Left, QTextCursor.MoveMode.KeepAnchor, 9)
        self.setTextCursor(cursor)
    
    def unindent_text(self):
        """反缩进"""
        cursor = self.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.StartOfLine)
        cursor.movePosition(QTextCursor.MoveOperation.NextCharacter, QTextCursor.MoveMode.KeepAnchor, 4)
        
        if cursor.selectedText() == '    ':
            cursor.removeSelectedText()
    
    def smart_enter(self):
        """智能换行"""
        cursor = self.textCursor()
        
        # 获取当前行
        cursor.movePosition(QTextCursor.MoveOperation.StartOfLine)
        cursor.movePosition(QTextCursor.MoveOperation.EndOfLine, QTextCursor.MoveMode.KeepAnchor)
        current_line = cursor.selectedText()
        
        # 检查是否是列表项
        list_match = re.match(r'^(\s*)([\-\*\+]|\d+\.)\s+', current_line)
        if list_match:
            indent = list_match.group(1)
            marker = list_match.group(2)
            
            # 如果列表项为空，删除标记
            if current_line.strip() == marker:
                cursor.movePosition(QTextCursor.MoveOperation.StartOfLine)
                cursor.movePosition(QTextCursor.MoveOperation.EndOfLine, QTextCursor.MoveMode.KeepAnchor)
                cursor.removeSelectedText()
                cursor.insertText('\n')
            else:
                # 继续列表
                cursor.movePosition(QTextCursor.MoveOperation.EndOfLine)
                if marker.isdigit():
                    next_marker = str(int(marker.rstrip('.')) + 1) + '.'
                else:
                    next_marker = marker
                cursor.insertText(f'\n{indent}{next_marker} ')
        else:
            # 普通换行
            cursor.movePosition(QTextCursor.MoveOperation.EndOfLine)
            cursor.insertText('\n')
    
    def on_theme_changed(self):
        """主题变化处理"""
        self.setup_style()
        self.highlighter.setup_highlighting_rules()
        self.highlight_current_line()


class WriteStatsWidget(QFrame):
    """写作统计组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.setup_ui()
        self.setup_style()
        self.theme_manager.theme_changed.connect(self.setup_style)
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 6, 12, 6)
        layout.setSpacing(20)
        
        self.char_label = QLabel("字符: 0")
        layout.addWidget(self.char_label)
        
        self.word_label = QLabel("字数: 0")
        layout.addWidget(self.word_label)
        
        self.line_label = QLabel("行数: 0")
        layout.addWidget(self.line_label)
        
        layout.addStretch()
        
        self.cursor_label = QLabel("行 1, 列 1")
        layout.addWidget(self.cursor_label)
        
        self.status_indicator = StatusIndicator("idle")
        layout.addWidget(self.status_indicator)
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        self.setStyleSheet(f"""
            WriteStatsWidget {{
                background-color: {colors['surface']};
                border-top: 1px solid {colors['border']};
                color: {colors['text_secondary']};
            }}
            
            QLabel {{
                font-size: 12px;
                color: {colors['text_secondary']};
                padding: 2px;
            }}
        """)
    
    def update_stats(self, text: str, cursor_position: int = 0):
        """更新统计信息"""
        char_count = len(text)
        word_count = len(text.split()) if text.strip() else 0
        line_count = text.count('\n') + 1 if text else 1
        
        # 计算光标位置
        lines_before_cursor = text[:cursor_position].count('\n')
        line_start = text.rfind('\n', 0, cursor_position) + 1
        column = cursor_position - line_start + 1
        
        self.char_label.setText(f"字符: {char_count:,}")
        self.word_label.setText(f"字数: {word_count:,}")
        self.line_label.setText(f"行数: {line_count}")
        self.cursor_label.setText(f"行 {lines_before_cursor + 1}, 列 {column}")