# -*- coding: utf-8 -*-
"""
章节模型测试
"""

import pytest
from datetime import datetime
from bamboofall.models.chapter import Chapter


class TestChapter:
    """Chapter模型测试类"""
    
    def test_init_with_all_fields(self):
        """测试使用所有字段初始化"""
        chapter = Chapter(
            id="chapter-001",
            project_id="project-001",
            title="第一章：开始",
            content="这是第一章的内容...",
            order=1,
            word_count=1500,
            status="draft",
            notes="这是第一章的笔记",
            tags=["开头", "介绍"],
            outline="章节大纲内容",
            characters=["张三", "李四"],
            locations=["村庄", "森林"],
            created_at="2024-01-01T00:00:00",
            updated_at="2024-01-02T00:00:00"
        )
        
        assert chapter.id == "chapter-001"
        assert chapter.project_id == "project-001"
        assert chapter.title == "第一章：开始"
        assert chapter.content == "这是第一章的内容..."
        assert chapter.order == 1
        assert chapter.word_count == 1500
        assert chapter.status == "draft"
        assert chapter.notes == "这是第一章的笔记"
        assert chapter.tags == ["开头", "介绍"]
        assert chapter.outline == "章节大纲内容"
        assert chapter.characters == ["张三", "李四"]
        assert chapter.locations == ["村庄", "森林"]
        assert chapter.created_at == "2024-01-01T00:00:00"
        assert chapter.updated_at == "2024-01-02T00:00:00"
    
    def test_init_with_minimal_fields(self):
        """测试使用最少字段初始化"""
        chapter = Chapter(
            id="chapter-minimal",
            project_id="project-001",
            title="最小章节"
        )
        
        assert chapter.id == "chapter-minimal"
        assert chapter.project_id == "project-001"
        assert chapter.title == "最小章节"
        assert chapter.content == ""
        assert chapter.order == 0
        assert chapter.word_count == 0
        assert chapter.status == "draft"
        assert chapter.notes == ""
        assert chapter.tags == []
        assert chapter.outline == ""
        assert chapter.characters == []
        assert chapter.locations == []
        assert chapter.created_at is not None
        assert chapter.updated_at is not None
    
    def test_auto_generated_timestamps(self):
        """测试自动生成的时间戳"""
        chapter = Chapter(
            id="timestamp-test",
            project_id="project-001",
            title="时间戳测试"
        )
        
        # 验证时间戳格式
        assert isinstance(chapter.created_at, str)
        assert isinstance(chapter.updated_at, str)
        
        # 验证时间戳可以解析
        created_dt = datetime.fromisoformat(chapter.created_at.replace('Z', '+00:00'))
        updated_dt = datetime.fromisoformat(chapter.updated_at.replace('Z', '+00:00'))
        
        assert isinstance(created_dt, datetime)
        assert isinstance(updated_dt, datetime)
    
    def test_to_dict(self):
        """测试转换为字典"""
        chapter = Chapter(
            id="dict-test",
            project_id="project-001",
            title="字典测试",
            content="测试内容",
            order=2,
            word_count=800,
            status="published",
            tags=["测试", "字典"]
        )
        
        chapter_dict = chapter.to_dict()
        
        assert isinstance(chapter_dict, dict)
        assert chapter_dict["id"] == "dict-test"
        assert chapter_dict["project_id"] == "project-001"
        assert chapter_dict["title"] == "字典测试"
        assert chapter_dict["content"] == "测试内容"
        assert chapter_dict["order"] == 2
        assert chapter_dict["word_count"] == 800
        assert chapter_dict["status"] == "published"
        assert chapter_dict["tags"] == ["测试", "字典"]
        assert "created_at" in chapter_dict
        assert "updated_at" in chapter_dict
    
    def test_from_dict(self):
        """测试从字典创建"""
        chapter_data = {
            "id": "from-dict-test",
            "project_id": "project-001",
            "title": "从字典创建",
            "content": "从字典数据创建的章节内容",
            "order": 3,
            "word_count": 1200,
            "status": "review",
            "notes": "需要审查",
            "tags": ["字典", "创建"],
            "outline": "章节大纲",
            "characters": ["主角", "配角"],
            "locations": ["城市", "学校"]
        }
        
        chapter = Chapter.from_dict(chapter_data)
        
        assert chapter.id == "from-dict-test"
        assert chapter.project_id == "project-001"
        assert chapter.title == "从字典创建"
        assert chapter.content == "从字典数据创建的章节内容"
        assert chapter.order == 3
        assert chapter.word_count == 1200
        assert chapter.status == "review"
        assert chapter.notes == "需要审查"
        assert chapter.tags == ["字典", "创建"]
        assert chapter.outline == "章节大纲"
        assert chapter.characters == ["主角", "配角"]
        assert chapter.locations == ["城市", "学校"]
    
    def test_from_dict_missing_fields(self):
        """测试从不完整字典创建"""
        chapter_data = {
            "id": "incomplete-test",
            "project_id": "project-001",
            "title": "不完整章节"
        }
        
        chapter = Chapter.from_dict(chapter_data)
        
        assert chapter.id == "incomplete-test"
        assert chapter.project_id == "project-001"
        assert chapter.title == "不完整章节"
        assert chapter.content == ""
        assert chapter.order == 0
        assert chapter.word_count == 0
        assert chapter.status == "draft"
        assert chapter.tags == []
        assert chapter.characters == []
        assert chapter.locations == []
    
    def test_update_word_count(self):
        """测试更新字数统计"""
        chapter = Chapter(
            id="word-count-test",
            project_id="project-001",
            title="字数测试",
            content="这是一个测试内容，用来计算字数。包含中文和English混合内容。"
        )
        
        # 更新字数统计
        chapter.update_word_count()
        
        # 验证字数被正确计算
        assert chapter.word_count > 0
        
        # 测试空内容
        chapter.content = ""
        chapter.update_word_count()
        assert chapter.word_count == 0
    
    def test_add_character(self):
        """测试添加角色"""
        chapter = Chapter(
            id="character-test",
            project_id="project-001",
            title="角色测试"
        )
        
        # 添加新角色
        chapter.add_character("张三")
        assert "张三" in chapter.characters
        
        # 添加重复角色
        chapter.add_character("张三")
        assert chapter.characters.count("张三") == 1
        
        # 添加多个角色
        chapter.add_character("李四")
        chapter.add_character("王五")
        assert len(chapter.characters) == 3
        assert "李四" in chapter.characters
        assert "王五" in chapter.characters
    
    def test_remove_character(self):
        """测试移除角色"""
        chapter = Chapter(
            id="remove-char-test",
            project_id="project-001",
            title="移除角色测试",
            characters=["张三", "李四", "王五"]
        )
        
        # 移除存在的角色
        chapter.remove_character("李四")
        assert "李四" not in chapter.characters
        assert len(chapter.characters) == 2
        
        # 移除不存在的角色
        chapter.remove_character("不存在")
        assert len(chapter.characters) == 2
    
    def test_add_location(self):
        """测试添加地点"""
        chapter = Chapter(
            id="location-test",
            project_id="project-001",
            title="地点测试"
        )
        
        # 添加新地点
        chapter.add_location("村庄")
        assert "村庄" in chapter.locations
        
        # 添加重复地点
        chapter.add_location("村庄")
        assert chapter.locations.count("村庄") == 1
        
        # 添加多个地点
        chapter.add_location("森林")
        chapter.add_location("城市")
        assert len(chapter.locations) == 3
        assert "森林" in chapter.locations
        assert "城市" in chapter.locations
    
    def test_remove_location(self):
        """测试移除地点"""
        chapter = Chapter(
            id="remove-loc-test",
            project_id="project-001",
            title="移除地点测试",
            locations=["村庄", "森林", "城市"]
        )
        
        # 移除存在的地点
        chapter.remove_location("森林")
        assert "森林" not in chapter.locations
        assert len(chapter.locations) == 2
        
        # 移除不存在的地点
        chapter.remove_location("不存在")
        assert len(chapter.locations) == 2
    
    def test_add_tag(self):
        """测试添加标签"""
        chapter = Chapter(
            id="tag-test",
            project_id="project-001",
            title="标签测试"
        )
        
        # 添加新标签
        chapter.add_tag("重要")
        assert "重要" in chapter.tags
        
        # 添加重复标签
        chapter.add_tag("重要")
        assert chapter.tags.count("重要") == 1
        
        # 添加多个标签
        chapter.add_tag("精彩")
        chapter.add_tag("转折")
        assert len(chapter.tags) == 3
        assert "精彩" in chapter.tags
        assert "转折" in chapter.tags
    
    def test_remove_tag(self):
        """测试移除标签"""
        chapter = Chapter(
            id="remove-tag-test",
            project_id="project-001",
            title="移除标签测试",
            tags=["重要", "精彩", "转折"]
        )
        
        # 移除存在的标签
        chapter.remove_tag("精彩")
        assert "精彩" not in chapter.tags
        assert len(chapter.tags) == 2
        
        # 移除不存在的标签
        chapter.remove_tag("不存在")
        assert len(chapter.tags) == 2
    
    def test_has_tag(self):
        """测试检查标签"""
        chapter = Chapter(
            id="has-tag-test",
            project_id="project-001",
            title="检查标签测试",
            tags=["重要", "精彩"]
        )
        
        assert chapter.has_tag("重要") is True
        assert chapter.has_tag("精彩") is True
        assert chapter.has_tag("普通") is False
    
    def test_is_draft(self):
        """测试是否为草稿"""
        draft_chapter = Chapter(
            id="draft-test",
            project_id="project-001",
            title="草稿测试",
            status="draft"
        )
        
        published_chapter = Chapter(
            id="published-test",
            project_id="project-001",
            title="已发布测试",
            status="published"
        )
        
        assert draft_chapter.is_draft() is True
        assert published_chapter.is_draft() is False
    
    def test_is_published(self):
        """测试是否已发布"""
        published_chapter = Chapter(
            id="published-test",
            project_id="project-001",
            title="已发布测试",
            status="published"
        )
        
        draft_chapter = Chapter(
            id="draft-test",
            project_id="project-001",
            title="草稿测试",
            status="draft"
        )
        
        assert published_chapter.is_published() is True
        assert draft_chapter.is_published() is False
    
    def test_get_status_display(self):
        """测试获取状态显示"""
        draft_chapter = Chapter(
            id="status-test1",
            project_id="project-001",
            title="状态测试1",
            status="draft"
        )
        
        review_chapter = Chapter(
            id="status-test2",
            project_id="project-001",
            title="状态测试2",
            status="review"
        )
        
        published_chapter = Chapter(
            id="status-test3",
            project_id="project-001",
            title="状态测试3",
            status="published"
        )
        
        assert draft_chapter.get_status_display() in ["draft", "草稿"]
        assert review_chapter.get_status_display() in ["review", "审查中"]
        assert published_chapter.get_status_display() in ["published", "已发布"]
    
    def test_get_reading_time(self):
        """测试获取阅读时间"""
        chapter = Chapter(
            id="reading-time-test",
            project_id="project-001",
            title="阅读时间测试",
            word_count=1000
        )
        
        reading_time = chapter.get_reading_time()
        
        # 假设阅读速度为每分钟200字
        expected_time = 1000 / 200
        assert reading_time == expected_time
        
        # 测试零字数
        chapter.word_count = 0
        assert chapter.get_reading_time() == 0
    
    def test_validate(self):
        """测试验证"""
        # 有效章节
        valid_chapter = Chapter(
            id="valid-chapter",
            project_id="project-001",
            title="有效章节"
        )
        assert valid_chapter.validate() is True
        
        # 无效章节（空标题）
        invalid_chapter1 = Chapter(
            id="invalid1",
            project_id="project-001",
            title=""
        )
        assert invalid_chapter1.validate() is False
        
        # 无效章节（空项目ID）
        invalid_chapter2 = Chapter(
            id="invalid2",
            project_id="",
            title="无效章节2"
        )
        assert invalid_chapter2.validate() is False
        
        # 无效章节（负序号）
        invalid_chapter3 = Chapter(
            id="invalid3",
            project_id="project-001",
            title="无效章节3",
            order=-1
        )
        assert invalid_chapter3.validate() is False
        
        # 无效章节（负字数）
        invalid_chapter4 = Chapter(
            id="invalid4",
            project_id="project-001",
            title="无效章节4",
            word_count=-100
        )
        assert invalid_chapter4.validate() is False
    
    def test_equality(self):
        """测试相等性比较"""
        chapter1 = Chapter(
            id="equal-test",
            project_id="project-001",
            title="相等测试"
        )
        
        chapter2 = Chapter(
            id="equal-test",
            project_id="project-001",
            title="相等测试"
        )
        
        chapter3 = Chapter(
            id="different-test",
            project_id="project-001",
            title="不同测试"
        )
        
        # 相同ID的章节应该相等
        assert chapter1 == chapter2
        
        # 不同ID的章节不相等
        assert chapter1 != chapter3
    
    def test_string_representation(self):
        """测试字符串表示"""
        chapter = Chapter(
            id="str-test",
            project_id="project-001",
            title="字符串测试",
            order=1
        )
        
        str_repr = str(chapter)
        assert "字符串测试" in str_repr
        assert "str-test" in str_repr
    
    def test_copy(self):
        """测试复制章节"""
        original = Chapter(
            id="original",
            project_id="project-001",
            title="原始章节",
            content="原始内容",
            order=1,
            word_count=500,
            characters=["张三", "李四"],
            tags=["标签1", "标签2"]
        )
        
        # 复制章节
        copied = original.copy(
            new_id="copied",
            new_title="复制章节",
            new_project_id="project-002",
            new_order=2
        )
        
        assert copied.id == "copied"
        assert copied.project_id == "project-002"
        assert copied.title == "复制章节"
        assert copied.order == 2
        assert copied.content == "原始内容"
        assert copied.word_count == 500
        assert copied.characters == ["张三", "李四"]
        assert copied.tags == ["标签1", "标签2"]
        
        # 验证是独立的副本
        copied.add_tag("新标签")
        assert "新标签" not in original.tags
    
    def test_export_data(self):
        """测试导出数据"""
        chapter = Chapter(
            id="export-test",
            project_id="project-001",
            title="导出测试",
            content="导出内容",
            order=1,
            word_count=300,
            status="published",
            characters=["主角"],
            tags=["重要"]
        )
        
        exported_data = chapter.export_data()
        
        # 验证导出的数据包含所有必要字段
        assert exported_data["id"] == "export-test"
        assert exported_data["project_id"] == "project-001"
        assert exported_data["title"] == "导出测试"
        assert exported_data["content"] == "导出内容"
        assert exported_data["order"] == 1
        assert exported_data["word_count"] == 300
        assert exported_data["status"] == "published"
        assert exported_data["characters"] == ["主角"]
        assert exported_data["tags"] == ["重要"]
        assert "created_at" in exported_data
        assert "updated_at" in exported_data
    
    def test_import_data(self):
        """测试导入数据"""
        import_data = {
            "id": "import-test",
            "project_id": "project-001",
            "title": "导入测试",
            "content": "导入内容",
            "order": 2,
            "word_count": 800,
            "status": "review",
            "notes": "导入笔记",
            "tags": ["导入", "测试"],
            "outline": "导入大纲",
            "characters": ["角色1", "角色2"],
            "locations": ["地点1", "地点2"],
            "created_at": "2024-01-01T00:00:00",
            "updated_at": "2024-01-02T00:00:00"
        }
        
        chapter = Chapter.import_data(import_data)
        
        assert chapter.id == "import-test"
        assert chapter.project_id == "project-001"
        assert chapter.title == "导入测试"
        assert chapter.content == "导入内容"
        assert chapter.order == 2
        assert chapter.word_count == 800
        assert chapter.status == "review"
        assert chapter.notes == "导入笔记"
        assert chapter.tags == ["导入", "测试"]
        assert chapter.outline == "导入大纲"
        assert chapter.characters == ["角色1", "角色2"]
        assert chapter.locations == ["地点1", "地点2"]
        assert chapter.created_at == "2024-01-01T00:00:00"
        assert chapter.updated_at == "2024-01-02T00:00:00"
    
    def test_get_summary(self):
        """测试获取章节摘要"""
        chapter = Chapter(
            id="summary-test",
            project_id="project-001",
            title="摘要测试",
            content="这是一个很长的章节内容，用来测试摘要功能。" * 10,
            word_count=200,
            status="published"
        )
        
        summary = chapter.get_summary(max_length=50)
        
        assert isinstance(summary, str)
        assert len(summary) <= 50
        assert "摘要测试" in summary or "这是一个很长的章节内容" in summary
    
    def test_update_from_dict(self):
        """测试从字典更新章节"""
        chapter = Chapter(
            id="update-test",
            project_id="project-001",
            title="更新测试",
            order=1
        )
        
        update_data = {
            "content": "更新的内容",
            "order": 2,
            "status": "published",
            "tags": ["新标签"],
            "word_count": 1000
        }
        
        chapter.update_from_dict(update_data)
        
        assert chapter.title == "更新测试"  # 未更新的字段保持不变
        assert chapter.content == "更新的内容"
        assert chapter.order == 2
        assert chapter.status == "published"
        assert chapter.tags == ["新标签"]
        assert chapter.word_count == 1000
    
    def test_get_next_order(self):
        """测试获取下一个序号"""
        chapters = [
            Chapter(id="ch1", project_id="proj1", title="章节1", order=1),
            Chapter(id="ch2", project_id="proj1", title="章节2", order=3),
            Chapter(id="ch3", project_id="proj1", title="章节3", order=5)
        ]
        
        next_order = Chapter.get_next_order(chapters)
        assert next_order == 6  # 应该是最大序号 + 1
        
        # 测试空列表
        assert Chapter.get_next_order([]) == 1
    
    def test_reorder_chapters(self):
        """测试重新排序章节"""
        chapters = [
            Chapter(id="ch1", project_id="proj1", title="章节1", order=3),
            Chapter(id="ch2", project_id="proj1", title="章节2", order=1),
            Chapter(id="ch3", project_id="proj1", title="章节3", order=5)
        ]
        
        Chapter.reorder_chapters(chapters)
        
        # 验证章节按序号排序
        assert chapters[0].order == 1
        assert chapters[1].order == 3
        assert chapters[2].order == 5
        
        # 验证章节ID顺序
        assert chapters[0].id == "ch2"
        assert chapters[1].id == "ch1"
        assert chapters[2].id == "ch3"