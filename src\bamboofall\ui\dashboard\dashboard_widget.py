"""
仪表板界面

显示项目列表、最近项目、写作统计等信息
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QScrollArea,
    QLabel, QFrame, QPushButton, QListWidget, QListWidgetItem,
    QGroupBox, QProgressBar, QSizePolicy
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QSize
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor, QBrush
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime, timedelta

from ...models.project import Project
from ...core.project_manager import get_project_manager
from ..widgets.modern_widgets import (
    ModernButton, create_modern_button, ProjectCardWidget,
    create_project_card, StatusIndicator
)
from ..themes.theme_manager import get_theme_manager
from ...utils.logger import LoggerMixin

logger = logging.getLogger(__name__)


class StatCard(QFrame):
    """统计卡片组件"""
    
    def __init__(self, title: str, value: str, subtitle: str = "", parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.title = title
        self.value = value
        self.subtitle = subtitle
        
        self.setup_ui()
        self.setup_style()
        
        # 连接主题变化信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 16, 20, 16)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel(self.title)
        title_label.setStyleSheet("color: #666; font-size: 12px; font-weight: 500;")
        layout.addWidget(title_label)
        
        # 数值
        value_label = QLabel(self.value)
        value_font = QFont()
        value_font.setPointSize(24)
        value_font.setBold(True)
        value_label.setFont(value_font)
        layout.addWidget(value_label)
        
        # 副标题
        if self.subtitle:
            subtitle_label = QLabel(self.subtitle)
            subtitle_label.setStyleSheet("color: #999; font-size: 11px;")
            layout.addWidget(subtitle_label)
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        self.setStyleSheet(f"""
            StatCard {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 12px;
            }}
            StatCard:hover {{
                border-color: {colors['primary']};
            }}
        """)
    
    def on_theme_changed(self):
        """主题变化时更新样式"""
        self.setup_style()
    
    def update_value(self, new_value: str):
        """更新数值"""
        self.value = new_value
        # 找到数值标签并更新
        layout = self.layout()
        if layout.count() >= 2:
            value_label = layout.itemAt(1).widget()
            if isinstance(value_label, QLabel):
                value_label.setText(new_value)


class QuickActionsWidget(QWidget):
    """快速操作组件"""
    
    new_project_clicked = pyqtSignal()
    open_project_clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        
        self.setup_ui()
        self.setup_style()
        
        # 连接主题变化信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(0, 0, 0, 0)

        # 标题
        title_label = QLabel("快速操作")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #333; margin-bottom: 8px;")
        layout.addWidget(title_label)

        # 按钮容器
        button_container = QWidget()
        button_layout = QVBoxLayout(button_container)
        button_layout.setSpacing(16)
        button_layout.setContentsMargins(0, 0, 0, 0)

        # 新建项目按钮 - 主要操作
        self.new_project_btn = create_modern_button("📝 新建项目", "primary")
        self.new_project_btn.setMinimumHeight(72)
        self.new_project_btn.setStyleSheet(self.new_project_btn.styleSheet() + """
            QPushButton {
                font-size: 16px;
                font-weight: 600;
                border-radius: 12px;
            }
        """)
        self.new_project_btn.clicked.connect(self.new_project_clicked.emit)
        button_layout.addWidget(self.new_project_btn)

        # 次要操作按钮行
        secondary_layout = QHBoxLayout()
        secondary_layout.setSpacing(12)

        # 打开项目按钮
        self.open_project_btn = create_modern_button("📂 打开项目", "secondary")
        self.open_project_btn.setMinimumHeight(56)
        self.open_project_btn.clicked.connect(self.open_project_clicked.emit)
        secondary_layout.addWidget(self.open_project_btn)

        # 导入项目按钮
        self.import_btn = create_modern_button("📥 导入", "outline")
        self.import_btn.setMinimumHeight(56)
        secondary_layout.addWidget(self.import_btn)

        button_layout.addLayout(secondary_layout)

        # 帮助按钮
        self.help_btn = create_modern_button("❓ 使用帮助", "outline")
        self.help_btn.setMinimumHeight(48)
        button_layout.addWidget(self.help_btn)

        layout.addWidget(button_container)
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors

        self.setStyleSheet(f"""
            QuickActionsWidget {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 16px;
                padding: 24px;
            }}
        """)
    
    def on_theme_changed(self):
        """主题变化时更新样式"""
        self.setup_style()


class RecentProjectsWidget(QWidget):
    """最近项目组件"""
    
    project_clicked = pyqtSignal(str)  # 项目ID
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.project_manager = get_project_manager()
        
        self.setup_ui()
        self.setup_style()
        self.load_recent_projects()
        
        # 连接主题变化信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(0, 0, 0, 0)

        # 标题栏
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)

        title_label = QLabel("最近项目")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #333; margin-bottom: 8px;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 查看全部按钮
        self.view_all_btn = QPushButton("查看全部")
        self.view_all_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: #2196F3;
                font-weight: 500;
                padding: 4px 8px;
            }
            QPushButton:hover {
                background-color: rgba(33, 150, 243, 0.1);
                border-radius: 4px;
            }
        """)
        header_layout.addWidget(self.view_all_btn)

        layout.addLayout(header_layout)

        # 项目列表滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 项目列表容器
        self.projects_container = QWidget()
        self.projects_layout = QVBoxLayout(self.projects_container)
        self.projects_layout.setSpacing(8)
        self.projects_layout.setContentsMargins(0, 0, 0, 0)

        scroll_area.setWidget(self.projects_container)
        layout.addWidget(scroll_area)

        # 空状态提示
        self.empty_label = QLabel("暂无最近项目\n开始创建您的第一个项目吧！")
        self.empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.empty_label.setStyleSheet("""
            QLabel {
                color: #999;
                font-size: 14px;
                padding: 40px;
            }
        """)
        self.empty_label.setVisible(False)
        layout.addWidget(self.empty_label)

    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors

        self.setStyleSheet(f"""
            RecentProjectsWidget {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 16px;
                padding: 24px;
            }}
        """)

    def load_recent_projects(self):
        """加载最近项目"""
        try:
            recent_projects = self.project_manager.get_recent_projects(5)
            self.update_projects_list(recent_projects)
        except Exception as e:
            logger.error(f"加载最近项目失败: {e}")
            self.show_empty_state()
    
    def update_projects_list(self, projects: List[Project]):
        """更新项目列表"""
        # 清空现有项目
        for i in reversed(range(self.projects_layout.count())):
            child = self.projects_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        if not projects:
            self.show_empty_state()
            return
        
        # 隐藏空状态
        self.empty_label.setVisible(False)
        self.projects_container.setVisible(True)
        
        # 添加项目卡片
        for project in projects:
            card_data = {
                "id": project.id,
                "name": project.name,
                "description": project.description or "暂无描述",
                "type": project.type.value,
                "word_count": project.word_count,
                "updated_at": project.updated_at.strftime("%m月%d日 %H:%M")
            }
            
            card = ProjectCardWidget(card_data)
            card.clicked.connect(self.project_clicked.emit)
            self.projects_layout.addWidget(card)
        
        # 添加弹性空间
        self.projects_layout.addStretch()
    
    def show_empty_state(self):
        """显示空状态"""
        self.projects_container.setVisible(False)
        self.empty_label.setVisible(True)
    
    def on_theme_changed(self):
        """主题变化时更新样式"""
        self.setup_style()


class WritingStatsWidget(QWidget):
    """写作统计组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.project_manager = get_project_manager()
        
        self.setup_ui()
        self.setup_style()
        self.load_stats()
        
        # 定时更新统计
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.load_stats)
        self.stats_timer.start(60000)  # 每分钟更新一次
        
        # 连接主题变化信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(0, 0, 0, 0)

        # 标题
        title_label = QLabel("写作统计")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #333; margin-bottom: 8px;")
        layout.addWidget(title_label)
        
        # 统计卡片布局
        stats_layout = QGridLayout()
        stats_layout.setSpacing(16)
        
        # 总项目数
        self.total_projects_card = StatCard("总项目数", "0", "个创作项目")
        stats_layout.addWidget(self.total_projects_card, 0, 0)
        
        # 总字数
        self.total_words_card = StatCard("总字数", "0", "累计创作")
        stats_layout.addWidget(self.total_words_card, 0, 1)
        
        # 今日字数
        self.today_words_card = StatCard("今日字数", "0", "今天写作")
        stats_layout.addWidget(self.today_words_card, 1, 0)
        
        # 连续写作天数
        self.streak_days_card = StatCard("连续天数", "0", "坚持写作")
        stats_layout.addWidget(self.streak_days_card, 1, 1)
        
        layout.addLayout(stats_layout)
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors

        self.setStyleSheet(f"""
            WritingStatsWidget {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 16px;
                padding: 24px;
            }}
        """)

    def load_stats(self):
        """加载统计数据"""
        try:
            projects = self.project_manager.get_all_projects()
            
            # 计算统计数据
            total_projects = len(projects)
            total_words = sum(p.word_count for p in projects)
            today_words = 0  # TODO: 实现今日字数统计
            streak_days = 0  # TODO: 实现连续写作天数统计
            
            # 更新卡片
            self.total_projects_card.update_value(str(total_projects))
            self.total_words_card.update_value(f"{total_words:,}")
            self.today_words_card.update_value(str(today_words))
            self.streak_days_card.update_value(str(streak_days))
            
        except Exception as e:
            logger.error(f"加载统计数据失败: {e}")

    def on_theme_changed(self):
        """主题变化时更新样式"""
        self.setup_style()


class DashboardWidget(QWidget, LoggerMixin):
    """仪表板主界面"""
    
    # 信号定义
    new_project_requested = pyqtSignal()
    open_project_requested = pyqtSignal()
    project_selected = pyqtSignal(str)  # 项目ID
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.project_manager = get_project_manager()
        
        self.setup_ui()
        self.setup_style()
        self.setup_signals()
        
        self.logger.info("仪表板界面初始化完成")

    def setup_ui(self):
        """设置用户界面"""
        # 主滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 主内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(32)
        content_layout.setContentsMargins(40, 40, 40, 40)

        # 欢迎标题区域
        welcome_layout = QVBoxLayout()
        welcome_layout.setSpacing(12)

        welcome_label = QLabel("欢迎使用笔落App")
        welcome_font = QFont()
        welcome_font.setPointSize(32)
        welcome_font.setBold(True)
        welcome_label.setFont(welcome_font)
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        welcome_label.setStyleSheet("""
            QLabel {
                color: #2196F3;
                margin-bottom: 8px;
            }
        """)
        welcome_layout.addWidget(welcome_label)

        subtitle_label = QLabel("让AI为您的创作插上翅膀")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 18px;
                margin-bottom: 32px;
                font-weight: 300;
            }
        """)
        welcome_layout.addWidget(subtitle_label)

        content_layout.addLayout(welcome_layout)

        # 主要内容区域
        main_content_layout = QHBoxLayout()
        main_content_layout.setSpacing(32)

        # 左侧列 - 快速操作和统计
        left_column = QVBoxLayout()
        left_column.setSpacing(24)

        # 快速操作
        self.quick_actions = QuickActionsWidget()
        left_column.addWidget(self.quick_actions)

        # 写作统计
        self.writing_stats = WritingStatsWidget()
        left_column.addWidget(self.writing_stats)

        left_column.addStretch()

        # 右侧列（最近项目）
        self.recent_projects = RecentProjectsWidget()

        # 设置列比例 - 左侧稍窄，右侧更宽
        main_content_layout.addLayout(left_column, 2)
        main_content_layout.addWidget(self.recent_projects, 3)

        content_layout.addLayout(main_content_layout)
        content_layout.addStretch()

        scroll_area.setWidget(content_widget)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)

    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors

        self.setStyleSheet(f"""
            DashboardWidget {{
                background-color: {colors['background']};
            }}
            
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            
            QScrollBar:vertical {{
                background-color: {colors['surface']};
                width: 12px;
                border-radius: 6px;
            }}
            
            QScrollBar::handle:vertical {{
                background-color: {colors['text_disabled']};
                border-radius: 6px;
                min-height: 20px;
            }}
            
            QScrollBar::handle:vertical:hover {{
                background-color: {colors['text_secondary']};
            }}
        """)

    def setup_signals(self):
        """设置信号连接"""
        # 快速操作信号
        self.quick_actions.new_project_clicked.connect(self.new_project_requested.emit)
        self.quick_actions.open_project_clicked.connect(self.open_project_requested.emit)
        
        # 最近项目信号
        self.recent_projects.project_clicked.connect(self.project_selected.emit)
        
        # 主题变化信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def refresh_data(self):
        """刷新仪表板数据"""
        self.recent_projects.load_recent_projects()
        self.writing_stats.load_stats()
        self.logger.info("仪表板数据已刷新")

    def on_theme_changed(self):
        """主题变化时更新样式"""
        self.setup_style()