# -*- coding: utf-8 -*-
"""
大纲管理器

管理项目的大纲结构，提供大纲的创建、编辑、组织等功能
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import json
from datetime import datetime
from functools import lru_cache

from ..models.outline import Outline, OutlineNode, OutlineNodeType, OutlineNodeStatus
from ..models.project import Project
from ..database.repositories import OutlineRepository
from ..utils.logger import LoggerMixin
from ..utils.config_utils import get_config_manager
from ..core.cache_manager import get_cache_manager
from ..core.performance_monitor import performance_monitor
from ..exceptions.exceptions import (
    BambooFallError, ProjectNotFoundError, ValidationError
)

logger = logging.getLogger(__name__)


class OutlineManager(LoggerMixin):
    """大纲管理器"""
    
    def __init__(self, project_manager=None):
        self.config_manager = get_config_manager()
        self.cache_manager = get_cache_manager()
        self.project_manager = project_manager
        self.current_project: Optional[Project] = None
        self.current_outline: Optional[Outline] = None
        
        # 节点缓存
        self.nodes_cache: Dict[str, OutlineNode] = {}
        self.cache_ttl = 900  # 15分钟缓存
        
        # 初始化仓储
        self.outline_repo = OutlineRepository()
        
        self.logger.info("大纲管理器初始化完成")
    
    @performance_monitor(category="outline")
    def set_current_project(self, project: Project):
        """设置当前项目"""
        self.current_project = project
        self.nodes_cache.clear()
        
        # 清除相关缓存
        self._clear_project_cache(project.id)
        
        # 加载项目的主大纲
        self.current_outline = self.get_project_main_outline(project.id)
        if not self.current_outline:
            # 创建默认大纲
            self.current_outline = self.create_default_outline(project)
        
        self.logger.info(f"设置当前项目: {project.name}")
    
    def create_default_outline(self, project: Project) -> Outline:
        """为项目创建默认大纲"""
        try:
            # 创建大纲
            outline = Outline(
                project_id=project.id,
                name=f"{project.name} - 主大纲",
                description="项目主要大纲结构"
            )
            
            # 创建根节点
            root_node = OutlineNode(
                project_id=project.id,
                title=project.name,
                content=project.description or "",
                type=OutlineNodeType.ROOT,
                level=0
            )
            
            # 保存到数据库
            created_outline = self.outline_repo.create_outline(outline)
            created_root = self.outline_repo.create_outline_node(root_node)
            
            # 设置根节点ID
            created_outline.root_node_id = created_root.id
            self.outline_repo.update_outline(created_outline)
            
            # 缓存节点
            self.nodes_cache[created_root.id] = created_root
            
            self.logger.info(f"创建默认大纲: {outline.name}")
            return created_outline
            
        except Exception as e:
            self.logger.error(f"创建默认大纲失败: {e}")
            raise BambooFallError(f"创建默认大纲失败: {e}")
    
    @performance_monitor(category="outline")
    def get_project_main_outline(self, project_id: str) -> Optional[Outline]:
        """获取项目主大纲"""
        # 检查缓存
        cache_key = f"main_outline_{project_id}"
        cached_outline = self.cache_manager.get(cache_key)
        if cached_outline:
            return cached_outline
            
        try:
            outlines = self.outline_repo.get_project_outlines(project_id)
            if outlines:
                main_outline = outlines[0]  # 返回第一个大纲作为主大纲
                # 缓存结果
                self.cache_manager.set(cache_key, main_outline, ttl=self.cache_ttl)
                return main_outline
            return None
        except Exception as e:
            self.logger.error(f"获取项目主大纲失败: {e}")
            return None
    
    @performance_monitor(category="outline")
    def get_outline_tree(self, outline_id: Optional[str] = None) -> List[OutlineNode]:
        """获取大纲树结构"""
        if not outline_id and self.current_outline:
            outline_id = self.current_outline.id
        
        if not outline_id:
            return []
        
        # 检查缓存
        cache_key = f"outline_tree_{outline_id}"
        cached_tree = self.cache_manager.get(cache_key)
        if cached_tree:
            return cached_tree
        
        try:
            # 获取所有节点
            nodes = self.outline_repo.get_outline_nodes(outline_id)
            
            # 缓存节点
            for node in nodes:
                self.nodes_cache[node.id] = node
            
            # 构建树结构
            tree = self._build_tree_structure(nodes)
            
            # 缓存树结构
            self.cache_manager.set(cache_key, tree, ttl=self.cache_ttl)
            return tree
            
        except Exception as e:
            self.logger.error(f"获取大纲树结构失败: {e}")
            return []
    
    def _build_tree_structure(self, nodes: List[OutlineNode]) -> List[OutlineNode]:
        """构建树状结构"""
        # 按父子关系组织节点
        node_map = {node.id: node for node in nodes}
        root_nodes = []
        
        for node in nodes:
            if node.parent_id is None or node.parent_id not in node_map:
                root_nodes.append(node)
        
        # 按order_index排序
        root_nodes.sort(key=lambda x: x.order_index)
        return root_nodes
    
    @lru_cache(maxsize=128)
    @performance_monitor(category="outline")
    def get_node_children(self, node_id: str) -> List[OutlineNode]:
        """获取节点的子节点"""
        # 检查缓存
        cache_key = f"node_children_{node_id}"
        cached_children = self.cache_manager.get(cache_key)
        if cached_children:
            return cached_children
            
        try:
            node = self.get_node(node_id)
            if not node:
                return []
            
            children = []
            for child_id in node.children_ids:
                child = self.get_node(child_id)
                if child:
                    children.append(child)
            
            # 按order_index排序
            children.sort(key=lambda x: x.order_index)
            
            # 缓存结果
            self.cache_manager.set(cache_key, children, ttl=self.cache_ttl)
            return children
            
        except Exception as e:
            self.logger.error(f"获取子节点失败: {e}")
            return []
    
    @lru_cache(maxsize=256)
    @performance_monitor(category="outline")
    def get_node(self, node_id: str) -> Optional[OutlineNode]:
        """获取节点"""
        # 先从缓存获取
        if node_id in self.nodes_cache:
            return self.nodes_cache[node_id]
        
        # 检查缓存管理器
        cache_key = f"outline_node_{node_id}"
        cached_node = self.cache_manager.get(cache_key)
        if cached_node:
            self.nodes_cache[node_id] = cached_node
            return cached_node
        
        # 从数据库获取
        try:
            node = self.outline_repo.get_outline_node(node_id)
            if node:
                self.nodes_cache[node_id] = node
                # 缓存到缓存管理器
                self.cache_manager.set(cache_key, node, ttl=self.cache_ttl)
            return node
        except Exception as e:
            self.logger.error(f"获取节点失败: {e}")
            return None
    
    @performance_monitor(category="outline")
    def create_node(self, parent_id: Optional[str], title: str, 
                   node_type: OutlineNodeType = OutlineNodeType.CHAPTER,
                   content: str = "") -> Optional[OutlineNode]:
        """创建新节点"""
        if not self.current_project:
            raise ProjectNotFoundError("未设置当前项目")
        
        try:
            # 计算层级和顺序
            level = 0
            order_index = 0
            
            if parent_id:
                parent = self.get_node(parent_id)
                if parent:
                    level = parent.level + 1
                    order_index = len(parent.children_ids)
            
            # 创建节点
            node = OutlineNode(
                project_id=self.current_project.id,
                title=title,
                content=content,
                type=node_type,
                parent_id=parent_id,
                level=level,
                order_index=order_index
            )
            
            # 保存到数据库
            created_node = self.outline_repo.create_outline_node(node)
            
            # 更新父节点
            if parent_id:
                parent = self.get_node(parent_id)
                if parent:
                    parent.add_child(created_node.id)
                    self.outline_repo.update_outline_node(parent)
            
            # 缓存节点
            self.nodes_cache[created_node.id] = created_node
            
            # 清除相关缓存
            self._invalidate_node_cache(parent_id)
            
            self.logger.info(f"创建节点: {title}")
            return created_node
            
        except Exception as e:
            self.logger.error(f"创建节点失败: {e}")
            raise BambooFallError(f"创建节点失败: {e}")
    
    @performance_monitor(category="outline")
    def update_node(self, node_id: str, **kwargs) -> bool:
        """更新节点"""
        try:
            node = self.get_node(node_id)
            if not node:
                return False
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(node, key):
                    setattr(node, key, value)
            
            node.update_timestamp()
            
            # 保存到数据库
            self.outline_repo.update_outline_node(node)
            
            # 更新缓存
            self.nodes_cache[node_id] = node
            
            # 清除相关缓存
            self._invalidate_node_cache(node_id)
            
            self.logger.info(f"更新节点: {node.title}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新节点失败: {e}")
            return False
    
    @performance_monitor(category="outline")
    def delete_node(self, node_id: str) -> bool:
        """删除节点"""
        try:
            node = self.get_node(node_id)
            if not node:
                return False
            
            # 递归删除子节点
            for child_id in node.children_ids.copy():
                self.delete_node(child_id)
            
            # 从父节点移除
            if node.parent_id:
                parent = self.get_node(node.parent_id)
                if parent:
                    parent.remove_child(node_id)
                    self.outline_repo.update_outline_node(parent)
            
            # 从数据库删除
            self.outline_repo.delete_outline_node(node_id)
            
            # 从缓存移除
            if node_id in self.nodes_cache:
                del self.nodes_cache[node_id]
            
            # 清除相关缓存
            self._invalidate_node_cache(node_id)
            self._invalidate_node_cache(node.parent_id)
            
            self.logger.info(f"删除节点: {node.title}")
            return True
            
        except Exception as e:
            self.logger.error(f"删除节点失败: {e}")
            return False
    
    def move_node(self, node_id: str, new_parent_id: Optional[str], 
                 new_index: int = -1) -> bool:
        """移动节点"""
        try:
            node = self.get_node(node_id)
            if not node:
                return False
            
            old_parent_id = node.parent_id
            
            # 从原父节点移除
            if old_parent_id:
                old_parent = self.get_node(old_parent_id)
                if old_parent:
                    old_parent.remove_child(node_id)
                    self.outline_repo.update_outline_node(old_parent)
            
            # 更新节点信息
            node.parent_id = new_parent_id
            
            # 计算新层级
            if new_parent_id:
                new_parent = self.get_node(new_parent_id)
                if new_parent:
                    node.level = new_parent.level + 1
                    # 添加到新父节点
                    if new_index >= 0:
                        new_parent.children_ids.insert(new_index, node_id)
                    else:
                        new_parent.add_child(node_id)
                    self.outline_repo.update_outline_node(new_parent)
            else:
                node.level = 0
            
            node.update_timestamp()
            self.outline_repo.update_outline_node(node)
            
            self.logger.info(f"移动节点: {node.title}")
            return True
            
        except Exception as e:
            self.logger.error(f"移动节点失败: {e}")
            return False
    
    def reorder_children(self, parent_id: str, child_ids: List[str]) -> bool:
        """重新排序子节点"""
        try:
            parent = self.get_node(parent_id)
            if not parent:
                return False
            
            # 更新子节点顺序
            parent.children_ids = child_ids
            
            # 更新每个子节点的order_index
            for i, child_id in enumerate(child_ids):
                child = self.get_node(child_id)
                if child:
                    child.order_index = i
                    child.update_timestamp()
                    self.outline_repo.update_outline_node(child)
            
            parent.update_timestamp()
            self.outline_repo.update_outline_node(parent)
            
            self.logger.info(f"重新排序子节点: {parent.title}")
            return True
            
        except Exception as e:
            self.logger.error(f"重新排序子节点失败: {e}")
            return False
    
    def search_nodes(self, keyword: str) -> List[OutlineNode]:
        """搜索节点"""
        if not self.current_outline:
            return []
        
        try:
            return self.outline_repo.search_outline_nodes(
                self.current_outline.id, keyword
            )
        except Exception as e:
            self.logger.error(f"搜索节点失败: {e}")
            return []
    
    def get_node_statistics(self, node_id: str) -> Dict[str, Any]:
        """获取节点统计信息"""
        node = self.get_node(node_id)
        if not node:
            return {}
        
        try:
            # 递归计算统计信息
            total_nodes = 1
            total_estimated_words = node.estimated_word_count or 0
            total_actual_words = node.actual_word_count
            completed_nodes = 1 if node.status == OutlineNodeStatus.COMPLETE else 0
            
            # 统计子节点
            for child_id in node.children_ids:
                child_stats = self.get_node_statistics(child_id)
                total_nodes += child_stats.get('total_nodes', 0)
                total_estimated_words += child_stats.get('total_estimated_words', 0)
                total_actual_words += child_stats.get('total_actual_words', 0)
                completed_nodes += child_stats.get('completed_nodes', 0)
            
            # 计算进度
            progress = (completed_nodes / total_nodes * 100) if total_nodes > 0 else 0
            word_progress = (total_actual_words / total_estimated_words * 100) if total_estimated_words > 0 else 0
            
            return {
                'total_nodes': total_nodes,
                'completed_nodes': completed_nodes,
                'total_estimated_words': total_estimated_words,
                'total_actual_words': total_actual_words,
                'progress_percentage': progress,
                'word_progress_percentage': word_progress
            }
            
        except Exception as e:
            self.logger.error(f"获取节点统计信息失败: {e}")
            return {}
    
    def export_outline(self, format_type: str = 'json') -> Optional[str]:
        """导出大纲"""
        if not self.current_outline:
            return None
        
        try:
            tree = self.get_outline_tree()
            
            if format_type == 'json':
                return self._export_to_json(tree)
            elif format_type == 'markdown':
                return self._export_to_markdown(tree)
            elif format_type == 'txt':
                return self._export_to_text(tree)
            else:
                raise ValidationError(f"不支持的导出格式: {format_type}")
                
        except Exception as e:
            self.logger.error(f"导出大纲失败: {e}")
            return None
    
    def _export_to_json(self, nodes: List[OutlineNode]) -> str:
        """导出为JSON格式"""
        def node_to_dict(node: OutlineNode) -> Dict[str, Any]:
            children = self.get_node_children(node.id)
            return {
                'id': node.id,
                'title': node.title,
                'content': node.content,
                'type': node.type.value,
                'status': node.status.value,
                'level': node.level,
                'estimated_word_count': node.estimated_word_count,
                'actual_word_count': node.actual_word_count,
                'tags': node.tags,
                'children': [node_to_dict(child) for child in children]
            }
        
        data = {
            'outline': {
                'id': self.current_outline.id,
                'name': self.current_outline.name,
                'description': self.current_outline.description,
                'exported_at': datetime.now().isoformat()
            },
            'nodes': [node_to_dict(node) for node in nodes]
        }
        
        return json.dumps(data, ensure_ascii=False, indent=2)
    
    def _export_to_markdown(self, nodes: List[OutlineNode]) -> str:
        """导出为Markdown格式"""
        lines = []
        lines.append(f"# {self.current_outline.name}")
        lines.append("")
        if self.current_outline.description:
            lines.append(self.current_outline.description)
            lines.append("")
        
        def add_node_markdown(node: OutlineNode, level: int = 1):
            # 添加标题
            prefix = "#" * min(level + 1, 6)
            lines.append(f"{prefix} {node.title}")
            lines.append("")
            
            # 添加内容
            if node.content:
                lines.append(node.content)
                lines.append("")
            
            # 添加元信息
            if node.estimated_word_count or node.actual_word_count:
                info = []
                if node.estimated_word_count:
                    info.append(f"预估字数: {node.estimated_word_count}")
                if node.actual_word_count:
                    info.append(f"实际字数: {node.actual_word_count}")
                lines.append(f"*{' | '.join(info)}*")
                lines.append("")
            
            # 递归处理子节点
            children = self.get_node_children(node.id)
            for child in children:
                add_node_markdown(child, level + 1)
        
        for node in nodes:
            add_node_markdown(node)
        
        return "\n".join(lines)
    
    def _export_to_text(self, nodes: List[OutlineNode]) -> str:
        """导出为纯文本格式"""
        lines = []
        lines.append(self.current_outline.name)
        lines.append("=" * len(self.current_outline.name))
        lines.append("")
        
        def add_node_text(node: OutlineNode, level: int = 0):
            # 添加缩进和标题
            indent = "  " * level
            lines.append(f"{indent}- {node.title}")
            
            # 添加内容
            if node.content:
                content_lines = node.content.split('\n')
                for line in content_lines:
                    if line.strip():
                        lines.append(f"{indent}  {line}")
            
            # 递归处理子节点
            children = self.get_node_children(node.id)
            for child in children:
                add_node_text(child, level + 1)
        
        for node in nodes:
            add_node_text(node)
        
        return "\n".join(lines)
    
    def _clear_project_cache(self, project_id: str):
        """清除项目相关缓存"""
        cache_patterns = [
            f"main_outline_{project_id}",
            f"outline_tree_*",
            f"node_children_*",
            f"outline_node_*"
        ]
        for pattern in cache_patterns:
            self.cache_manager.delete_pattern(pattern)
    
    def _invalidate_node_cache(self, node_id: Optional[str]):
        """使节点相关缓存失效"""
        if not node_id:
            return
            
        # 清除节点缓存
        cache_keys = [
            f"outline_node_{node_id}",
            f"node_children_{node_id}"
        ]
        
        for key in cache_keys:
            self.cache_manager.delete(key)
        
        # 清除内存缓存
        if hasattr(self, 'get_node_children'):
            self.get_node_children.cache_clear()
        if hasattr(self, 'get_node'):
            self.get_node.cache_clear()
        
        # 清除大纲树缓存
        if self.current_outline:
            tree_key = f"outline_tree_{self.current_outline.id}"
            self.cache_manager.delete(tree_key)
    
    def clear_cache(self):
        """清除所有缓存"""
        # 清除内存缓存
        self.nodes_cache.clear()
        
        # 清除LRU缓存
        if hasattr(self, 'get_node_children'):
            self.get_node_children.cache_clear()
        if hasattr(self, 'get_node'):
            self.get_node.cache_clear()
        
        # 清除缓存管理器中的大纲相关缓存
        cache_patterns = [
            "main_outline_*",
            "outline_tree_*",
            "node_children_*",
            "outline_node_*"
        ]
        for pattern in cache_patterns:
            self.cache_manager.delete_pattern(pattern)
            
        self.logger.info("大纲缓存已清除")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        stats = {
            "nodes_cache_size": len(self.nodes_cache)
        }
        
        # 添加LRU缓存统计
        if hasattr(self, 'get_node_children'):
            stats["node_children_cache"] = self.get_node_children.cache_info()
        if hasattr(self, 'get_node'):
            stats["node_cache"] = self.get_node.cache_info()
            
        return stats


# 全局大纲管理器实例
_outline_manager: Optional[OutlineManager] = None


def get_outline_manager() -> OutlineManager:
    """获取全局大纲管理器实例"""
    global _outline_manager
    if _outline_manager is None:
        _outline_manager = OutlineManager()
    return _outline_manager