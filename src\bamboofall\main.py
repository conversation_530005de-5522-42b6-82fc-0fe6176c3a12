#!/usr/bin/env python3
"""
笔落App 主程序入口

运行此文件启动应用程序
"""

import sys
import os
import asyncio
from pathlib import Path
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QCoreApplication, Qt
from PyQt6.QtGui import QIcon

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

from bamboofall.ui.main_window import MainWindow
from bamboofall.utils.logger import setup_logging
from bamboofall.utils.config_utils import ConfigManager

class BambooFallApp:
    """笔落App应用程序类"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.config_manager = ConfigManager()
        
    def init_app(self):
        """初始化应用程序"""
        # 设置应用程序属性
        QCoreApplication.setApplicationName("笔落App")
        QCoreApplication.setApplicationVersion("1.0.0")
        QCoreApplication.setOrganizationName("BambooFall")
        
        # 创建应用程序实例
        self.app = QApplication(sys.argv)
        
        # PyQt6中高DPI支持默认启用，无需手动设置
        
        # 设置应用程序图标
        icon_path = project_root / "resources" / "icons" / "app_icon.png"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))
        
        # 设置日志
        setup_logging()
        
        return self.app
    
    def create_main_window(self):
        """创建主窗口"""
        self.main_window = MainWindow()
        self.main_window.show()
        return self.main_window
    
    def run(self):
        """运行应用程序"""
        try:
            # 初始化应用程序
            app = self.init_app()
            
            # 创建主窗口
            main_window = self.create_main_window()
            
            # 运行应用程序
            return app.exec()
            
        except Exception as e:
            print(f"应用程序启动失败: {e}")
            return 1

def main():
    """主函数"""
    app = BambooFallApp()
    return app.run()

if __name__ == "__main__":
    sys.exit(main())