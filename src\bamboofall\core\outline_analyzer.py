# -*- coding: utf-8 -*-
"""
大纲分析器

提供大纲的统计分析功能，包括进度统计、字数统计、结构分析等
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, Counter

from ..models.outline import OutlineNode, OutlineNodeType, OutlineNodeStatus

logger = logging.getLogger(__name__)


class OutlineAnalyzer:
    """大纲分析器"""
    
    def __init__(self):
        pass
        
    def analyze_outline(self, nodes: List[OutlineNode]) -> Dict[str, Any]:
        """分析大纲
        
        Args:
            nodes: 大纲节点列表
            
        Returns:
            Dict: 分析结果
        """
        if not nodes:
            return self._empty_analysis()
            
        analysis = {
            'basic_stats': self._analyze_basic_stats(nodes),
            'progress_stats': self._analyze_progress(nodes),
            'word_count_stats': self._analyze_word_count(nodes),
            'structure_stats': self._analyze_structure(nodes),
            'type_distribution': self._analyze_type_distribution(nodes),
            'status_distribution': self._analyze_status_distribution(nodes),
            'timeline_stats': self._analyze_timeline(nodes),
            'completion_forecast': self._forecast_completion(nodes),
            'quality_metrics': self._analyze_quality_metrics(nodes),
            'recommendations': self._generate_recommendations(nodes)
        }
        
        return analysis
        
    def _empty_analysis(self) -> Dict[str, Any]:
        """空分析结果"""
        return {
            'basic_stats': {
                'total_nodes': 0,
                'root_nodes': 0,
                'leaf_nodes': 0,
                'max_depth': 0,
                'avg_children_per_node': 0
            },
            'progress_stats': {
                'overall_progress': 0.0,
                'completed_nodes': 0,
                'in_progress_nodes': 0,
                'draft_nodes': 0
            },
            'word_count_stats': {
                'total_estimated_words': 0,
                'total_actual_words': 0,
                'completion_ratio': 0.0,
                'avg_words_per_node': 0
            },
            'structure_stats': {
                'depth_distribution': {},
                'branching_factor': 0,
                'balance_score': 0
            },
            'type_distribution': {},
            'status_distribution': {},
            'timeline_stats': {},
            'completion_forecast': {},
            'quality_metrics': {},
            'recommendations': []
        }
        
    def _analyze_basic_stats(self, nodes: List[OutlineNode]) -> Dict[str, Any]:
        """分析基本统计信息"""
        total_nodes = len(nodes)
        
        # 构建节点映射
        node_dict = {node.id: node for node in nodes}
        
        # 统计根节点和叶子节点
        root_nodes = 0
        leaf_nodes = 0
        max_depth = 0
        total_children = 0
        
        for node in nodes:
            # 根节点：没有父节点或父节点不存在
            if not node.parent_id or node.parent_id not in node_dict:
                root_nodes += 1
                
            # 叶子节点：没有子节点
            if not node.children_ids:
                leaf_nodes += 1
            else:
                total_children += len(node.children_ids)
                
            # 最大深度
            if node.level is not None:
                max_depth = max(max_depth, node.level)
                
        # 平均子节点数
        non_leaf_nodes = total_nodes - leaf_nodes
        avg_children_per_node = total_children / non_leaf_nodes if non_leaf_nodes > 0 else 0
        
        return {
            'total_nodes': total_nodes,
            'root_nodes': root_nodes,
            'leaf_nodes': leaf_nodes,
            'max_depth': max_depth,
            'avg_children_per_node': round(avg_children_per_node, 2)
        }
        
    def _analyze_progress(self, nodes: List[OutlineNode]) -> Dict[str, Any]:
        """分析进度统计"""
        total_nodes = len(nodes)
        status_counts = Counter(node.status for node in nodes)
        
        completed_nodes = status_counts.get(OutlineNodeStatus.COMPLETED, 0)
        in_progress_nodes = status_counts.get(OutlineNodeStatus.IN_PROGRESS, 0)
        draft_nodes = status_counts.get(OutlineNodeStatus.DRAFT, 0)
        
        # 计算整体进度（基于节点数量）
        node_progress = completed_nodes / total_nodes if total_nodes > 0 else 0
        
        # 计算加权进度（基于字数）
        total_estimated_words = sum(node.estimated_word_count or 0 for node in nodes)
        completed_words = sum(
            node.actual_word_count or 0 for node in nodes 
            if node.status == OutlineNodeStatus.COMPLETED
        )
        word_progress = completed_words / total_estimated_words if total_estimated_words > 0 else 0
        
        # 综合进度
        overall_progress = (node_progress + word_progress) / 2
        
        return {
            'overall_progress': round(overall_progress, 3),
            'node_progress': round(node_progress, 3),
            'word_progress': round(word_progress, 3),
            'completed_nodes': completed_nodes,
            'in_progress_nodes': in_progress_nodes,
            'draft_nodes': draft_nodes,
            'completion_percentage': round(overall_progress * 100, 1)
        }
        
    def _analyze_word_count(self, nodes: List[OutlineNode]) -> Dict[str, Any]:
        """分析字数统计"""
        total_estimated_words = sum(node.estimated_word_count or 0 for node in nodes)
        total_actual_words = sum(node.actual_word_count or 0 for node in nodes)
        
        completion_ratio = total_actual_words / total_estimated_words if total_estimated_words > 0 else 0
        
        # 按类型统计字数
        type_word_stats = defaultdict(lambda: {'estimated': 0, 'actual': 0})
        for node in nodes:
            type_word_stats[node.type.value]['estimated'] += node.estimated_word_count or 0
            type_word_stats[node.type.value]['actual'] += node.actual_word_count or 0
            
        # 按状态统计字数
        status_word_stats = defaultdict(lambda: {'estimated': 0, 'actual': 0})
        for node in nodes:
            status_word_stats[node.status.value]['estimated'] += node.estimated_word_count or 0
            status_word_stats[node.status.value]['actual'] += node.actual_word_count or 0
            
        # 平均字数
        nodes_with_estimated = [node for node in nodes if node.estimated_word_count]
        nodes_with_actual = [node for node in nodes if node.actual_word_count]
        
        avg_estimated_words = (
            sum(node.estimated_word_count for node in nodes_with_estimated) / len(nodes_with_estimated)
            if nodes_with_estimated else 0
        )
        avg_actual_words = (
            sum(node.actual_word_count for node in nodes_with_actual) / len(nodes_with_actual)
            if nodes_with_actual else 0
        )
        
        return {
            'total_estimated_words': total_estimated_words,
            'total_actual_words': total_actual_words,
            'completion_ratio': round(completion_ratio, 3),
            'avg_estimated_words': round(avg_estimated_words, 0),
            'avg_actual_words': round(avg_actual_words, 0),
            'type_distribution': dict(type_word_stats),
            'status_distribution': dict(status_word_stats),
            'nodes_with_word_count': {
                'estimated': len(nodes_with_estimated),
                'actual': len(nodes_with_actual)
            }
        }
        
    def _analyze_structure(self, nodes: List[OutlineNode]) -> Dict[str, Any]:
        """分析结构统计"""
        if not nodes:
            return {'depth_distribution': {}, 'branching_factor': 0, 'balance_score': 0}
            
        # 深度分布
        depth_counts = Counter(node.level or 0 for node in nodes)
        depth_distribution = dict(depth_counts)
        
        # 分支因子（平均每个非叶子节点的子节点数）
        non_leaf_nodes = [node for node in nodes if node.children_ids]
        if non_leaf_nodes:
            total_children = sum(len(node.children_ids) for node in non_leaf_nodes)
            branching_factor = total_children / len(non_leaf_nodes)
        else:
            branching_factor = 0
            
        # 平衡度评分（基于深度分布的标准差）
        if len(depth_distribution) > 1:
            depths = list(depth_distribution.keys())
            counts = list(depth_distribution.values())
            mean_count = sum(counts) / len(counts)
            variance = sum((count - mean_count) ** 2 for count in counts) / len(counts)
            std_dev = variance ** 0.5
            # 平衡度评分：标准差越小，平衡度越高
            balance_score = max(0, 1 - (std_dev / mean_count)) if mean_count > 0 else 0
        else:
            balance_score = 1.0
            
        return {
            'depth_distribution': depth_distribution,
            'branching_factor': round(branching_factor, 2),
            'balance_score': round(balance_score, 3),
            'max_depth': max(depth_distribution.keys()) if depth_distribution else 0,
            'most_common_depth': max(depth_distribution, key=depth_distribution.get) if depth_distribution else 0
        }
        
    def _analyze_type_distribution(self, nodes: List[OutlineNode]) -> Dict[str, Any]:
        """分析类型分布"""
        type_counts = Counter(node.type.value for node in nodes)
        total_nodes = len(nodes)
        
        type_distribution = {}
        for node_type, count in type_counts.items():
            percentage = (count / total_nodes * 100) if total_nodes > 0 else 0
            type_distribution[node_type] = {
                'count': count,
                'percentage': round(percentage, 1)
            }
            
        return type_distribution
        
    def _analyze_status_distribution(self, nodes: List[OutlineNode]) -> Dict[str, Any]:
        """分析状态分布"""
        status_counts = Counter(node.status.value for node in nodes)
        total_nodes = len(nodes)
        
        status_distribution = {}
        for status, count in status_counts.items():
            percentage = (count / total_nodes * 100) if total_nodes > 0 else 0
            status_distribution[status] = {
                'count': count,
                'percentage': round(percentage, 1)
            }
            
        return status_distribution
        
    def _analyze_timeline(self, nodes: List[OutlineNode]) -> Dict[str, Any]:
        """分析时间线统计"""
        nodes_with_dates = [node for node in nodes if node.created_at]
        
        if not nodes_with_dates:
            return {
                'creation_span_days': 0,
                'avg_nodes_per_day': 0,
                'most_productive_day': None,
                'recent_activity': []
            }
            
        # 创建时间跨度
        creation_dates = [node.created_at for node in nodes_with_dates]
        earliest_date = min(creation_dates)
        latest_date = max(creation_dates)
        creation_span = (latest_date - earliest_date).days + 1
        
        # 平均每天创建的节点数
        avg_nodes_per_day = len(nodes_with_dates) / creation_span if creation_span > 0 else 0
        
        # 按日期统计节点创建数量
        daily_counts = defaultdict(int)
        for node in nodes_with_dates:
            date_key = node.created_at.date()
            daily_counts[date_key] += 1
            
        # 最高产的一天
        most_productive_day = max(daily_counts, key=daily_counts.get) if daily_counts else None
        
        # 最近7天的活动
        recent_activity = []
        today = datetime.now().date()
        for i in range(7):
            date = today - timedelta(days=i)
            count = daily_counts.get(date, 0)
            recent_activity.append({
                'date': date.isoformat(),
                'nodes_created': count
            })
            
        return {
            'creation_span_days': creation_span,
            'avg_nodes_per_day': round(avg_nodes_per_day, 2),
            'most_productive_day': most_productive_day.isoformat() if most_productive_day else None,
            'most_productive_day_count': daily_counts.get(most_productive_day, 0) if most_productive_day else 0,
            'recent_activity': recent_activity
        }
        
    def _forecast_completion(self, nodes: List[OutlineNode]) -> Dict[str, Any]:
        """预测完成时间"""
        completed_nodes = [node for node in nodes if node.status == OutlineNodeStatus.COMPLETED]
        total_nodes = len(nodes)
        
        if not completed_nodes or total_nodes == 0:
            return {
                'estimated_completion_date': None,
                'days_remaining': None,
                'completion_rate': 0,
                'confidence_level': 'low'
            }
            
        # 计算完成率（基于时间）
        nodes_with_dates = [node for node in completed_nodes if node.updated_at]
        if len(nodes_with_dates) < 2:
            return {
                'estimated_completion_date': None,
                'days_remaining': None,
                'completion_rate': 0,
                'confidence_level': 'low'
            }
            
        # 按更新时间排序
        nodes_with_dates.sort(key=lambda x: x.updated_at)
        
        # 计算最近的完成速度（节点/天）
        recent_nodes = nodes_with_dates[-min(10, len(nodes_with_dates)):]
        if len(recent_nodes) >= 2:
            time_span = (recent_nodes[-1].updated_at - recent_nodes[0].updated_at).days + 1
            completion_rate = len(recent_nodes) / time_span if time_span > 0 else 0
        else:
            completion_rate = 0
            
        # 预测剩余时间
        remaining_nodes = total_nodes - len(completed_nodes)
        if completion_rate > 0:
            days_remaining = remaining_nodes / completion_rate
            estimated_completion_date = datetime.now() + timedelta(days=days_remaining)
        else:
            days_remaining = None
            estimated_completion_date = None
            
        # 置信度评估
        confidence_level = 'low'
        if len(completed_nodes) >= 5 and completion_rate > 0:
            if len(completed_nodes) >= 20:
                confidence_level = 'high'
            else:
                confidence_level = 'medium'
                
        return {
            'estimated_completion_date': estimated_completion_date.isoformat() if estimated_completion_date else None,
            'days_remaining': round(days_remaining) if days_remaining else None,
            'completion_rate': round(completion_rate, 3),
            'confidence_level': confidence_level,
            'completed_nodes': len(completed_nodes),
            'remaining_nodes': remaining_nodes
        }
        
    def _analyze_quality_metrics(self, nodes: List[OutlineNode]) -> Dict[str, Any]:
        """分析质量指标"""
        total_nodes = len(nodes)
        if total_nodes == 0:
            return {}
            
        # 内容完整性
        nodes_with_content = len([node for node in nodes if node.content and node.content.strip()])
        content_completeness = nodes_with_content / total_nodes
        
        # 标题质量（非空且不是默认值）
        nodes_with_good_titles = len([
            node for node in nodes 
            if node.title and node.title.strip() and node.title != '未命名'
        ])
        title_quality = nodes_with_good_titles / total_nodes
        
        # 字数估算覆盖率
        nodes_with_word_estimate = len([node for node in nodes if node.estimated_word_count])
        word_estimate_coverage = nodes_with_word_estimate / total_nodes
        
        # 标签使用率
        nodes_with_tags = len([node for node in nodes if node.tags])
        tag_usage_rate = nodes_with_tags / total_nodes
        
        # 关联信息完整性
        nodes_with_relations = len([
            node for node in nodes 
            if node.chapter_id or node.character_ids or node.scene_ids
        ])
        relation_completeness = nodes_with_relations / total_nodes
        
        # 综合质量评分
        quality_score = (
            content_completeness * 0.3 +
            title_quality * 0.2 +
            word_estimate_coverage * 0.2 +
            tag_usage_rate * 0.15 +
            relation_completeness * 0.15
        )
        
        return {
            'quality_score': round(quality_score, 3),
            'content_completeness': round(content_completeness, 3),
            'title_quality': round(title_quality, 3),
            'word_estimate_coverage': round(word_estimate_coverage, 3),
            'tag_usage_rate': round(tag_usage_rate, 3),
            'relation_completeness': round(relation_completeness, 3),
            'nodes_with_content': nodes_with_content,
            'nodes_with_good_titles': nodes_with_good_titles,
            'nodes_with_word_estimate': nodes_with_word_estimate,
            'nodes_with_tags': nodes_with_tags,
            'nodes_with_relations': nodes_with_relations
        }
        
    def _generate_recommendations(self, nodes: List[OutlineNode]) -> List[Dict[str, Any]]:
        """生成改进建议"""
        recommendations = []
        
        if not nodes:
            return recommendations
            
        # 分析各项指标
        progress_stats = self._analyze_progress(nodes)
        word_stats = self._analyze_word_count(nodes)
        structure_stats = self._analyze_structure(nodes)
        quality_stats = self._analyze_quality_metrics(nodes)
        
        # 进度相关建议
        if progress_stats['overall_progress'] < 0.1:
            recommendations.append({
                'type': 'progress',
                'priority': 'high',
                'title': '开始执行大纲',
                'description': '大纲进度较低，建议开始执行具体的写作任务',
                'action': '将一些节点状态更新为"进行中"并开始写作'
            })
        elif progress_stats['overall_progress'] < 0.5:
            recommendations.append({
                'type': 'progress',
                'priority': 'medium',
                'title': '加快执行进度',
                'description': '当前进度良好，建议保持节奏并适当加快进度',
                'action': '设定每日或每周的写作目标'
            })
            
        # 字数相关建议
        if word_stats['nodes_with_word_count']['estimated'] < len(nodes) * 0.5:
            recommendations.append({
                'type': 'planning',
                'priority': 'medium',
                'title': '完善字数规划',
                'description': '许多节点缺少字数估算，这会影响进度跟踪',
                'action': '为更多节点添加预计字数'
            })
            
        # 结构相关建议
        if structure_stats['balance_score'] < 0.5:
            recommendations.append({
                'type': 'structure',
                'priority': 'medium',
                'title': '优化大纲结构',
                'description': '大纲结构不够平衡，某些层级节点过多或过少',
                'action': '重新组织节点层级，使结构更加均衡'
            })
            
        if structure_stats['max_depth'] > 6:
            recommendations.append({
                'type': 'structure',
                'priority': 'low',
                'title': '简化层级结构',
                'description': '大纲层级过深，可能影响可读性',
                'action': '考虑合并或重组深层级的节点'
            })
            
        # 质量相关建议
        if quality_stats.get('content_completeness', 0) < 0.6:
            recommendations.append({
                'type': 'quality',
                'priority': 'high',
                'title': '完善节点内容',
                'description': '许多节点缺少详细内容描述',
                'action': '为空白节点添加内容描述和详细说明'
            })
            
        if quality_stats.get('tag_usage_rate', 0) < 0.3:
            recommendations.append({
                'type': 'organization',
                'priority': 'low',
                'title': '增加标签使用',
                'description': '标签使用率较低，不利于内容组织和检索',
                'action': '为节点添加相关标签，如主题、情节线等'
            })
            
        # 关联信息建议
        if quality_stats.get('relation_completeness', 0) < 0.4:
            recommendations.append({
                'type': 'organization',
                'priority': 'medium',
                'title': '完善关联信息',
                'description': '节点间的关联信息不够完整',
                'action': '添加章节、角色、场景等关联信息'
            })
            
        return recommendations
        
    def generate_summary_report(self, nodes: List[OutlineNode]) -> str:
        """生成摘要报告"""
        analysis = self.analyze_outline(nodes)
        
        report_lines = []
        report_lines.append("# 大纲分析报告")
        report_lines.append("")
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 基本统计
        basic = analysis['basic_stats']
        report_lines.append("## 基本统计")
        report_lines.append(f"- 总节点数: {basic['total_nodes']}")
        report_lines.append(f"- 根节点数: {basic['root_nodes']}")
        report_lines.append(f"- 叶子节点数: {basic['leaf_nodes']}")
        report_lines.append(f"- 最大深度: {basic['max_depth']}")
        report_lines.append(f"- 平均分支数: {basic['avg_children_per_node']}")
        report_lines.append("")
        
        # 进度统计
        progress = analysis['progress_stats']
        report_lines.append("## 进度统计")
        report_lines.append(f"- 整体进度: {progress['completion_percentage']}%")
        report_lines.append(f"- 已完成: {progress['completed_nodes']} 节点")
        report_lines.append(f"- 进行中: {progress['in_progress_nodes']} 节点")
        report_lines.append(f"- 草稿: {progress['draft_nodes']} 节点")
        report_lines.append("")
        
        # 字数统计
        words = analysis['word_count_stats']
        report_lines.append("## 字数统计")
        report_lines.append(f"- 预计总字数: {words['total_estimated_words']:,}")
        report_lines.append(f"- 实际总字数: {words['total_actual_words']:,}")
        report_lines.append(f"- 完成比例: {words['completion_ratio']:.1%}")
        report_lines.append(f"- 平均预计字数: {words['avg_estimated_words']:,.0f}")
        report_lines.append("")
        
        # 质量指标
        quality = analysis['quality_metrics']
        if quality:
            report_lines.append("## 质量指标")
            report_lines.append(f"- 综合质量评分: {quality['quality_score']:.1%}")
            report_lines.append(f"- 内容完整性: {quality['content_completeness']:.1%}")
            report_lines.append(f"- 标题质量: {quality['title_quality']:.1%}")
            report_lines.append(f"- 字数估算覆盖率: {quality['word_estimate_coverage']:.1%}")
            report_lines.append("")
            
        # 改进建议
        recommendations = analysis['recommendations']
        if recommendations:
            report_lines.append("## 改进建议")
            for i, rec in enumerate(recommendations[:5], 1):  # 只显示前5个建议
                report_lines.append(f"{i}. **{rec['title']}** ({rec['priority']}优先级)")
                report_lines.append(f"   {rec['description']}")
                report_lines.append(f"   建议行动: {rec['action']}")
                report_lines.append("")
                
        return "\n".join(report_lines)


# 全局分析器实例
_outline_analyzer = None


def get_outline_analyzer() -> OutlineAnalyzer:
    """获取大纲分析器实例"""
    global _outline_analyzer
    if _outline_analyzer is None:
        _outline_analyzer = OutlineAnalyzer()
    return _outline_analyzer