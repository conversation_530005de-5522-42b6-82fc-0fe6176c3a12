# -*- coding: utf-8 -*-
"""
性能监控系统

监控应用性能指标，包括内存使用、CPU使用、数据库查询性能等
"""

import logging
import time
import threading
import psutil
import gc
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from functools import wraps
from collections import deque, defaultdict
import weakref

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """性能指标"""
    name: str
    value: float
    unit: str
    timestamp: datetime = field(default_factory=datetime.now)
    category: str = "general"
    tags: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'name': self.name,
            'value': self.value,
            'unit': self.unit,
            'timestamp': self.timestamp.isoformat(),
            'category': self.category,
            'tags': self.tags
        }


@dataclass
class FunctionPerformance:
    """函数性能统计"""
    function_name: str
    call_count: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    last_call: Optional[datetime] = None
    error_count: int = 0
    
    @property
    def average_time(self) -> float:
        """平均执行时间"""
        if self.call_count == 0:
            return 0.0
        return self.total_time / self.call_count
        
    def add_call(self, execution_time: float, error: bool = False):
        """添加调用记录"""
        self.call_count += 1
        self.total_time += execution_time
        self.min_time = min(self.min_time, execution_time)
        self.max_time = max(self.max_time, execution_time)
        self.last_call = datetime.now()
        
        if error:
            self.error_count += 1
            
    def to_dict(self) -> Dict[str, Any]:
        return {
            'function_name': self.function_name,
            'call_count': self.call_count,
            'total_time': self.total_time,
            'average_time': self.average_time,
            'min_time': self.min_time if self.min_time != float('inf') else 0.0,
            'max_time': self.max_time,
            'last_call': self.last_call.isoformat() if self.last_call else None,
            'error_count': self.error_count,
            'error_rate': self.error_count / self.call_count if self.call_count > 0 else 0.0
        }


@dataclass
class SystemMetrics:
    """系统指标"""
    cpu_percent: float
    memory_percent: float
    memory_used: int  # 字节
    memory_available: int  # 字节
    disk_usage_percent: float
    disk_free: int  # 字节
    network_sent: int  # 字节
    network_recv: int  # 字节
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'cpu_percent': self.cpu_percent,
            'memory_percent': self.memory_percent,
            'memory_used_mb': self.memory_used / (1024 * 1024),
            'memory_available_mb': self.memory_available / (1024 * 1024),
            'disk_usage_percent': self.disk_usage_percent,
            'disk_free_gb': self.disk_free / (1024 * 1024 * 1024),
            'network_sent_mb': self.network_sent / (1024 * 1024),
            'network_recv_mb': self.network_recv / (1024 * 1024),
            'timestamp': self.timestamp.isoformat()
        }


class PerformanceCollector:
    """性能数据收集器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics: deque = deque(maxlen=max_history)
        self.function_stats: Dict[str, FunctionPerformance] = {}
        self.system_metrics: deque = deque(maxlen=max_history)
        self._lock = threading.RLock()
        self._process = psutil.Process()
        
    def add_metric(self, metric: PerformanceMetric):
        """添加性能指标"""
        with self._lock:
            self.metrics.append(metric)
            
    def add_function_call(self, function_name: str, execution_time: float, error: bool = False):
        """添加函数调用记录"""
        with self._lock:
            if function_name not in self.function_stats:
                self.function_stats[function_name] = FunctionPerformance(function_name)
                
            self.function_stats[function_name].add_call(execution_time, error)
            
    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = self._process.cpu_percent()
            
            # 内存使用
            memory_info = self._process.memory_info()
            system_memory = psutil.virtual_memory()
            
            # 磁盘使用
            disk_usage = psutil.disk_usage('/')
            
            # 网络IO
            net_io = psutil.net_io_counters()
            
            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=system_memory.percent,
                memory_used=memory_info.rss,
                memory_available=system_memory.available,
                disk_usage_percent=disk_usage.percent,
                disk_free=disk_usage.free,
                network_sent=net_io.bytes_sent if net_io else 0,
                network_recv=net_io.bytes_recv if net_io else 0
            )
            
            with self._lock:
                self.system_metrics.append(metrics)
                
            return metrics
            
        except Exception as e:
            logger.error(f"收集系统指标失败: {e}")
            return SystemMetrics(0, 0, 0, 0, 0, 0, 0, 0)
            
    def get_function_stats(self, function_name: str = None) -> Dict[str, Any]:
        """获取函数统计信息"""
        with self._lock:
            if function_name:
                stats = self.function_stats.get(function_name)
                return stats.to_dict() if stats else {}
            else:
                return {name: stats.to_dict() for name, stats in self.function_stats.items()}
                
    def get_metrics_by_category(self, category: str, 
                              since: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """按类别获取指标"""
        with self._lock:
            filtered_metrics = []
            for metric in self.metrics:
                if metric.category == category:
                    if since is None or metric.timestamp >= since:
                        filtered_metrics.append(metric.to_dict())
            return filtered_metrics
            
    def get_recent_system_metrics(self, count: int = 10) -> List[Dict[str, Any]]:
        """获取最近的系统指标"""
        with self._lock:
            recent_metrics = list(self.system_metrics)[-count:]
            return [metric.to_dict() for metric in recent_metrics]
            
    def clear_old_data(self, older_than: timedelta = timedelta(hours=24)):
        """清理旧数据"""
        cutoff_time = datetime.now() - older_than
        
        with self._lock:
            # 清理旧指标
            self.metrics = deque(
                (m for m in self.metrics if m.timestamp >= cutoff_time),
                maxlen=self.max_history
            )
            
            # 清理旧系统指标
            self.system_metrics = deque(
                (m for m in self.system_metrics if m.timestamp >= cutoff_time),
                maxlen=self.max_history
            )


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, collection_interval: int = 60):
        self.collection_interval = collection_interval  # 秒
        self.collector = PerformanceCollector()
        self.alerts: List[Dict[str, Any]] = []
        self.thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_usage_percent': 90.0,
            'function_avg_time': 5.0,  # 秒
            'function_error_rate': 0.1  # 10%
        }
        self._monitoring_thread = None
        self._stop_monitoring = threading.Event()
        self._alert_callbacks: List[Callable] = []
        
    def start_monitoring(self):
        """开始监控"""
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            return
            
        self._stop_monitoring.clear()
        self._monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self._monitoring_thread.start()
        logger.info("性能监控已启动")
        
    def stop_monitoring(self):
        """停止监控"""
        self._stop_monitoring.set()
        if self._monitoring_thread:
            self._monitoring_thread.join(timeout=5)
        logger.info("性能监控已停止")
        
    def _monitoring_loop(self):
        """监控循环"""
        while not self._stop_monitoring.wait(self.collection_interval):
            try:
                # 收集系统指标
                system_metrics = self.collector.collect_system_metrics()
                
                # 检查阈值并生成告警
                self._check_thresholds(system_metrics)
                
                # 清理旧数据
                self.collector.clear_old_data()
                
            except Exception as e:
                logger.error(f"性能监控循环错误: {e}")
                
    def _check_thresholds(self, system_metrics: SystemMetrics):
        """检查阈值"""
        alerts = []
        
        # 检查CPU使用率
        if system_metrics.cpu_percent > self.thresholds['cpu_percent']:
            alerts.append({
                'type': 'cpu_high',
                'message': f'CPU使用率过高: {system_metrics.cpu_percent:.1f}%',
                'value': system_metrics.cpu_percent,
                'threshold': self.thresholds['cpu_percent'],
                'timestamp': datetime.now()
            })
            
        # 检查内存使用率
        if system_metrics.memory_percent > self.thresholds['memory_percent']:
            alerts.append({
                'type': 'memory_high',
                'message': f'内存使用率过高: {system_metrics.memory_percent:.1f}%',
                'value': system_metrics.memory_percent,
                'threshold': self.thresholds['memory_percent'],
                'timestamp': datetime.now()
            })
            
        # 检查磁盘使用率
        if system_metrics.disk_usage_percent > self.thresholds['disk_usage_percent']:
            alerts.append({
                'type': 'disk_high',
                'message': f'磁盘使用率过高: {system_metrics.disk_usage_percent:.1f}%',
                'value': system_metrics.disk_usage_percent,
                'threshold': self.thresholds['disk_usage_percent'],
                'timestamp': datetime.now()
            })
            
        # 检查函数性能
        for func_name, func_stats in self.collector.function_stats.items():
            # 检查平均执行时间
            if func_stats.average_time > self.thresholds['function_avg_time']:
                alerts.append({
                    'type': 'function_slow',
                    'message': f'函数执行缓慢: {func_name} 平均耗时 {func_stats.average_time:.2f}s',
                    'function': func_name,
                    'value': func_stats.average_time,
                    'threshold': self.thresholds['function_avg_time'],
                    'timestamp': datetime.now()
                })
                
            # 检查错误率
            error_rate = func_stats.error_count / func_stats.call_count if func_stats.call_count > 0 else 0
            if error_rate > self.thresholds['function_error_rate']:
                alerts.append({
                    'type': 'function_error_rate_high',
                    'message': f'函数错误率过高: {func_name} 错误率 {error_rate:.1%}',
                    'function': func_name,
                    'value': error_rate,
                    'threshold': self.thresholds['function_error_rate'],
                    'timestamp': datetime.now()
                })
                
        # 处理告警
        for alert in alerts:
            self._handle_alert(alert)
            
    def _handle_alert(self, alert: Dict[str, Any]):
        """处理告警"""
        self.alerts.append(alert)
        
        # 限制告警历史数量
        if len(self.alerts) > 1000:
            self.alerts = self.alerts[-500:]
            
        # 调用告警回调
        for callback in self._alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"告警回调执行失败: {e}")
                
        logger.warning(alert['message'])
        
    def add_alert_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加告警回调"""
        self._alert_callbacks.append(callback)
        
    def set_threshold(self, metric: str, value: float):
        """设置阈值"""
        self.thresholds[metric] = value
        
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        # 获取最近的系统指标
        recent_metrics = self.collector.get_recent_system_metrics(10)
        
        # 计算平均值
        if recent_metrics:
            avg_cpu = sum(m['cpu_percent'] for m in recent_metrics) / len(recent_metrics)
            avg_memory = sum(m['memory_percent'] for m in recent_metrics) / len(recent_metrics)
            avg_disk = sum(m['disk_usage_percent'] for m in recent_metrics) / len(recent_metrics)
        else:
            avg_cpu = avg_memory = avg_disk = 0
            
        # 获取函数统计
        function_stats = self.collector.get_function_stats()
        
        # 获取最近的告警
        recent_alerts = [alert for alert in self.alerts 
                        if alert['timestamp'] > datetime.now() - timedelta(hours=1)]
        
        return {
            'system_metrics': {
                'avg_cpu_percent': avg_cpu,
                'avg_memory_percent': avg_memory,
                'avg_disk_usage_percent': avg_disk,
                'recent_metrics': recent_metrics
            },
            'function_performance': function_stats,
            'alerts': {
                'recent_count': len(recent_alerts),
                'total_count': len(self.alerts),
                'recent_alerts': [{
                    'type': alert['type'],
                    'message': alert['message'],
                    'timestamp': alert['timestamp'].isoformat()
                } for alert in recent_alerts[-10:]]
            },
            'thresholds': self.thresholds,
            'monitoring_status': 'running' if self._monitoring_thread and self._monitoring_thread.is_alive() else 'stopped'
        }
        
    def force_gc(self) -> Dict[str, Any]:
        """强制垃圾回收"""
        before_memory = psutil.Process().memory_info().rss
        
        # 执行垃圾回收
        collected = gc.collect()
        
        after_memory = psutil.Process().memory_info().rss
        freed_memory = before_memory - after_memory
        
        result = {
            'collected_objects': collected,
            'freed_memory_mb': freed_memory / (1024 * 1024),
            'before_memory_mb': before_memory / (1024 * 1024),
            'after_memory_mb': after_memory / (1024 * 1024)
        }
        
        logger.info(f"垃圾回收完成: 回收 {collected} 个对象, 释放 {result['freed_memory_mb']:.2f} MB 内存")
        return result


def performance_monitor(category: str = "function", 
                      include_args: bool = False,
                      include_result: bool = False):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            error_occurred = False
            result = None
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                error_occurred = True
                raise
            finally:
                execution_time = time.time() - start_time
                
                # 记录性能数据
                monitor = get_performance_monitor()
                monitor.collector.add_function_call(
                    func.__name__, 
                    execution_time, 
                    error_occurred
                )
                
                # 添加详细指标
                tags = {'function': func.__name__}
                if include_args and args:
                    tags['args_count'] = str(len(args))
                if include_result and result is not None:
                    tags['has_result'] = 'true'
                    
                metric = PerformanceMetric(
                    name=f"{func.__name__}_execution_time",
                    value=execution_time,
                    unit="seconds",
                    category=category,
                    tags=tags
                )
                
                monitor.collector.add_metric(metric)
                
        return wrapper
    return decorator


def memory_profiler(func: Callable) -> Callable:
    """内存分析装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        process = psutil.Process()
        before_memory = process.memory_info().rss
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            after_memory = process.memory_info().rss
            memory_diff = after_memory - before_memory
            
            # 记录内存使用变化
            monitor = get_performance_monitor()
            metric = PerformanceMetric(
                name=f"{func.__name__}_memory_usage",
                value=memory_diff / (1024 * 1024),  # MB
                unit="MB",
                category="memory",
                tags={'function': func.__name__}
            )
            
            monitor.collector.add_metric(metric)
            
            if memory_diff > 10 * 1024 * 1024:  # 超过10MB
                logger.warning(f"函数 {func.__name__} 内存使用增加 {memory_diff / (1024 * 1024):.2f} MB")
                
    return wrapper


class DatabaseQueryMonitor:
    """数据库查询监控"""
    
    def __init__(self):
        self.query_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0.0,
            'min_time': float('inf'),
            'max_time': 0.0,
            'errors': 0
        })
        self._lock = threading.RLock()
        
    def record_query(self, query_type: str, execution_time: float, error: bool = False):
        """记录查询"""
        with self._lock:
            stats = self.query_stats[query_type]
            stats['count'] += 1
            stats['total_time'] += execution_time
            stats['min_time'] = min(stats['min_time'], execution_time)
            stats['max_time'] = max(stats['max_time'], execution_time)
            
            if error:
                stats['errors'] += 1
                
    def get_query_stats(self) -> Dict[str, Any]:
        """获取查询统计"""
        with self._lock:
            result = {}
            for query_type, stats in self.query_stats.items():
                result[query_type] = {
                    'count': stats['count'],
                    'total_time': stats['total_time'],
                    'average_time': stats['total_time'] / stats['count'] if stats['count'] > 0 else 0,
                    'min_time': stats['min_time'] if stats['min_time'] != float('inf') else 0,
                    'max_time': stats['max_time'],
                    'error_count': stats['errors'],
                    'error_rate': stats['errors'] / stats['count'] if stats['count'] > 0 else 0
                }
            return result
            
    def clear_stats(self):
        """清空统计"""
        with self._lock:
            self.query_stats.clear()


def database_monitor(query_type: str):
    """数据库查询监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            error_occurred = False
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                error_occurred = True
                raise
            finally:
                execution_time = time.time() - start_time
                
                # 记录查询性能
                db_monitor = get_db_monitor()
                db_monitor.record_query(query_type, execution_time, error_occurred)
                
                # 记录到性能监控器
                monitor = get_performance_monitor()
                metric = PerformanceMetric(
                    name=f"db_query_{query_type}",
                    value=execution_time,
                    unit="seconds",
                    category="database",
                    tags={'query_type': query_type, 'function': func.__name__}
                )
                
                monitor.collector.add_metric(metric)
                
        return wrapper
    return decorator


# 全局实例
_performance_monitor: Optional[PerformanceMonitor] = None
_db_monitor: Optional[DatabaseQueryMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器实例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor


def get_db_monitor() -> DatabaseQueryMonitor:
    """获取数据库监控器实例"""
    global _db_monitor
    if _db_monitor is None:
        _db_monitor = DatabaseQueryMonitor()
    return _db_monitor


def initialize_performance_monitoring(collection_interval: int = 60, 
                                    auto_start: bool = True):
    """初始化性能监控"""
    global _performance_monitor, _db_monitor
    
    _performance_monitor = PerformanceMonitor(collection_interval)
    _db_monitor = DatabaseQueryMonitor()
    
    if auto_start:
        _performance_monitor.start_monitoring()
        
    logger.info("性能监控系统初始化完成")


def shutdown_performance_monitoring():
    """关闭性能监控"""
    global _performance_monitor, _db_monitor
    
    if _performance_monitor:
        _performance_monitor.stop_monitoring()
        
    _performance_monitor = None
    _db_monitor = None
    
    logger.info("性能监控系统已关闭")