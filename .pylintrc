[MASTER]
# Python代码分析配置文件

# 指定Python解释器路径
#py-version = 3.9

# 并行作业数量
jobs = 4

# 持久化缓存
persistent = yes

# 扩展加载
#load-plugins = 

# 输出格式
#output-format = text

# 报告y/n
reports = no

# 评估表达式
evaluation = 10.0 - ((float(5 * error + warning + refactor + convention) / statement) * 10)

[MESSAGES CONTROL]
# 禁用的消息
disable = 
    missing-docstring,
    too-few-public-methods,
    too-many-arguments,
    too-many-instance-attributes,
    too-many-locals,
    too-many-branches,
    too-many-statements,
    too-many-public-methods,
    line-too-long,
    invalid-name,
    broad-except,
    import-error,
    no-name-in-module

[REPORTS]
# 设置输出格式
output-format = colorized

# 在文件中包含消息的id
include-ids = yes

# 在文件中包含符号名称
include-symbols = no

[BASIC]
# 良好的变量名正则表达式
good-names = i,j,k,ex,Run,_,id,ui,db,ai,x,y,z

# 不好的变量名正则表达式
bad-names = foo,bar,baz,toto,tutu,tata

# 常量名正则表达式
const-rgx = (([A-Z_][A-Z0-9_]*)|(__.*__))$

# 类名正则表达式
class-rgx = [A-Z_][a-zA-Z0-9]+$

# 函数名正则表达式
function-rgx = [a-z_][a-z0-9_]{2,30}$

# 方法名正则表达式
method-rgx = [a-z_][a-z0-9_]{2,30}$

# 模块名正则表达式
module-rgx = (([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+))$

# 变量名正则表达式
variable-rgx = [a-z_][a-z0-9_]{2,30}$

# 参数名正则表达式
argument-rgx = [a-z_][a-z0-9_]{2,30}$

# 属性名正则表达式
attr-rgx = [a-z_][a-z0-9_]{2,30}$

[FORMAT]
# 最大行长度
max-line-length = 88

# 字符串中最大行长度
max-module-lines = 1000

# 缩进字符串
indent-string = '    '

[DESIGN]
# 最大参数数量
max-args = 5

# 最大局部变量数量
max-locals = 15

# 最大返回语句数量
max-returns = 6

# 最大分支数量
max-branches = 12

# 最大语句数量
max-statements = 50

# 最大属性数量
max-attributes = 7

# 最大公共方法数量
max-public-methods = 20

[SIMILARITIES]
# 最小相似行数
min-similarity-lines = 4

# 忽略注释
ignore-comments = yes

# 忽略文档字符串
ignore-docstrings = yes

# 忽略导入
ignore-imports = no

[TYPECHECK]
# 告诉pylint哪些包或模块可用
ignored-modules = PyQt6

# 忽略的类
ignored-classes = SQLObject, optparse.Values

[VARIABLES]
# 告诉pylint哪些变量名是可以的
init-import = no

# 虚拟变量的正则表达式
dummy-variables-rgx = _+$|(_[a-zA-Z0-9_]*[a-zA-Z0-9]+?$)|dummy

[CLASSES]
# 有效的元类
valid-metaclasses = abc.ABCMeta

[IMPORTS]
# 禁止通配符导入
deprecated-modules = regsub,string,TERMIOS,Bastion,rexec