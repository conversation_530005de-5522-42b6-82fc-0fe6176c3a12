# -*- coding: utf-8 -*-
"""
数据库连接池管理器

提供高效的数据库连接池管理，支持连接复用、自动重连、连接健康检查等功能
"""

import logging
import threading
import time
import queue
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from contextlib import contextmanager
from sqlalchemy import create_engine, text, event
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool, StaticPool
from sqlalchemy.exc import DisconnectionError, OperationalError
import weakref

logger = logging.getLogger(__name__)


@dataclass
class ConnectionStats:
    """连接统计信息"""
    total_connections: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    failed_connections: int = 0
    total_queries: int = 0
    failed_queries: int = 0
    average_query_time: float = 0.0
    peak_connections: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    last_reset: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'total_connections': self.total_connections,
            'active_connections': self.active_connections,
            'idle_connections': self.idle_connections,
            'failed_connections': self.failed_connections,
            'total_queries': self.total_queries,
            'failed_queries': self.failed_queries,
            'average_query_time': self.average_query_time,
            'peak_connections': self.peak_connections,
            'success_rate': (self.total_queries - self.failed_queries) / max(self.total_queries, 1),
            'created_at': self.created_at.isoformat(),
            'last_reset': self.last_reset.isoformat()
        }


@dataclass
class PoolConfig:
    """连接池配置"""
    database_url: str
    pool_size: int = 10
    max_overflow: int = 20
    pool_timeout: int = 30
    pool_recycle: int = 3600  # 1小时
    pool_pre_ping: bool = True
    echo: bool = False
    connect_args: Dict[str, Any] = field(default_factory=dict)
    
    def to_engine_kwargs(self) -> Dict[str, Any]:
        """转换为SQLAlchemy引擎参数"""
        return {
            'poolclass': QueuePool,
            'pool_size': self.pool_size,
            'max_overflow': self.max_overflow,
            'pool_timeout': self.pool_timeout,
            'pool_recycle': self.pool_recycle,
            'pool_pre_ping': self.pool_pre_ping,
            'echo': self.echo,
            'connect_args': self.connect_args
        }


class ConnectionWrapper:
    """连接包装器"""
    
    def __init__(self, session: Session, pool_manager: 'DatabasePoolManager'):
        self.session = session
        self.pool_manager = pool_manager
        self.created_at = datetime.now()
        self.last_used = datetime.now()
        self.query_count = 0
        self.is_active = True
        
    def execute(self, query, params=None):
        """执行查询"""
        start_time = time.time()
        try:
            self.last_used = datetime.now()
            self.query_count += 1
            
            if params:
                result = self.session.execute(text(query), params)
            else:
                result = self.session.execute(text(query))
                
            execution_time = time.time() - start_time
            self.pool_manager._record_query_success(execution_time)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.pool_manager._record_query_failure(execution_time)
            logger.error(f"查询执行失败: {e}")
            raise
            
    def commit(self):
        """提交事务"""
        try:
            self.session.commit()
        except Exception as e:
            logger.error(f"事务提交失败: {e}")
            self.session.rollback()
            raise
            
    def rollback(self):
        """回滚事务"""
        try:
            self.session.rollback()
        except Exception as e:
            logger.error(f"事务回滚失败: {e}")
            
    def close(self):
        """关闭连接"""
        if self.is_active:
            try:
                self.session.close()
                self.is_active = False
                self.pool_manager._record_connection_closed()
            except Exception as e:
                logger.error(f"关闭连接失败: {e}")
                
    def is_healthy(self) -> bool:
        """检查连接健康状态"""
        try:
            # 执行简单查询测试连接
            self.session.execute(text("SELECT 1"))
            return True
        except Exception:
            return False
            
    def __enter__(self):
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.rollback()
        else:
            self.commit()
        self.close()


class DatabasePoolManager:
    """数据库连接池管理器"""
    
    def __init__(self, config: PoolConfig):
        self.config = config
        self.engine: Optional[Engine] = None
        self.session_factory: Optional[sessionmaker] = None
        self.stats = ConnectionStats()
        self._lock = threading.RLock()
        self._active_connections: Dict[int, ConnectionWrapper] = {}
        self._health_check_thread: Optional[threading.Thread] = None
        self._stop_health_check = threading.Event()
        self._query_times: List[float] = []
        self._max_query_history = 1000
        
    def initialize(self):
        """初始化连接池"""
        try:
            # 创建引擎
            engine_kwargs = self.config.to_engine_kwargs()
            self.engine = create_engine(self.config.database_url, **engine_kwargs)
            
            # 添加事件监听器
            self._setup_event_listeners()
            
            # 创建会话工厂
            self.session_factory = sessionmaker(bind=self.engine)
            
            # 测试连接
            self._test_connection()
            
            # 启动健康检查
            self._start_health_check()
            
            logger.info(f"数据库连接池初始化成功: {self.config.database_url}")
            
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise
            
    def _setup_event_listeners(self):
        """设置事件监听器"""
        @event.listens_for(self.engine, "connect")
        def on_connect(dbapi_connection, connection_record):
            with self._lock:
                self.stats.total_connections += 1
                self.stats.active_connections += 1
                self.stats.peak_connections = max(
                    self.stats.peak_connections, 
                    self.stats.active_connections
                )
                
        @event.listens_for(self.engine, "close")
        def on_close(dbapi_connection, connection_record):
            with self._lock:
                self.stats.active_connections = max(0, self.stats.active_connections - 1)
                
        @event.listens_for(self.engine, "invalid")
        def on_invalid(dbapi_connection, connection_record, exception):
            with self._lock:
                self.stats.failed_connections += 1
                
    def _test_connection(self):
        """测试数据库连接"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("数据库连接测试成功")
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            raise
            
    def _start_health_check(self):
        """启动健康检查线程"""
        if self._health_check_thread and self._health_check_thread.is_alive():
            return
            
        self._stop_health_check.clear()
        self._health_check_thread = threading.Thread(
            target=self._health_check_loop, 
            daemon=True
        )
        self._health_check_thread.start()
        
    def _health_check_loop(self):
        """健康检查循环"""
        while not self._stop_health_check.wait(60):  # 每分钟检查一次
            try:
                self._perform_health_check()
            except Exception as e:
                logger.error(f"健康检查失败: {e}")
                
    def _perform_health_check(self):
        """执行健康检查"""
        with self._lock:
            # 检查活跃连接
            unhealthy_connections = []
            for conn_id, conn_wrapper in self._active_connections.items():
                if not conn_wrapper.is_healthy():
                    unhealthy_connections.append(conn_id)
                    
            # 清理不健康的连接
            for conn_id in unhealthy_connections:
                conn_wrapper = self._active_connections.pop(conn_id, None)
                if conn_wrapper:
                    conn_wrapper.close()
                    logger.warning(f"清理不健康连接: {conn_id}")
                    
        # 测试连接池
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
        except Exception as e:
            logger.error(f"连接池健康检查失败: {e}")
            
    def get_connection(self) -> ConnectionWrapper:
        """获取数据库连接"""
        try:
            session = self.session_factory()
            conn_wrapper = ConnectionWrapper(session, self)
            
            with self._lock:
                conn_id = id(conn_wrapper)
                self._active_connections[conn_id] = conn_wrapper
                
            return conn_wrapper
            
        except Exception as e:
            with self._lock:
                self.stats.failed_connections += 1
            logger.error(f"获取数据库连接失败: {e}")
            raise
            
    @contextmanager
    def get_session(self):
        """获取数据库会话上下文管理器"""
        conn = self.get_connection()
        try:
            yield conn
        finally:
            conn.close()
            
    def _record_connection_closed(self):
        """记录连接关闭"""
        with self._lock:
            # 从活跃连接中移除
            conn_to_remove = None
            for conn_id, conn_wrapper in self._active_connections.items():
                if not conn_wrapper.is_active:
                    conn_to_remove = conn_id
                    break
                    
            if conn_to_remove:
                self._active_connections.pop(conn_to_remove, None)
                
    def _record_query_success(self, execution_time: float):
        """记录查询成功"""
        with self._lock:
            self.stats.total_queries += 1
            self._query_times.append(execution_time)
            
            # 限制历史记录数量
            if len(self._query_times) > self._max_query_history:
                self._query_times = self._query_times[-self._max_query_history//2:]
                
            # 更新平均查询时间
            self.stats.average_query_time = sum(self._query_times) / len(self._query_times)
            
    def _record_query_failure(self, execution_time: float):
        """记录查询失败"""
        with self._lock:
            self.stats.total_queries += 1
            self.stats.failed_queries += 1
            self._query_times.append(execution_time)
            
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._lock:
            # 更新当前活跃连接数
            self.stats.active_connections = len(self._active_connections)
            
            # 获取引擎池信息
            pool_info = {}
            if self.engine and hasattr(self.engine.pool, 'size'):
                pool = self.engine.pool
                pool_info = {
                    'pool_size': pool.size(),
                    'checked_in': pool.checkedin(),
                    'checked_out': pool.checkedout(),
                    'overflow': pool.overflow(),
                    'invalid': pool.invalid()
                }
                
            return {
                **self.stats.to_dict(),
                'pool_info': pool_info,
                'config': {
                    'pool_size': self.config.pool_size,
                    'max_overflow': self.config.max_overflow,
                    'pool_timeout': self.config.pool_timeout,
                    'pool_recycle': self.config.pool_recycle
                }
            }
            
    def reset_stats(self):
        """重置统计信息"""
        with self._lock:
            self.stats = ConnectionStats()
            self._query_times.clear()
            logger.info("连接池统计信息已重置")
            
    def close_all_connections(self):
        """关闭所有连接"""
        with self._lock:
            # 关闭所有活跃连接
            for conn_wrapper in list(self._active_connections.values()):
                try:
                    conn_wrapper.close()
                except Exception as e:
                    logger.error(f"关闭连接失败: {e}")
                    
            self._active_connections.clear()
            
        logger.info("所有数据库连接已关闭")
        
    def shutdown(self):
        """关闭连接池"""
        # 停止健康检查
        self._stop_health_check.set()
        if self._health_check_thread:
            self._health_check_thread.join(timeout=5)
            
        # 关闭所有连接
        self.close_all_connections()
        
        # 关闭引擎
        if self.engine:
            self.engine.dispose()
            
        logger.info("数据库连接池已关闭")
        
    def execute_batch(self, queries: List[Dict[str, Any]]) -> List[Any]:
        """批量执行查询"""
        results = []
        
        with self.get_session() as conn:
            try:
                for query_info in queries:
                    query = query_info.get('query')
                    params = query_info.get('params')
                    
                    if not query:
                        continue
                        
                    result = conn.execute(query, params)
                    results.append(result)
                    
                conn.commit()
                
            except Exception as e:
                conn.rollback()
                logger.error(f"批量查询执行失败: {e}")
                raise
                
        return results
        
    def get_slow_queries(self, threshold: float = 1.0) -> List[Dict[str, Any]]:
        """获取慢查询信息"""
        with self._lock:
            slow_queries = []
            for query_time in self._query_times:
                if query_time > threshold:
                    slow_queries.append({
                        'execution_time': query_time,
                        'threshold': threshold
                    })
                    
            return sorted(slow_queries, key=lambda x: x['execution_time'], reverse=True)
            
    def optimize_pool(self):
        """优化连接池配置"""
        stats = self.get_stats()
        
        # 基于统计信息调整池大小
        peak_connections = stats['peak_connections']
        current_pool_size = self.config.pool_size
        
        if peak_connections > current_pool_size * 0.8:
            # 如果峰值连接数超过池大小的80%，建议增加池大小
            suggested_size = min(peak_connections + 5, current_pool_size * 2)
            logger.info(f"建议增加连接池大小: {current_pool_size} -> {suggested_size}")
            
        elif peak_connections < current_pool_size * 0.3:
            # 如果峰值连接数低于池大小的30%，建议减少池大小
            suggested_size = max(peak_connections + 2, current_pool_size // 2)
            logger.info(f"建议减少连接池大小: {current_pool_size} -> {suggested_size}")
            
        # 检查慢查询
        slow_queries = self.get_slow_queries()
        if slow_queries:
            logger.warning(f"发现 {len(slow_queries)} 个慢查询，建议优化查询性能")
            
        return {
            'current_pool_size': current_pool_size,
            'peak_connections': peak_connections,
            'slow_query_count': len(slow_queries),
            'optimization_suggestions': [
                "考虑调整连接池大小",
                "优化慢查询性能",
                "监控连接使用模式"
            ]
        }


class DatabasePoolRegistry:
    """数据库连接池注册表"""
    
    def __init__(self):
        self._pools: Dict[str, DatabasePoolManager] = {}
        self._lock = threading.RLock()
        
    def register_pool(self, name: str, config: PoolConfig) -> DatabasePoolManager:
        """注册连接池"""
        with self._lock:
            if name in self._pools:
                logger.warning(f"连接池 {name} 已存在，将被替换")
                self._pools[name].shutdown()
                
            pool_manager = DatabasePoolManager(config)
            pool_manager.initialize()
            self._pools[name] = pool_manager
            
            logger.info(f"连接池 {name} 注册成功")
            return pool_manager
            
    def get_pool(self, name: str) -> Optional[DatabasePoolManager]:
        """获取连接池"""
        with self._lock:
            return self._pools.get(name)
            
    def remove_pool(self, name: str):
        """移除连接池"""
        with self._lock:
            pool = self._pools.pop(name, None)
            if pool:
                pool.shutdown()
                logger.info(f"连接池 {name} 已移除")
                
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有连接池统计信息"""
        with self._lock:
            return {name: pool.get_stats() for name, pool in self._pools.items()}
            
    def shutdown_all(self):
        """关闭所有连接池"""
        with self._lock:
            for name, pool in self._pools.items():
                try:
                    pool.shutdown()
                    logger.info(f"连接池 {name} 已关闭")
                except Exception as e:
                    logger.error(f"关闭连接池 {name} 失败: {e}")
                    
            self._pools.clear()
            
    def optimize_all_pools(self) -> Dict[str, Dict[str, Any]]:
        """优化所有连接池"""
        with self._lock:
            optimization_results = {}
            for name, pool in self._pools.items():
                try:
                    optimization_results[name] = pool.optimize_pool()
                except Exception as e:
                    logger.error(f"优化连接池 {name} 失败: {e}")
                    optimization_results[name] = {'error': str(e)}
                    
            return optimization_results


# 全局连接池注册表
_pool_registry: Optional[DatabasePoolRegistry] = None


def get_pool_registry() -> DatabasePoolRegistry:
    """获取连接池注册表"""
    global _pool_registry
    if _pool_registry is None:
        _pool_registry = DatabasePoolRegistry()
    return _pool_registry


def create_pool(name: str, database_url: str, **kwargs) -> DatabasePoolManager:
    """创建数据库连接池"""
    config = PoolConfig(database_url=database_url, **kwargs)
    registry = get_pool_registry()
    return registry.register_pool(name, config)


def get_pool(name: str) -> Optional[DatabasePoolManager]:
    """获取数据库连接池"""
    registry = get_pool_registry()
    return registry.get_pool(name)


@contextmanager
def get_db_session(pool_name: str = 'default'):
    """获取数据库会话"""
    pool = get_pool(pool_name)
    if not pool:
        raise ValueError(f"连接池 {pool_name} 不存在")
        
    with pool.get_session() as session:
        yield session


def initialize_database_pools(configs: Dict[str, Dict[str, Any]]):
    """初始化数据库连接池"""
    registry = get_pool_registry()
    
    for name, config_dict in configs.items():
        try:
            config = PoolConfig(**config_dict)
            registry.register_pool(name, config)
            logger.info(f"数据库连接池 {name} 初始化成功")
        except Exception as e:
            logger.error(f"数据库连接池 {name} 初始化失败: {e}")
            

def shutdown_database_pools():
    """关闭所有数据库连接池"""
    global _pool_registry
    if _pool_registry:
        _pool_registry.shutdown_all()
        _pool_registry = None
    logger.info("所有数据库连接池已关闭")