"""
文本编辑器主组件

集成编辑器、工具栏、统计栏的完整组件
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QSplitter, QTextEdit, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QTextCursor
from typing import Optional
import logging
import markdown

from .text_editor import ModernTextEditor, WriteStatsWidget
from .editor_toolbar import EditorToolBar
from .markdown_preview import PreviewContainer
from ..themes.theme_manager import get_theme_manager
from ...models.chapter import Chapter
from ...utils.logger import LoggerMixin

logger = logging.getLogger(__name__)


class MarkdownPreview(QTextEdit):
    """Markdown预览组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.setReadOnly(True)
        self.setup_style()
        self.theme_manager.theme_changed.connect(self.setup_style)
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        self.setStyleSheet(f"""
            MarkdownPreview {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 8px;
                color: {colors['text_primary']};
                padding: 16px;
            }}
        """)
    
    def update_preview(self, markdown_text: str):
        """更新预览内容"""
        try:
            html = markdown.markdown(markdown_text, extensions=['extra', 'toc'])
            
            # 添加样式
            colors = self.theme_manager.current_colors
            styled_html = f"""
            <style>
            body {{
                font-family: {self.theme_manager.get_font('ui')};
                color: {colors['text_primary']};
                line-height: 1.6;
            }}
            h1, h2, h3, h4, h5, h6 {{
                color: {colors['primary']};
                margin-top: 1.5em;
                margin-bottom: 0.5em;
            }}
            code {{
                background-color: {colors['surface_hover']};
                padding: 2px 4px;
                border-radius: 3px;
                font-family: {self.theme_manager.get_font('code')};
            }}
            blockquote {{
                border-left: 4px solid {colors['primary']};
                margin-left: 0;
                padding-left: 16px;
                color: {colors['text_secondary']};
                font-style: italic;
            }}
            </style>
            {html}
            """
            
            self.setHtml(styled_html)
            
        except Exception as e:
            logger.error(f"预览更新失败: {e}")
            self.setPlainText("预览生成失败")


class TextEditorWidget(QWidget, LoggerMixin):
    """文本编辑器完整组件"""
    
    # 信号定义
    content_changed = pyqtSignal(str)
    save_requested = pyqtSignal()
    chapter_updated = pyqtSignal(object)  # Chapter对象
    
    def __init__(self, chapter: Optional[Chapter] = None, parent=None):
        super().__init__(parent)
        self.chapter = chapter
        self.theme_manager = get_theme_manager()
        self.preview_visible = False
        
        self.setup_ui()
        self.setup_signals()
        
        # 加载章节内容
        if self.chapter:
            self.load_chapter_content()
        
        self.logger.info("文本编辑器组件初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 工具栏
        self.toolbar = EditorToolBar()
        layout.addWidget(self.toolbar)
        
        # 编辑器和预览分割器
        self.splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 文本编辑器
        self.text_editor = ModernTextEditor()
        self.splitter.addWidget(self.text_editor)
        
        # Markdown预览（初始隐藏）
        self.preview = MarkdownPreview()
        self.preview.setVisible(False)
        self.splitter.addWidget(self.preview)
        
        layout.addWidget(self.splitter)
        
        # 统计栏
        self.stats_widget = WriteStatsWidget()
        layout.addWidget(self.stats_widget)
        
        # 设置分割器比例
        self.splitter.setSizes([600, 400])
    
    def setup_signals(self):
        """设置信号连接"""
        # 编辑器信号
        self.text_editor.content_changed.connect(self.on_content_changed)
        self.text_editor.cursor_position_changed.connect(self.on_cursor_changed)
        self.text_editor.save_requested.connect(self.save_requested.emit)
        
        # 工具栏信号
        self.toolbar.format_bold.connect(self.format_bold)
        self.toolbar.format_italic.connect(self.format_italic)
        self.toolbar.format_header.connect(self.format_header)
        self.toolbar.insert_link.connect(self.insert_link)
        self.toolbar.insert_image.connect(self.insert_image)
        self.toolbar.toggle_preview.connect(self.toggle_preview)
    
    def load_chapter_content(self):
        """加载章节内容"""
        if self.chapter and self.chapter.content:
            self.text_editor.setPlainText(self.chapter.content)
            self.logger.info(f"已加载章节内容: {self.chapter.title}")
    
    def get_content(self) -> str:
        """获取编辑器内容"""
        return self.text_editor.toPlainText()
    
    def set_content(self, content: str):
        """设置编辑器内容"""
        self.text_editor.setPlainText(content)
    
    def save_content(self) -> bool:
        """保存内容到章节"""
        try:
            content = self.get_content()
            
            if self.chapter:
                self.chapter.content = content
                self.chapter.update_statistics()  # 更新统计信息
                self.chapter_updated.emit(self.chapter)
                self.logger.info(f"章节内容已保存: {self.chapter.title}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存内容失败: {e}")
            return False
    
    def on_content_changed(self):
        """内容改变处理"""
        content = self.get_content()
        self.content_changed.emit(content)
        
        # 更新预览
        if self.preview_visible:
            self.preview.update_preview(content)
        
        # 更新统计
        cursor_position = self.text_editor.textCursor().position()
        self.stats_widget.update_stats(content, cursor_position)
    
    def on_cursor_changed(self, position: int):
        """光标位置改变处理"""
        content = self.get_content()
        self.stats_widget.update_stats(content, position)
    
    def format_bold(self):
        """加粗格式化"""
        cursor = self.text_editor.textCursor()
        selected_text = cursor.selectedText()
        
        if selected_text:
            # 如果已经是加粗格式，取消加粗
            if selected_text.startswith('**') and selected_text.endswith('**'):
                new_text = selected_text[2:-2]
            else:
                new_text = f"**{selected_text}**"
            
            cursor.insertText(new_text)
        else:
            # 没有选中文本，插入加粗标记
            cursor.insertText("****")
            cursor.movePosition(QTextCursor.MoveOperation.Left, QTextCursor.MoveMode.MoveAnchor, 2)
            self.text_editor.setTextCursor(cursor)
    
    def format_italic(self):
        """斜体格式化"""
        cursor = self.text_editor.textCursor()
        selected_text = cursor.selectedText()
        
        if selected_text:
            if selected_text.startswith('*') and selected_text.endswith('*') and not selected_text.startswith('**'):
                new_text = selected_text[1:-1]
            else:
                new_text = f"*{selected_text}*"
            
            cursor.insertText(new_text)
        else:
            cursor.insertText("**")
            cursor.movePosition(QTextCursor.MoveOperation.Left, QTextCursor.MoveMode.MoveAnchor, 1)
            self.text_editor.setTextCursor(cursor)
    
    def format_header(self, level: int):
        """标题格式化"""
        cursor = self.text_editor.textCursor()
        
        # 移动到行首
        cursor.movePosition(QTextCursor.MoveOperation.StartOfLine)
        
        # 选择整行
        cursor.movePosition(QTextCursor.MoveOperation.EndOfLine, QTextCursor.MoveMode.KeepAnchor)
        
        line_text = cursor.selectedText()
        
        # 移除现有的标题标记
        clean_text = line_text.lstrip('#').strip()
        
        # 添加新的标题标记
        if level > 0:
            new_text = '#' * level + ' ' + clean_text
        else:
            new_text = clean_text
        
        cursor.insertText(new_text)
    
    def insert_link(self):
        """插入链接"""
        cursor = self.text_editor.textCursor()
        selected_text = cursor.selectedText()
        
        if selected_text:
            link_text = f"[{selected_text}](url)"
        else:
            link_text = "[链接文本](url)"
        
        cursor.insertText(link_text)
        
        # 选中URL部分
        cursor.movePosition(QTextCursor.MoveOperation.Left, QTextCursor.MoveMode.MoveAnchor, 4)
        cursor.movePosition(QTextCursor.MoveOperation.Left, QTextCursor.MoveMode.KeepAnchor, 3)
        self.text_editor.setTextCursor(cursor)
    
    def insert_image(self):
        """插入图片"""
        cursor = self.text_editor.textCursor()
        selected_text = cursor.selectedText()
        
        if selected_text:
            image_text = f"![{selected_text}](image_url)"
        else:
            image_text = "![图片描述](image_url)"
        
        cursor.insertText(image_text)
        
        # 选中URL部分
        cursor.movePosition(QTextCursor.MoveOperation.Left, QTextCursor.MoveMode.MoveAnchor, 1)
        cursor.movePosition(QTextCursor.MoveOperation.Left, QTextCursor.MoveMode.KeepAnchor, 9)
        self.text_editor.setTextCursor(cursor)
    
    def toggle_preview(self, show_preview: bool):
        """切换预览模式"""
        self.preview_visible = show_preview
        self.preview.setVisible(show_preview)
        
        if show_preview:
            # 更新预览内容
            content = self.get_content()
            self.preview.update_preview(content)
            
            # 调整分割器比例
            self.splitter.setSizes([500, 500])
        else:
            # 隐藏预览时，编辑器占满
            self.splitter.setSizes([1000, 0])
    
    def find_text(self, text: str, case_sensitive: bool = False) -> bool:
        """查找文本"""
        return self.text_editor.find_text(text, case_sensitive)
    
    def replace_text(self, find_text: str, replace_text: str, case_sensitive: bool = False):
        """替换文本"""
        self.text_editor.replace_text(find_text, replace_text, case_sensitive)
    
    def goto_line(self, line_number: int):
        """跳转到指定行"""
        self.text_editor.goto_line(line_number)
    
    def set_read_only(self, read_only: bool):
        """设置只读模式"""
        self.text_editor.setReadOnly(read_only)
        self.toolbar.setEnabled(not read_only)