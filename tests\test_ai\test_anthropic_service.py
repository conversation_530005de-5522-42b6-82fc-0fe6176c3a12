# -*- coding: utf-8 -*-
"""
Anthropic服务测试
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch

from bamboofall.ai.anthropic_service import AnthropicService
from bamboofall.ai.ai_service_base import (
    AIConfig, AIMessage, MessageRole, AIResponse, AIModelType,
    AIServiceError, APIKeyError
)


class TestAnthropicService:
    """AnthropicService测试类"""
    
    @pytest.fixture
    def anthropic_config(self):
        """Anthropic配置fixture"""
        return AIConfig(
            api_key="test-anthropic-key",
            model="claude-3-sonnet-20240229",
            max_tokens=1000,
            temperature=0.7
        )
    
    @pytest.fixture
    def anthropic_service(self, anthropic_config):
        """AnthropicService实例fixture"""
        return AnthropicService(anthropic_config)
    
    def test_init(self, anthropic_service):
        """测试初始化"""
        assert anthropic_service.provider_name == "anthropic"
        assert anthropic_service.model_type == AIModelType.CHAT
        assert "claude-3-sonnet-20240229" in anthropic_service.supported_models
        assert "claude-3-opus-20240229" in anthropic_service.supported_models
        assert "claude-3-haiku-20240307" in anthropic_service.supported_models
    
    def test_supported_models(self, anthropic_service):
        """测试支持的模型列表"""
        models = anthropic_service.supported_models
        expected_models = [
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229", 
            "claude-3-haiku-20240307",
            "claude-2.1",
            "claude-2.0",
            "claude-instant-1.2",
            "claude-instant-1.1"
        ]
        
        for model in expected_models:
            assert model in models
    
    def test_validate_config_valid(self, anthropic_service):
        """测试有效配置验证"""
        assert anthropic_service.validate_config() is True
    
    def test_validate_config_invalid_api_key(self):
        """测试无效API密钥"""
        config = AIConfig(api_key="", model="claude-3-sonnet-20240229")
        service = AnthropicService(config)
        
        with pytest.raises(APIKeyError, match="Anthropic API密钥不能为空"):
            service.validate_config()
    
    def test_validate_config_invalid_model(self):
        """测试无效模型"""
        config = AIConfig(api_key="test-key", model="invalid-model")
        
        with pytest.raises(ValueError, match="不支持的模型"):
            AnthropicService(config)
    
    def test_convert_messages(self, anthropic_service):
        """测试消息转换"""
        messages = [
            AIMessage(role=MessageRole.SYSTEM, content="你是一个助手"),
            AIMessage(role=MessageRole.USER, content="你好"),
            AIMessage(role=MessageRole.ASSISTANT, content="你好！")
        ]
        
        converted_messages, system_message = anthropic_service._convert_messages(messages)
        
        # 检查系统消息被提取
        assert system_message == "你是一个助手"
        
        # 检查转换后的消息
        assert len(converted_messages) == 2
        assert converted_messages[0]["role"] == "user"
        assert converted_messages[0]["content"] == "你好"
        assert converted_messages[1]["role"] == "assistant"
        assert converted_messages[1]["content"] == "你好！"
    
    def test_convert_messages_no_system(self, anthropic_service):
        """测试无系统消息的转换"""
        messages = [
            AIMessage(role=MessageRole.USER, content="你好")
        ]
        
        converted_messages, system_message = anthropic_service._convert_messages(messages)
        
        assert system_message is None
        assert len(converted_messages) == 1
        assert converted_messages[0]["role"] == "user"
    
    @patch('anthropic.Anthropic')
    async def test_chat(self, mock_anthropic_client, anthropic_service):
        """测试聊天功能"""
        # 设置mock响应
        mock_response = Mock()
        mock_response.content = [Mock(text="这是Claude的回复")]
        mock_response.model = "claude-3-sonnet-20240229"
        mock_response.usage = Mock(input_tokens=10, output_tokens=20)
        mock_response.stop_reason = "end_turn"
        
        mock_client = Mock()
        mock_client.messages.create.return_value = mock_response
        mock_anthropic_client.return_value = mock_client
        
        # 测试聊天
        messages = [
            AIMessage(role=MessageRole.USER, content="你好")
        ]
        
        response = await anthropic_service.chat(messages)
        
        # 验证响应
        assert isinstance(response, AIResponse)
        assert response.content == "这是Claude的回复"
        assert response.model == "claude-3-sonnet-20240229"
        assert response.usage["input_tokens"] == 10
        assert response.usage["output_tokens"] == 20
        assert response.finish_reason == "end_turn"
        
        # 验证API调用
        mock_client.messages.create.assert_called_once()
        call_args = mock_client.messages.create.call_args[1]
        assert call_args["model"] == "claude-3-sonnet-20240229"
        assert call_args["max_tokens"] == 1000
        assert call_args["temperature"] == 0.7
    
    @patch('anthropic.Anthropic')
    async def test_stream_chat(self, mock_anthropic_client, anthropic_service):
        """测试流式聊天"""
        # 设置mock流式响应
        def mock_stream():
            yield Mock(delta=Mock(text="你好"))
            yield Mock(delta=Mock(text="，我是"))
            yield Mock(delta=Mock(text="Claude"))
        
        mock_client = Mock()
        mock_client.messages.stream.return_value = mock_stream()
        mock_anthropic_client.return_value = mock_client
        
        # 测试流式聊天
        messages = [
            AIMessage(role=MessageRole.USER, content="介绍一下你自己")
        ]
        
        chunks = []
        async for chunk in anthropic_service.stream_chat(messages):
            chunks.append(chunk)
        
        # 验证流式响应
        assert chunks == ["你好", "，我是", "Claude"]
        
        # 验证API调用
        mock_client.messages.stream.assert_called_once()
    
    async def test_complete(self, anthropic_service):
        """测试文本补全"""
        # 文本补全实际上调用chat方法
        with patch.object(anthropic_service, 'chat') as mock_chat:
            mock_response = AIResponse(
                content="补全的文本内容",
                model="claude-3-sonnet-20240229"
            )
            mock_chat.return_value = mock_response
            
            result = await anthropic_service.complete("请补全这段文字：")
            
            assert result == mock_response
            mock_chat.assert_called_once()
            
            # 检查传递给chat的消息
            call_args = mock_chat.call_args[0]
            messages = call_args[0]
            assert len(messages) == 1
            assert messages[0].role == MessageRole.USER
            assert messages[0].content == "请补全这段文字："
    
    async def test_get_embedding_not_implemented(self, anthropic_service):
        """测试embedding功能（应该抛出NotImplementedError）"""
        with pytest.raises(NotImplementedError, match="Anthropic不提供embedding服务"):
            await anthropic_service.get_embedding("测试文本")
    
    def test_get_model_info(self, anthropic_service):
        """测试获取模型信息"""
        # 测试有效模型
        info = anthropic_service.get_model_info("claude-3-sonnet-20240229")
        assert info is not None
        assert info["name"] == "Claude 3 Sonnet"
        assert info["max_tokens"] == 4096
        assert "context_window" in info
        
        # 测试无效模型
        info = anthropic_service.get_model_info("invalid-model")
        assert info is None
    
    @patch('anthropic.Anthropic')
    async def test_test_connection_success(self, mock_anthropic_client, anthropic_service):
        """测试连接成功"""
        # 设置成功的mock响应
        mock_response = Mock()
        mock_response.content = [Mock(text="Hello")]
        
        mock_client = Mock()
        mock_client.messages.create.return_value = mock_response
        mock_anthropic_client.return_value = mock_client
        
        result = await anthropic_service.test_connection()
        assert result is True
    
    @patch('anthropic.Anthropic')
    async def test_test_connection_failure(self, mock_anthropic_client, anthropic_service):
        """测试连接失败"""
        # 设置失败的mock响应
        mock_client = Mock()
        mock_client.messages.create.side_effect = Exception("连接失败")
        mock_anthropic_client.return_value = mock_client
        
        result = await anthropic_service.test_connection()
        assert result is False
    
    @patch('anthropic.Anthropic')
    async def test_chat_api_error(self, mock_anthropic_client, anthropic_service):
        """测试API错误处理"""
        # 设置API错误
        mock_client = Mock()
        mock_client.messages.create.side_effect = Exception("API错误")
        mock_anthropic_client.return_value = mock_client
        
        messages = [
            AIMessage(role=MessageRole.USER, content="测试")
        ]
        
        with pytest.raises(AIServiceError, match="Anthropic API调用失败"):
            await anthropic_service.chat(messages)
    
    def test_error_handling_invalid_response(self, anthropic_service):
        """测试无效响应处理"""
        # 测试处理无效响应的错误处理逻辑
        with patch.object(anthropic_service, '_handle_api_error') as mock_handler:
            mock_handler.return_value = AIServiceError("处理后的错误")
            
            error = anthropic_service._handle_api_error(Exception("原始错误"))
            assert isinstance(error, AIServiceError)
            assert "处理后的错误" in str(error)