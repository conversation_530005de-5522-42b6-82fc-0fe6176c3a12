# 笔落App Python版本 技术开发文档

## 文档信息
- **项目名称**：笔落App (Python版本)
- **技术文档版本**：1.0-Python
- **创建日期**：2025年8月30日
- **技术栈**：Python + PyQt6 + SQLite + AI集成

## 一、技术栈选择

### 1.1 前端界面技术栈
#### 1.1.1 GUI框架
- **主框架**：PyQt6 6.6+ (现代化GUI，丰富控件)
- **备选方案**：PySide6 6.6+ (开源替代，API兼容)
- **主题系统**：qdarktheme 2.1+ (现代化主题支持)
- **图标库**：qtawesome 1.3+ (丰富的图标集合)

#### 1.1.2 界面美化方案
- **样式系统**：QSS (类似CSS的样式定制)
- **动画效果**：QPropertyAnimation (平滑过渡动画)
- **布局系统**：QGridLayout + QSplitter (响应式布局)
- **字体方案**：Source Han Sans CN + JetBrains Mono

#### 1.1.3 编辑器组件
- **文本编辑器**：QTextEdit + QSyntaxHighlighter
- **Markdown支持**：markdown 3.5+ (渲染预览)
- **语法高亮**：自定义Highlighter类
- **代码补全**：QCompleter (智能提示)

### 1.2 后端业务逻辑
#### 1.2.1 核心框架
- **运行环境**：Python 3.9+
- **数据模型**：dataclasses + pydantic 2.5+ (数据验证)
- **异步处理**：asyncio + aiohttp 3.9+ (AI服务调用)
- **配置管理**：configparser + json (配置存储)

#### 1.2.2 数据存储方案
- **主数据库**：SQLite 3.40+ (轻量级本地数据库)
- **ORM框架**：SQLAlchemy 2.0+ (现代化ORM)
- **文档存储**：本地文件系统 (JSON/Markdown格式)
- **缓存系统**：内存缓存 + 磁盘缓存

#### 1.2.3 AI集成方案
- **HTTP客户端**：aiohttp + httpx (异步请求)
- **AI SDK集成**：
  - OpenAI SDK 1.0+
  - Anthropic SDK 0.8+
  - 自定义Deepseek API封装
- **本地模型**：Ollama集成 (可选)

### 1.3 可视化和数据处理
#### 1.3.1 可视化库
- **图表库**：matplotlib 3.8+ (角色关系图、统计图表)
- **交互式图表**：plotly 5.17+ (动态可视化)
- **网络图**：networkx 3.2+ (角色关系网络)
- **地图组件**：folium 0.15+ (场景地图)

#### 1.3.2 文本处理
- **NLP库**：spacy 3.7+ + nltk 3.8+ (文本分析)
- **搜索引擎**：whoosh 2.7.4+ (全文搜索)
- **模板引擎**：jinja2 3.1+ (导出模板)

### 1.4 开发工具链
#### 1.4.1 开发环境
- **包管理器**：pip + venv (虚拟环境)
- **代码检查**：black + flake8 + mypy (代码质量)
- **测试框架**：pytest 7.4+ (单元测试)
- **文档生成**：sphinx (API文档)

#### 1.4.2 打包分发
- **应用打包**：PyInstaller 6.0+ (一键打包)
- **备选方案**：cx_Freeze (跨平台打包)
- **安装包**：NSIS (Windows) + dmg (macOS) + AppImage (Linux)

## 二、项目架构设计

### 2.1 整体架构图
```
用户界面层 (PyQt6)
├── MainWindow (主窗口管理)
├── DashboardWidget (仪表板)
├── WorkspaceWindow (工作空间)
├── EditorArea (编辑器区域)
├── StoryBiblePanel (故事圣经面板)
└── SettingsDialog (设置对话框)
                ↓
业务逻辑层 (Python Core)
├── ProjectManager (项目管理器)
├── AIServiceManager (AI服务管理器)
├── StoryBibleManager (故事圣经管理器)
├── OutlineManager (大纲管理器)
├── ContentManager (内容管理器)
└── ExportService (导出服务)
                ↓
数据访问层 (Data Layer)
├── DatabaseManager (数据库管理器)
├── FileSystemManager (文件系统管理器)
├── ConfigManager (配置管理器)
└── CacheManager (缓存管理器)
                ↓
存储层 (Storage)
├── SQLite数据库 (结构化数据)
├── JSON文件 (配置和元数据)
├── Markdown文件 (章节内容)
└── 资源文件 (模板、主题、图标)
```

### 2.2 详细目录结构
```
bamboofall_python/
├── src/
│   └── bamboofall/
│       ├── __init__.py
│       ├── main.py                     # 应用入口
│       ├── ui/                         # 用户界面模块
│       │   ├── __init__.py
│       │   ├── main_window.py          # 主窗口
│       │   ├── dashboard/              # 仪表板界面
│       │   ├── workspace/              # 工作空间界面
│       │   ├── editor/                 # 编辑器组件
│       │   ├── story_bible/            # 故事圣经界面
│       │   ├── dialogs/                # 对话框
│       │   ├── widgets/                # 自定义控件
│       │   └── themes/                 # 主题样式
│       ├── core/                       # 核心业务逻辑
│       │   ├── __init__.py
│       │   ├── project_manager.py      # 项目管理
│       │   ├── ai_service.py           # AI服务
│       │   ├── story_bible.py          # 故事圣经
│       │   ├── outline_manager.py      # 大纲管理
│       │   ├── content_manager.py      # 内容管理
│       │   └── export_service.py       # 导出服务
│       ├── models/                     # 数据模型
│       │   ├── __init__.py
│       │   ├── project.py              # 项目模型
│       │   ├── character.py            # 角色模型
│       │   ├── scene.py                # 场景模型
│       │   ├── event.py                # 事件模型
│       │   ├── chapter.py              # 章节模型
│       │   └── outline.py              # 大纲模型
│       ├── database/                   # 数据库模块
│       │   ├── __init__.py
│       │   ├── db_manager.py           # 数据库管理
│       │   ├── models.py               # SQLAlchemy模型
│       │   └── migrations/             # 数据库迁移
│       ├── ai/                         # AI集成模块
│       │   ├── __init__.py
│       │   ├── providers/              # AI服务提供商
│       │   ├── content_generator.py    # 内容生成器
│       │   ├── content_optimizer.py    # 内容优化器
│       │   └── style_analyzer.py       # 风格分析器
│       ├── utils/                      # 工具函数
│       │   ├── __init__.py
│       │   ├── file_utils.py           # 文件操作
│       │   ├── text_utils.py           # 文本处理
│       │   ├── config_utils.py         # 配置管理
│       │   └── logger.py               # 日志工具
│       └── plugins/                    # 插件系统
│           ├── __init__.py
│           ├── plugin_manager.py       # 插件管理器
│           └── base_plugin.py          # 插件基类
├── resources/                          # 资源文件
│   ├── icons/                          # 图标资源
│   ├── themes/                         # 主题资源
│   ├── templates/                      # 项目模板
│   └── fonts/                          # 字体文件
├── tests/                              # 测试文件
├── docs/                               # 文档
├── scripts/                            # 构建脚本
├── requirements.txt                    # 生产依赖
├── requirements-dev.txt                # 开发依赖
├── pyproject.toml                      # 项目配置
└── README.md                           # 项目说明
```

## 三、核心模块设计

### 3.1 界面美化核心实现

#### 3.1.1 主题管理器
```python
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QSettings
import qdarktheme

class ThemeManager:
    def __init__(self):
        self.settings = QSettings()
        self.current_theme = self.settings.value("theme", "auto")
        
    def apply_theme(self, theme_name: str):
        """应用主题"""
        if theme_name == "dark":
            QApplication.instance().setStyleSheet(
                qdarktheme.load_stylesheet("dark")
            )
        elif theme_name == "light":
            QApplication.instance().setStyleSheet(
                qdarktheme.load_stylesheet("light")
            )
        else:  # auto
            QApplication.instance().setStyleSheet(
                qdarktheme.load_stylesheet()
            )
        
        # 应用自定义样式
        self.apply_custom_styles()
        
    def apply_custom_styles(self):
        """应用自定义样式"""
        custom_style = """
        QMainWindow {
            background-color: #fafafa;
        }
        QTextEdit {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            font-family: 'JetBrains Mono', monospace;
            font-size: 14px;
            line-height: 1.6;
        }
        QPushButton {
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 20px;
            font-weight: 500;
        }
        QPushButton:hover {
            background-color: #1976D2;
        }
        """
        app = QApplication.instance()
        current_style = app.styleSheet()
        app.setStyleSheet(current_style + custom_style)
```

#### 3.1.2 现代化控件库
```python
from PyQt6.QtWidgets import QPushButton, QWidget, QVBoxLayout, QLabel
from PyQt6.QtCore import pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QPainter, QColor

class ModernButton(QPushButton):
    def __init__(self, text, style='primary'):
        super().__init__(text)
        self.style_type = style
        self.setup_style()
        self.setup_animation()
    
    def setup_style(self):
        styles = {
            'primary': {
                'bg': '#2196F3',
                'hover': '#1976D2',
                'text': 'white'
            },
            'secondary': {
                'bg': '#757575',
                'hover': '#616161',
                'text': 'white'
            }
        }
        style = styles[self.style_type]
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {style['bg']};
                color: {style['text']};
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-weight: 500;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {style['hover']};
            }}
        """)
    
    def setup_animation(self):
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(200)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)

class ProjectCardWidget(QWidget):
    clicked = pyqtSignal(str)  # 项目ID
    
    def __init__(self, project_data):
        super().__init__()
        self.project = project_data
        self.setup_card()
        self.setup_animation()
    
    def setup_card(self):
        layout = QVBoxLayout()
        
        # 项目标题
        title = QLabel(self.project.name)
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: 600;
                color: #333;
                margin-bottom: 8px;
            }
        """)
        
        # 项目描述
        description = QLabel(self.project.description or "暂无描述")
        description.setWordWrap(True)
        description.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 14px;
                line-height: 1.4;
            }
        """)
        
        layout.addWidget(title)
        layout.addWidget(description)
        self.setLayout(layout)
        
        # 卡片样式
        self.setStyleSheet("""
            ProjectCardWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                padding: 20px;
                margin: 8px;
            }
            ProjectCardWidget:hover {
                border-color: #2196F3;
                box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            }
        """)
    
    def setup_animation(self):
        self.hover_animation = QPropertyAnimation(self, b"geometry")
        self.hover_animation.setDuration(150)
    
    def mousePressEvent(self, event):
        self.clicked.emit(self.project.id)
        super().mousePressEvent(event)
```

### 3.2 AI服务集成架构

#### 3.2.1 AI服务抽象层
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, AsyncGenerator
import asyncio
import aiohttp
from dataclasses import dataclass

@dataclass
class AIResponse:
    content: str
    model: str
    tokens_used: int
    cost: float = 0.0

class AIProvider(ABC):
    """AI服务提供商抽象基类"""
    
    @abstractmethod
    async def generate_content(self, prompt: str, **kwargs) -> AIResponse:
        pass
    
    @abstractmethod
    async def stream_content(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        pass
    
    @abstractmethod
    async def optimize_content(self, content: str, optimization_type: str) -> AIResponse:
        pass

class OpenAIProvider(AIProvider):
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.openai.com/v1"
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            headers={"Authorization": f"Bearer {self.api_key}"}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def generate_content(self, prompt: str, **kwargs) -> AIResponse:
        model = kwargs.get('model', 'gpt-4')
        max_tokens = kwargs.get('max_tokens', 1000)
        temperature = kwargs.get('temperature', 0.7)
        
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature
        }
        
        async with self.session.post(f"{self.base_url}/chat/completions", json=payload) as resp:
            data = await resp.json()
            content = data['choices'][0]['message']['content']
            tokens = data['usage']['total_tokens']
            
            return AIResponse(
                content=content,
                model=model,
                tokens_used=tokens
            )

class AIServiceManager:
    """AI服务管理器"""
    def __init__(self):
        self.providers: Dict[str, AIProvider] = {}
        self.default_provider = 'openai'
        self.rate_limiter = None
        
    def register_provider(self, name: str, provider: AIProvider):
        self.providers[name] = provider
    
    async def generate_content(self, prompt: str, provider: Optional[str] = None, **kwargs) -> AIResponse:
        provider_name = provider or self.default_provider
        if provider_name not in self.providers:
            raise ValueError(f"Unknown provider: {provider_name}")
        
        provider_instance = self.providers[provider_name]
        return await provider_instance.generate_content(prompt, **kwargs)
```

### 3.3 现代化文本编辑器

#### 3.3.1 增强型文本编辑器
```python
from PyQt6.QtWidgets import QTextEdit, QCompleter
from PyQt6.QtGui import QSyntaxHighlighter, QTextCharFormat, QFont
from PyQt6.QtCore import QRegularExpression, QTimer, pyqtSignal

class MarkdownHighlighter(QSyntaxHighlighter):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.highlighting_rules = []
        self.setup_highlighting_rules()
    
    def setup_highlighting_rules(self):
        # 标题格式
        title_format = QTextCharFormat()
        title_format.setForeground(QColor("#1976D2"))
        title_format.setFontWeight(QFont.Weight.Bold)
        self.highlighting_rules.append((
            QRegularExpression(r"^#{1,6}\s.*$"),
            title_format
        ))
        
        # 加粗格式
        bold_format = QTextCharFormat()
        bold_format.setFontWeight(QFont.Weight.Bold)
        self.highlighting_rules.append((
            QRegularExpression(r"\*\*([^*]+)\*\*"),
            bold_format
        ))
        
        # 斜体格式
        italic_format = QTextCharFormat()
        italic_format.setFontItalic(True)
        self.highlighting_rules.append((
            QRegularExpression(r"\*([^*]+)\*"),
            italic_format
        ))
    
    def highlightBlock(self, text):
        for pattern, format in self.highlighting_rules:
            iterator = pattern.globalMatch(text)
            while iterator.hasNext():
                match = iterator.next()
                self.setFormat(match.capturedStart(), match.capturedLength(), format)

class ModernTextEditor(QTextEdit):
    contentChanged = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.setup_editor()
        self.setup_highlighter()
        self.setup_auto_save()
        self.setup_auto_complete()
    
    def setup_editor(self):
        # 设置字体
        font = QFont("JetBrains Mono", 14)
        font.setStyleHint(QFont.StyleHint.Monospace)
        self.setFont(font)
        
        # 设置样式
        self.setStyleSheet("""
            QTextEdit {
                background-color: #fafafa;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 16px;
                line-height: 1.6;
            }
        """)
        
        # 连接信号
        self.textChanged.connect(self.on_text_changed)
    
    def setup_highlighter(self):
        self.highlighter = MarkdownHighlighter(self.document())
    
    def setup_auto_save(self):
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save)
        self.auto_save_timer.setSingleShot(True)
        
    def setup_auto_complete(self):
        # 设置自动补全
        completer = QCompleter(["章节", "人物", "场景", "对话"])
        self.setCompleter(completer)
    
    def on_text_changed(self):
        self.contentChanged.emit()
        # 重置自动保存定时器
        self.auto_save_timer.start(5000)  # 5秒后自动保存
    
    def auto_save(self):
        # 实现自动保存逻辑
        pass
```

## 四、数据库设计

### 4.1 SQLAlchemy模型设计
```python
from sqlalchemy import Column, String, Integer, DateTime, Text, ForeignKey, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class Project(Base):
    __tablename__ = 'projects'
    
    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    type = Column(String, nullable=False)
    description = Column(Text)
    author = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    settings = Column(Text)  # JSON格式
    
    # 关系
    characters = relationship("Character", back_populates="project")
    scenes = relationship("Scene", back_populates="project")
    chapters = relationship("Chapter", back_populates="project")

class Character(Base):
    __tablename__ = 'characters'
    
    id = Column(String, primary_key=True)
    project_id = Column(String, ForeignKey('projects.id'), nullable=False)
    name = Column(String, nullable=False)
    age = Column(Integer)
    gender = Column(String)
    description = Column(Text)
    personality = Column(Text)
    background = Column(Text)
    abilities = Column(Text)  # JSON格式
    relationships = Column(Text)  # JSON格式
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    project = relationship("Project", back_populates="characters")

class Chapter(Base):
    __tablename__ = 'chapters'
    
    id = Column(String, primary_key=True)
    project_id = Column(String, ForeignKey('projects.id'), nullable=False)
    title = Column(String, nullable=False)
    content = Column(Text)
    word_count = Column(Integer, default=0)
    status = Column(String, default='draft')
    order_index = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    project = relationship("Project", back_populates="chapters")
```

## 五、开发环境配置

### 5.1 环境要求
- **Python**: 3.9+
- **pip**: 最新版本
- **Git**: 2.30.0+

### 5.2 开发环境搭建
```bash
# 1. 创建虚拟环境
python -m venv bamboofall_env
source bamboofall_env/bin/activate  # Linux/macOS
# bamboofall_env\Scripts\activate  # Windows

# 2. 安装依赖
pip install -r requirements-dev.txt

# 3. 安装项目
pip install -e .

# 4. 运行应用
python src/bamboofall/main.py
```

### 5.3 核心依赖配置
```python
# requirements.txt
PyQt6>=6.6.0
SQLAlchemy>=2.0.0
pydantic>=2.5.0
aiohttp>=3.9.0
openai>=1.0.0
anthropic>=0.8.0
matplotlib>=3.8.0
plotly>=5.17.0
reportlab>=4.0.0
python-docx>=1.1.0
ebooklib>=0.18
qdarktheme>=2.1.0
qtawesome>=1.3.0
jinja2>=3.1.0
nltk>=3.8.0
spacy>=3.7.0
whoosh>=2.7.4
markdown>=3.5.0

# requirements-dev.txt
pytest>=7.4.0
black>=23.9.0
flake8>=6.0.0
mypy>=1.5.0
sphinx>=7.2.0
pytest-qt>=4.2.0
pytest-asyncio>=0.21.0
```

---

**技术开发文档完成**

*本文档为笔落App Python版本提供了详细的技术实现方案，包括界面美化、AI集成、数据库设计等核心技术。*