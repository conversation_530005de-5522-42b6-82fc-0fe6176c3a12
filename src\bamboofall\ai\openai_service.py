"""
OpenAI API服务实现

提供OpenAI API的完整集成，支持聊天、补全和嵌入功能
"""

import asyncio
import time
from typing import List, Optional, Dict, Any, AsyncGenerator
import openai
from openai import AsyncOpenAI

from .ai_service_base import (
    AIServiceBase, AIMessage, AIResponse, AIConfig, AIModelType,
    MessageRole, AIServiceError, APIKeyError, RateLimitError, NetworkError
)
from ..utils.logger import LoggerMixin


class OpenAIService(AIServiceBase, LoggerMixin):
    """OpenAI服务实现"""
    
    def __init__(self, config: AIConfig):
        super().__init__(config)
        self.client = AsyncOpenAI(
            api_key=config.api_key,
            base_url=config.base_url,
            timeout=config.timeout
        )
        
        # OpenAI支持的模型列表
        self._supported_models = [
            # GPT-4 系列
            "gpt-4",
            "gpt-4-turbo",
            "gpt-4-turbo-preview",
            "gpt-4-1106-preview",
            "gpt-4-0125-preview",
            
            # GPT-3.5 系列
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
            "gpt-3.5-turbo-1106",
            "gpt-3.5-turbo-0125",
            
            # 文本补全模型
            "text-davinci-003",
            "text-davinci-002",
            "davinci-002",
            
            # 嵌入模型
            "text-embedding-ada-002",
            "text-embedding-3-small",
            "text-embedding-3-large"
        ]
    
    @property
    def supported_models(self) -> List[str]:
        """支持的模型列表"""
        return self._supported_models
    
    @property
    def model_type(self) -> AIModelType:
        """模型类型"""
        return AIModelType.CHAT
    
    def _convert_messages(self, messages: List[AIMessage]) -> List[Dict[str, str]]:
        """转换消息格式为OpenAI格式"""
        return [
            {"role": msg.role.value, "content": msg.content}
            for msg in messages
        ]
    
    def _handle_api_error(self, error: Exception) -> AIServiceError:
        """处理API错误"""
        if isinstance(error, openai.AuthenticationError):
            return APIKeyError("OpenAI API密钥无效或已过期")
        elif isinstance(error, openai.RateLimitError):
            return RateLimitError("OpenAI API请求频率超限，请稍后重试")
        elif isinstance(error, openai.APIConnectionError):
            return NetworkError("无法连接到OpenAI API服务")
        elif isinstance(error, openai.APIStatusError):
            return AIServiceError(f"OpenAI API错误: {error.message}", str(error.status_code))
        else:
            return AIServiceError(f"OpenAI服务未知错误: {str(error)}")
    
    async def chat(
        self,
        messages: List[AIMessage],
        model: Optional[str] = None,
        **kwargs
    ) -> AIResponse:
        """聊天接口"""
        start_time = time.time()
        
        try:
            # 参数准备
            model = model or self.config.model
            max_tokens = kwargs.get('max_tokens', self.config.max_tokens)
            temperature = kwargs.get('temperature', self.config.temperature)
            
            # 构建请求参数
            request_params = {
                "model": model,
                "messages": self._convert_messages(messages),
                "max_tokens": max_tokens,
                "temperature": temperature,
                **self.config.extra_params,
                **{k: v for k, v in kwargs.items() if k not in ['max_tokens', 'temperature']}
            }
            
            self.logger.debug(f"发送OpenAI聊天请求: model={model}, messages={len(messages)}")
            
            # 发送请求
            response = await self.client.chat.completions.create(**request_params)
            
            # 解析响应
            choice = response.choices[0]
            content = choice.message.content or ""
            
            response_time = time.time() - start_time
            
            ai_response = AIResponse(
                content=content,
                model=response.model,
                provider="openai",
                usage={
                    "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                    "completion_tokens": response.usage.completion_tokens if response.usage else 0,
                    "total_tokens": response.usage.total_tokens if response.usage else 0
                },
                finish_reason=choice.finish_reason,
                response_time=response_time,
                metadata={
                    "id": response.id,
                    "created": response.created,
                    "system_fingerprint": getattr(response, 'system_fingerprint', None)
                }
            )
            
            self.logger.info(f"OpenAI聊天请求完成: tokens={ai_response.usage.get('total_tokens', 0)}, time={response_time:.2f}s")
            return ai_response
            
        except Exception as e:
            self.logger.error(f"OpenAI聊天请求失败: {e}")
            raise self._handle_api_error(e)
    
    async def stream_chat(
        self,
        messages: List[AIMessage],
        model: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式聊天接口"""
        try:
            # 参数准备
            model = model or self.config.model
            max_tokens = kwargs.get('max_tokens', self.config.max_tokens)
            temperature = kwargs.get('temperature', self.config.temperature)
            
            # 构建请求参数
            request_params = {
                "model": model,
                "messages": self._convert_messages(messages),
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": True,
                **self.config.extra_params,
                **{k: v for k, v in kwargs.items() if k not in ['max_tokens', 'temperature']}
            }
            
            self.logger.debug(f"发送OpenAI流式聊天请求: model={model}")
            
            # 发送流式请求
            stream = await self.client.chat.completions.create(**request_params)
            
            async for chunk in stream:
                if chunk.choices:
                    delta = chunk.choices[0].delta
                    if hasattr(delta, 'content') and delta.content:
                        yield delta.content
            
            self.logger.debug("OpenAI流式聊天请求完成")
            
        except Exception as e:
            self.logger.error(f"OpenAI流式聊天请求失败: {e}")
            raise self._handle_api_error(e)
    
    async def complete(
        self,
        prompt: str,
        model: Optional[str] = None,
        **kwargs
    ) -> AIResponse:
        """文本补全接口"""
        start_time = time.time()
        
        try:
            # 使用聊天模式实现补全
            messages = [AIMessage(role=MessageRole.USER, content=prompt)]
            return await self.chat(messages, model, **kwargs)
            
        except Exception as e:
            self.logger.error(f"OpenAI文本补全请求失败: {e}")
            raise self._handle_api_error(e)
    
    async def get_embedding(
        self,
        text: str,
        model: Optional[str] = None
    ) -> List[float]:
        """获取文本嵌入"""
        try:
            model = model or "text-embedding-ada-002"
            
            self.logger.debug(f"发送OpenAI嵌入请求: model={model}")
            
            response = await self.client.embeddings.create(
                input=text,
                model=model
            )
            
            embedding = response.data[0].embedding
            
            self.logger.debug(f"OpenAI嵌入请求完成: dimensions={len(embedding)}")
            return embedding
            
        except Exception as e:
            self.logger.error(f"OpenAI嵌入请求失败: {e}")
            raise self._handle_api_error(e)
    
    def validate_config(self) -> bool:
        """验证配置是否有效"""
        try:
            # 检查API密钥
            if not self.config.api_key or not self.config.api_key.startswith('sk-'):
                self.logger.error("无效的OpenAI API密钥格式")
                return False
            
            # 检查模型是否支持
            if self.config.model not in self.supported_models:
                self.logger.warning(f"模型 {self.config.model} 可能不受支持")
            
            return True
            
        except Exception as e:
            self.logger.error(f"OpenAI配置验证失败: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            # 发送简单的测试请求
            test_messages = [
                AIMessage(role=MessageRole.USER, content="Hello")
            ]
            
            response = await self.chat(
                test_messages,
                max_tokens=10,
                temperature=0
            )
            
            return response is not None and response.content
            
        except Exception as e:
            self.logger.error(f"OpenAI连接测试失败: {e}")
            return False
    
    async def list_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        try:
            response = await self.client.models.list()
            models = []
            
            for model in response.data:
                if model.id in self.supported_models:
                    models.append({
                        "id": model.id,
                        "object": model.object,
                        "created": model.created,
                        "owned_by": model.owned_by
                    })
            
            return models
            
        except Exception as e:
            self.logger.error(f"获取OpenAI模型列表失败: {e}")
            raise self._handle_api_error(e)
    
    def get_model_pricing(self, model: str) -> Dict[str, float]:
        """获取模型定价信息（美元/1K tokens）"""
        pricing = {
            # GPT-4 系列
            "gpt-4": {"input": 0.03, "output": 0.06},
            "gpt-4-turbo": {"input": 0.01, "output": 0.03},
            "gpt-4-turbo-preview": {"input": 0.01, "output": 0.03},
            
            # GPT-3.5 系列
            "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},
            "gpt-3.5-turbo-16k": {"input": 0.003, "output": 0.004},
            
            # 嵌入模型
            "text-embedding-ada-002": {"input": 0.0001, "output": 0},
            "text-embedding-3-small": {"input": 0.00002, "output": 0},
            "text-embedding-3-large": {"input": 0.00013, "output": 0}
        }
        
        return pricing.get(model, {"input": 0, "output": 0})
    
    def estimate_cost(self, model: str, input_tokens: int, output_tokens: int = 0) -> float:
        """估算成本"""
        pricing = self.get_model_pricing(model)
        input_cost = (input_tokens / 1000) * pricing["input"]
        output_cost = (output_tokens / 1000) * pricing["output"]
        return input_cost + output_cost