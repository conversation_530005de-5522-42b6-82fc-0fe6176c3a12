"""
编辑器工具栏组件

提供格式化、插入等编辑功能的工具栏
"""

from PyQt6.QtWidgets import (
    QFrame, QHBoxLayout, QComboBox, QPushButton
)
from PyQt6.QtCore import pyqtSignal
from PyQt6.QtGui import QFont
from typing import Optional

from ..themes.theme_manager import get_theme_manager
from ..widgets.modern_widgets import create_modern_button


class EditorToolBar(QFrame):
    """编辑器工具栏"""
    
    # 信号定义
    format_bold = pyqtSignal()
    format_italic = pyqtSignal()
    format_header = pyqtSignal(int)
    insert_link = pyqtSignal()
    insert_image = pyqtSignal()
    toggle_preview = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.setup_ui()
        self.setup_style()
        self.theme_manager.theme_changed.connect(self.setup_style)
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(8, 4, 8, 4)
        layout.setSpacing(4)
        
        # 格式化按钮
        self.bold_btn = create_modern_button("B", "outline")
        self.bold_btn.setMaximumSize(32, 28)
        self.bold_btn.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        self.bold_btn.clicked.connect(self.format_bold.emit)
        layout.addWidget(self.bold_btn)
        
        self.italic_btn = create_modern_button("I", "outline")
        self.italic_btn.setMaximumSize(32, 28)
        italic_font = QFont("Arial", 10)
        italic_font.setItalic(True)
        self.italic_btn.setFont(italic_font)
        self.italic_btn.clicked.connect(self.format_italic.emit)
        layout.addWidget(self.italic_btn)
        
        # 分隔符
        layout.addWidget(self.create_separator())
        
        # 标题级别选择
        self.header_combo = QComboBox()
        self.header_combo.addItems(["正文", "标题1", "标题2", "标题3", "标题4", "标题5", "标题6"])
        self.header_combo.setMaximumWidth(80)
        self.header_combo.currentIndexChanged.connect(self.on_header_changed)
        layout.addWidget(self.header_combo)
        
        # 分隔符
        layout.addWidget(self.create_separator())
        
        # 插入按钮
        self.link_btn = create_modern_button("链接", "outline")
        self.link_btn.setMaximumSize(50, 28)
        self.link_btn.clicked.connect(self.insert_link.emit)
        layout.addWidget(self.link_btn)
        
        self.image_btn = create_modern_button("图片", "outline")
        self.image_btn.setMaximumSize(50, 28)
        self.image_btn.clicked.connect(self.insert_image.emit)
        layout.addWidget(self.image_btn)
        
        layout.addStretch()
        
        # 预览切换按钮
        self.preview_btn = create_modern_button("预览", "secondary")
        self.preview_btn.setMaximumSize(60, 28)
        self.preview_btn.setCheckable(True)
        self.preview_btn.toggled.connect(self.toggle_preview.emit)
        layout.addWidget(self.preview_btn)
    
    def create_separator(self) -> QFrame:
        """创建分隔符"""
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.VLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setMaximumHeight(20)
        return separator
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        self.setStyleSheet(f"""
            EditorToolBar {{
                background-color: {colors['surface']};
                border-bottom: 1px solid {colors['border']};
            }}
            
            QComboBox {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 4px;
                padding: 4px 8px;
                color: {colors['text_primary']};
                font-size: 12px;
            }}
            
            QComboBox::drop-down {{
                border: none;
                width: 16px;
            }}
            
            QComboBox QAbstractItemView {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 4px;
                color: {colors['text_primary']};
                selection-background-color: {colors['primary']};
            }}
        """)
    
    def on_header_changed(self, index: int):
        """标题级别改变处理"""
        if index > 0:
            self.format_header.emit(index)