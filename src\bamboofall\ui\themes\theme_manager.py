"""
主题管理器

管理应用程序的主题和样式
"""

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QObject, pyqtSignal
from PyQt6.QtGui import QPalette, QColor
from typing import Dict, Any, Optional
import logging

# 尝试导入主题库，提供备选方案
try:
    import qdarktheme
    THEME_LIB = "qdarktheme"
except ImportError:
    try:
        import qdarkstyle
        THEME_LIB = "qdarkstyle"
    except ImportError:
        THEME_LIB = "none"

from ...utils.config_utils import get_config_manager

logger = logging.getLogger(__name__)


class ThemeManager(QObject):
    """主题管理器"""
    
    # 信号
    theme_changed = pyqtSignal(str)
    
    # 颜色定义
    COLORS = {
        "light": {
            "primary": "#2196F3",
            "primary_hover": "#1976D2",
            "secondary": "#757575",
            "secondary_hover": "#616161",
            "success": "#4CAF50",
            "warning": "#FF9800",
            "error": "#F44336",
            "accent": "#2196F3",
            "background": "#FAFAFA",
            "surface": "#FFFFFF",
            "surface_hover": "#F5F5F5",
            "text_primary": "#212121",
            "text_secondary": "#757575",
            "text_disabled": "#BDBDBD",
            "border": "#E0E0E0",
            "border_focus": "#2196F3",
            "shadow": "rgba(0, 0, 0, 0.1)",
        },
        "dark": {
            "primary": "#2196F3",
            "primary_hover": "#42A5F5",
            "secondary": "#757575",
            "secondary_hover": "#9E9E9E",
            "success": "#4CAF50",
            "warning": "#FF9800",
            "error": "#F44336",
            "accent": "#42A5F5",
            "background": "#121212",
            "surface": "#1E1E1E",
            "surface_hover": "#2C2C2C",
            "text_primary": "#FFFFFF",
            "text_secondary": "#AAAAAA",
            "text_disabled": "#616161",
            "border": "#373737",
            "border_focus": "#2196F3",
            "shadow": "rgba(0, 0, 0, 0.3)",
        }
    }
    
    # 字体定义
    FONTS = {
        "ui": "Source Han Sans CN, Microsoft YaHei UI, sans-serif",
        "editor": "JetBrains Mono, Consolas, 'Courier New', monospace",
        "code": "Fira Code, Monaco, 'Courier New', monospace"
    }
    
    def __init__(self):
        super().__init__()
        self.config_manager = get_config_manager()
        self.current_theme = "auto"
        self.current_colors = self.COLORS["light"]
        
    def set_theme(self, theme: str):
        """设置主题"""
        if theme not in ["light", "dark", "auto"]:
            logger.warning(f"未知主题: {theme}")
            return
        
        self.current_theme = theme
        
        # 应用主题库基础主题
        app = QApplication.instance()
        if THEME_LIB == "qdarktheme":
            if theme == "dark":
                qdarktheme.setup_theme("dark")
                self.current_colors = self.COLORS["dark"]
            elif theme == "light":
                qdarktheme.setup_theme("light")
                self.current_colors = self.COLORS["light"]
            else:  # auto
                qdarktheme.setup_theme("auto")
                if app and app.palette().color(QPalette.ColorRole.Window).lightness() < 128:
                    self.current_colors = self.COLORS["dark"]
                else:
                    self.current_colors = self.COLORS["light"]
        elif THEME_LIB == "qdarkstyle":
            if theme == "dark":
                if app:
                    app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt6())
                self.current_colors = self.COLORS["dark"]
            elif theme == "light":
                if app:
                    app.setStyleSheet("")  # 使用默认浅色主题
                self.current_colors = self.COLORS["light"]
            else:  # auto
                # 检测系统主题
                if app and app.palette().color(QPalette.ColorRole.Window).lightness() < 128:
                    app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt6())
                    self.current_colors = self.COLORS["dark"]
                else:
                    app.setStyleSheet("")
                    self.current_colors = self.COLORS["light"]
        else:
            # 没有主题库，使用基础样式
            logger.warning("未找到可用的主题库，使用基础样式")
            if theme == "dark":
                self.current_colors = self.COLORS["dark"]
            else:
                self.current_colors = self.COLORS["light"]
            if app:
                app.setStyleSheet("")  # 清除样式
        
        # 应用自定义样式
        self.apply_custom_styles()
        
        # 保存到配置
        self.config_manager.update_config(ui={"theme": theme})
        
        # 发送信号
        self.theme_changed.emit(theme)
        
        logger.info(f"主题已切换到: {theme} (使用 {THEME_LIB})")
    
    def apply_custom_styles(self):
        """应用自定义样式"""
        app = QApplication.instance()
        if not app:
            return
        
        custom_style = self.get_custom_stylesheet()
        current_style = app.styleSheet()
        app.setStyleSheet(current_style + "\n" + custom_style)
    
    def get_custom_stylesheet(self) -> str:
        """获取自定义样式表"""
        colors = self.current_colors
        
        return f"""
        /* 主窗口样式 */
        QMainWindow {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
            font-family: {self.FONTS['ui']};
        }}
        
        /* 按钮样式 */
        QPushButton {{
            background-color: {colors['primary']};
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            font-size: 14px;
            font-family: {self.FONTS['ui']};
            min-height: 20px;
        }}
        
        QPushButton:hover {{
            background-color: {colors['primary_hover']};
        }}
        
        QPushButton:pressed {{
            background-color: {colors['primary_hover']};
        }}
        
        QPushButton:disabled {{
            background-color: {colors['text_disabled']};
            color: {colors['text_secondary']};
        }}
        
        /* 次要按钮样式 */
        QPushButton[class="secondary"] {{
            background-color: {colors['secondary']};
        }}
        
        QPushButton[class="secondary"]:hover {{
            background-color: {colors['secondary_hover']};
        }}
        
        /* 文本框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            padding: 8px;
            color: {colors['text_primary']};
            font-family: {self.FONTS['ui']};
            selection-background-color: {colors['primary']};
        }}
        
        QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover {{
            border-color: {colors['border_focus']};
        }}
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {colors['primary']};
            border-width: 2px;
        }}
        
        /* 编辑器特殊样式 */
        QTextEdit[class="editor"] {{
            font-family: {self.FONTS['editor']};
            font-size: 14px;
            line-height: 1.6;
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 8px;
            padding: 16px;
        }}
        
        /* 标签样式 */
        QLabel {{
            color: {colors['text_primary']};
            font-family: {self.FONTS['ui']};
        }}
        
        QLabel[class="title"] {{
            font-size: 18px;
            font-weight: 600;
            color: {colors['text_primary']};
        }}
        
        QLabel[class="subtitle"] {{
            font-size: 14px;
            color: {colors['text_secondary']};
        }}
        
        /* 列表和树状控件样式 */
        QListWidget, QTreeWidget {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            color: {colors['text_primary']};
            font-family: {self.FONTS['ui']};
            outline: none;
        }}
        
        QListWidget::item, QTreeWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {colors['border']};
        }}
        
        QListWidget::item:hover, QTreeWidget::item:hover {{
            background-color: {colors['surface_hover']};
        }}
        
        QListWidget::item:selected, QTreeWidget::item:selected {{
            background-color: {colors['primary']};
            color: white;
        }}
        
        /* 分割器样式 */
        QSplitter::handle {{
            background-color: {colors['border']};
        }}
        
        QSplitter::handle:horizontal {{
            width: 1px;
        }}
        
        QSplitter::handle:vertical {{
            height: 1px;
        }}
        
        /* 选项卡样式 */
        QTabWidget::pane {{
            border: 1px solid {colors['border']};
            background-color: {colors['surface']};
            border-radius: 4px;
        }}
        
        QTabBar::tab {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            color: {colors['text_secondary']};
        }}
        
        QTabBar::tab:hover {{
            background-color: {colors['surface_hover']};
        }}
        
        QTabBar::tab:selected {{
            background-color: {colors['primary']};
            color: white;
            border-bottom: none;
        }}
        
        /* 菜单样式 */
        QMenuBar {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border-bottom: 1px solid {colors['border']};
        }}
        
        QMenuBar::item {{
            padding: 6px 12px;
            background-color: transparent;
        }}
        
        QMenuBar::item:hover {{
            background-color: {colors['surface_hover']};
        }}
        
        QMenu {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            color: {colors['text_primary']};
        }}
        
        QMenu::item {{
            padding: 6px 12px;
        }}
        
        QMenu::item:hover {{
            background-color: {colors['primary']};
            color: white;
        }}
        
        /* 工具栏样式 */
        QToolBar {{
            background-color: {colors['surface']};
            border: none;
            border-bottom: 1px solid {colors['border']};
            spacing: 4px;
        }}
        
        QToolButton {{
            background-color: transparent;
            border: none;
            border-radius: 4px;
            padding: 6px;
            color: {colors['text_primary']};
        }}
        
        QToolButton:hover {{
            background-color: {colors['surface_hover']};
        }}
        
        QToolButton:pressed {{
            background-color: {colors['primary']};
            color: white;
        }}
        
        /* 状态栏样式 */
        QStatusBar {{
            background-color: {colors['surface']};
            border-top: 1px solid {colors['border']};
            color: {colors['text_secondary']};
        }}
        
        /* 进度条样式 */
        QProgressBar {{
            border: 1px solid {colors['border']};
            border-radius: 4px;
            background-color: {colors['surface']};
            text-align: center;
            color: {colors['text_primary']};
        }}
        
        QProgressBar::chunk {{
            background-color: {colors['primary']};
            border-radius: 3px;
        }}
        
        /* 滚动条样式 */
        QScrollBar:vertical {{
            background-color: {colors['surface']};
            width: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:vertical {{
            background-color: {colors['text_disabled']};
            border-radius: 6px;
            min-height: 20px;
        }}
        
        QScrollBar::handle:vertical:hover {{
            background-color: {colors['text_secondary']};
        }}
        
        QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
            height: 0px;
        }}
        
        QScrollBar:horizontal {{
            background-color: {colors['surface']};
            height: 12px;
            border-radius: 6px;
        }}
        
        QScrollBar::handle:horizontal {{
            background-color: {colors['text_disabled']};
            border-radius: 6px;
            min-width: 20px;
        }}
        
        QScrollBar::handle:horizontal:hover {{
            background-color: {colors['text_secondary']};
        }}
        
        QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
            width: 0px;
        }}
        
        /* 对话框样式 */
        QDialog {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
        }}
        
        /* 分组框样式 */
        QGroupBox {{
            font-weight: bold;
            border: 1px solid {colors['border']};
            border-radius: 4px;
            margin-top: 10px;
            padding-top: 10px;
            color: {colors['text_primary']};
        }}
        
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }}
        
        /* 复选框和单选框样式 */
        QCheckBox, QRadioButton {{
            color: {colors['text_primary']};
            font-family: {self.FONTS['ui']};
        }}
        
        QCheckBox::indicator, QRadioButton::indicator {{
            width: 16px;
            height: 16px;
        }}
        
        QCheckBox::indicator:unchecked {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 3px;
        }}
        
        QCheckBox::indicator:checked {{
            background-color: {colors['primary']};
            border: 1px solid {colors['primary']};
            border-radius: 3px;
        }}
        
        /* 下拉框样式 */
        QComboBox {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            padding: 6px 12px;
            color: {colors['text_primary']};
            font-family: {self.FONTS['ui']};
        }}
        
        QComboBox:hover {{
            border-color: {colors['border_focus']};
        }}
        
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        
        QComboBox::down-arrow {{
            width: 8px;
            height: 8px;
        }}
        
        QComboBox QAbstractItemView {{
            background-color: {colors['surface']};
            border: 1px solid {colors['border']};
            border-radius: 4px;
            color: {colors['text_primary']};
            selection-background-color: {colors['primary']};
        }}
        """
    
    def get_button_style(self, button_type: str = "primary") -> str:
        """获取特定类型按钮的样式"""
        colors = self.current_colors
        
        styles = {
            "primary": f"""
                QPushButton {{
                    background-color: {colors['primary']};
                    color: white;
                }}
                QPushButton:hover {{
                    background-color: {colors['primary_hover']};
                }}
            """,
            "secondary": f"""
                QPushButton {{
                    background-color: {colors['secondary']};
                    color: white;
                }}
                QPushButton:hover {{
                    background-color: {colors['secondary_hover']};
                }}
            """,
            "success": f"""
                QPushButton {{
                    background-color: {colors['success']};
                    color: white;
                }}
            """,
            "warning": f"""
                QPushButton {{
                    background-color: {colors['warning']};
                    color: white;
                }}
            """,
            "error": f"""
                QPushButton {{
                    background-color: {colors['error']};
                    color: white;
                }}
            """
        }
        
        return styles.get(button_type, styles["primary"])
    
    def get_color(self, color_name: str) -> str:
        """获取当前主题的颜色"""
        return self.current_colors.get(color_name, "#000000")
    
    def get_font(self, font_type: str) -> str:
        """获取字体"""
        return self.FONTS.get(font_type, self.FONTS["ui"])


# 全局主题管理器实例
_theme_manager: Optional[ThemeManager] = None


def get_theme_manager() -> ThemeManager:
    """获取全局主题管理器"""
    global _theme_manager
    if _theme_manager is None:
        _theme_manager = ThemeManager()
    return _theme_manager


def set_theme(theme: str):
    """设置全局主题"""
    theme_manager = get_theme_manager()
    theme_manager.set_theme(theme)


def get_color(color_name: str) -> str:
    """获取当前主题颜色"""
    theme_manager = get_theme_manager()
    return theme_manager.get_color(color_name)