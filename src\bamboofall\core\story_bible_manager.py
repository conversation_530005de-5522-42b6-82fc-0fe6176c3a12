"""
故事圣经管理器

管理项目的故事圣经，包括角色、情节、世界观等核心设定
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
import json
from pathlib import Path

from ..models.project import Project
from ..models.character import Character
from ..models.scene import Scene  
from ..models.chapter import Chapter
from ..utils.logger import LoggerMixin
from ..utils.config_utils import get_config_manager


@dataclass
class StoryBible:
    """故事圣经数据结构"""
    project_id: str
    summary: str = ""
    theme: str = ""
    tone_style: str = ""
    target_audience: str = ""
    world_settings: Dict[str, Any] = field(default_factory=dict)
    character_profiles: Dict[str, Any] = field(default_factory=dict)
    plot_structure: Dict[str, Any] = field(default_factory=dict)
    timeline: List[Dict[str, Any]] = field(default_factory=list)
    locations: Dict[str, Any] = field(default_factory=dict)
    rules_constraints: Dict[str, Any] = field(default_factory=dict)
    reference_materials: List[Dict[str, str]] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "project_id": self.project_id,
            "summary": self.summary,
            "theme": self.theme, 
            "tone_style": self.tone_style,
            "target_audience": self.target_audience,
            "world_settings": self.world_settings,
            "character_profiles": self.character_profiles,
            "plot_structure": self.plot_structure,
            "timeline": self.timeline,
            "locations": self.locations,
            "rules_constraints": self.rules_constraints,
            "reference_materials": self.reference_materials
        }


class StoryBibleManager(LoggerMixin):
    """故事圣经管理器"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.story_bibles: Dict[str, StoryBible] = {}
        self.logger.info("故事圣经管理器初始化完成")
    
    def create_story_bible(self, project: Project) -> StoryBible:
        """创建故事圣经"""
        story_bible = StoryBible(
            project_id=project.id,
            summary=project.description,
            target_audience=project.target_audience or "一般读者",
            world_settings={
                "genre": project.genre,
                "setting": project.world_building or "",
                "time_period": "现代",
                "location": "未指定"
            }
        )
        
        self.story_bibles[project.id] = story_bible
        self.logger.info(f"已创建项目故事圣经: {project.name}")
        return story_bible
    
    def get_story_bible(self, project_id: str) -> Optional[StoryBible]:
        """获取故事圣经"""
        return self.story_bibles.get(project_id)
    
    def update_character_profile(
        self, 
        project_id: str, 
        character: Character
    ) -> bool:
        """更新角色档案"""
        story_bible = self.get_story_bible(project_id)
        if not story_bible:
            return False
        
        profile = {
            "name": character.name,
            "description": character.description,
            "age": character.age,
            "gender": character.gender,
            "occupation": character.occupation,
            "personality_traits": character.personality_traits,
            "background": character.background,
            "abilities": character.abilities,
            "relationships": character.relationships,
            "goals": character.goals,
            "conflicts": character.conflicts,
            "arc_development": character.arc_development,
            "appearance": character.appearance
        }
        
        story_bible.character_profiles[character.id] = profile
        self.logger.info(f"已更新角色档案: {character.name}")
        return True
    
    def update_world_settings(
        self,
        project_id: str,
        settings: Dict[str, Any]
    ) -> bool:
        """更新世界观设定"""
        story_bible = self.get_story_bible(project_id)
        if not story_bible:
            return False
        
        story_bible.world_settings.update(settings)
        self.logger.info("已更新世界观设定")
        return True
    
    def add_location(
        self,
        project_id: str,
        location_name: str,
        description: str,
        importance: str = "一般",
        **metadata
    ) -> bool:
        """添加地点信息"""
        story_bible = self.get_story_bible(project_id)
        if not story_bible:
            return False
        
        location_info = {
            "name": location_name,
            "description": description,
            "importance": importance,
            **metadata
        }
        
        story_bible.locations[location_name] = location_info
        self.logger.info(f"已添加地点: {location_name}")
        return True