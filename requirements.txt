# 笔落App Python版本 核心依赖
# GUI框架
PyQt6>=6.4.0
qdarktheme>=2.0.0
qtawesome>=1.2.0

# 数据库和ORM
SQLAlchemy>=1.4.0
alembic>=1.8.0

# 数据验证和模型
pydantic>=1.10.0
dataclasses-json>=0.5.0

# AI服务集成
openai>=1.0.0
anthropic>=0.7.0
aiohttp>=3.8.0
httpx>=0.24.0

# 文本处理和NLP
markdown>=3.4.0
nltk>=3.7.0
spacy>=3.5.0
whoosh>=2.7.0

# 可视化
matplotlib>=3.6.0
plotly>=5.15.0
networkx>=3.0.0

# 文档导出
reportlab>=3.6.0
python-docx>=0.8.0
ebooklib>=0.17.0
fpdf2>=2.5.0

# 模板引擎
jinja2>=3.0.0

# 配置和工具
toml>=0.10.0
python-dotenv>=0.19.0
click>=8.0.0

# 日志和调试
loguru>=0.6.0

# 文件处理
pathlib2>=2.3.0
watchdog>=2.1.0

# 异步支持
asyncio-mqtt>=0.11.0
trio>=0.20.0

# 加密和安全
cryptography>=38.0.0
keyring>=23.0.0