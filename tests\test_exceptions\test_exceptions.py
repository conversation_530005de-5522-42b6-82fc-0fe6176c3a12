"""异常处理模块测试

测试统一异常处理机制的各个组件。
"""

import pytest
import logging
import time
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

from bamboofall.exceptions import (
    # 基础异常
    BambooFallError,
    BambooFallWarning,
    ErrorCode,
    ErrorSeverity,
    
    # 业务异常
    ProjectError,
    ProjectNotFoundError,
    AIServiceError,
    DatabaseError,
    FileOperationError,
    ValidationError,
    NetworkError,
    TimeoutError,
    
    # 处理器
    GlobalExceptionHandler,
    UIExceptionHandler,
    ServiceExceptionHandler,
    
    # 装饰器
    handle_exceptions,
    handle_async_exceptions,
    retry_on_exception,
    log_exceptions,
    validate_input,
    timeout_handler,
    circuit_breaker,
    
    # 工具函数
    format_exception,
    sanitize_exception_message,
    get_exception_chain,
    analyze_exception_severity,
    create_error_report,
    is_recoverable_error,
    get_exception_suggestions,
    format_user_friendly_message
)


class TestBaseExceptions:
    """测试基础异常类"""
    
    def test_bamboo_fall_error_creation(self):
        """测试BambooFallError创建"""
        error = BambooFallError(
            message="Test error",
            error_code=ErrorCode.PROJECT_NOT_FOUND,
            severity=ErrorSeverity.HIGH,
            details={"key": "value"},
            user_message="用户友好消息"
        )
        
        assert str(error) == "Test error"
        assert error.error_code == ErrorCode.PROJECT_NOT_FOUND
        assert error.severity == ErrorSeverity.HIGH
        assert error.details == {"key": "value"}
        assert error.user_message == "用户友好消息"
        assert error.timestamp is not None
    
    def test_bamboo_fall_error_with_cause(self):
        """测试带原因的BambooFallError"""
        cause = ValueError("Original error")
        error = BambooFallError(
            message="Wrapped error",
            cause=cause
        )
        
        assert error.cause == cause
        assert "Original error" in str(error.cause)
    
    def test_bamboo_fall_warning(self):
        """测试BambooFallWarning"""
        warning = BambooFallWarning(
            message="Test warning",
            severity=ErrorSeverity.LOW
        )
        
        assert str(warning) == "Test warning"
        assert warning.severity == ErrorSeverity.LOW


class TestBusinessExceptions:
    """测试业务异常类"""
    
    def test_project_not_found_error(self):
        """测试ProjectNotFoundError"""
        error = ProjectNotFoundError(
            project_id="test_project",
            details={"search_path": "/path/to/projects"}
        )
        
        assert "test_project" in str(error)
        assert error.error_code == ErrorCode.PROJECT_NOT_FOUND
        assert error.severity == ErrorSeverity.MEDIUM
    
    def test_ai_service_error(self):
        """测试AIServiceError"""
        error = AIServiceError(
            service="openai",
            operation="generate_text",
            details={"model": "gpt-4"}
        )
        
        assert "openai" in str(error)
        assert "generate_text" in str(error)
        assert error.error_code == ErrorCode.AI_SERVICE_ERROR
    
    def test_database_error(self):
        """测试DatabaseError"""
        error = DatabaseError(
            operation="insert",
            table="projects",
            details={"constraint": "unique_name"}
        )
        
        assert "insert" in str(error)
        assert "projects" in str(error)
        assert error.error_code == ErrorCode.DATABASE_ERROR


class TestExceptionHandlers:
    """测试异常处理器"""
    
    def test_global_exception_handler(self):
        """测试全局异常处理器"""
        handler = GlobalExceptionHandler()
        
        # 测试处理异常
        test_exception = ValueError("Test error")
        result = handler.handle_exception(test_exception)
        
        assert result is not None
        assert "error_id" in result
        assert "timestamp" in result
    
    def test_ui_exception_handler(self):
        """测试UI异常处理器"""
        handler = UIExceptionHandler()
        
        # 模拟UI错误
        ui_error = RuntimeError("UI component error")
        result = handler.handle_exception(ui_error)
        
        assert result is not None
        assert result.get("user_message") is not None
    
    def test_service_exception_handler(self):
        """测试服务异常处理器"""
        handler = ServiceExceptionHandler()
        
        # 测试服务错误
        service_error = AIServiceError(
            service="test_service",
            operation="test_operation"
        )
        result = handler.handle_exception(service_error)
        
        assert result is not None
        assert "retry_suggested" in result


class TestExceptionDecorators:
    """测试异常装饰器"""
    
    def test_handle_exceptions_decorator(self):
        """测试异常处理装饰器"""
        @handle_exceptions(default_return="error")
        def test_function():
            raise ValueError("Test error")
        
        result = test_function()
        assert result == "error"
    
    def test_retry_on_exception_decorator(self):
        """测试重试装饰器"""
        call_count = 0
        
        @retry_on_exception(max_retries=3, delay=0.1)
        def test_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ConnectionError("Network error")
            return "success"
        
        result = test_function()
        assert result == "success"
        assert call_count == 3
    
    def test_log_exceptions_decorator(self, caplog):
        """测试异常日志装饰器"""
        @log_exceptions()
        def test_function():
            raise ValueError("Test error")
        
        with pytest.raises(ValueError):
            test_function()
        
        assert "Test error" in caplog.text
    
    def test_validate_input_decorator(self):
        """测试输入验证装饰器"""
        def validate_positive(value):
            return value > 0
        
        @validate_input(value=validate_positive)
        def test_function(value):
            return value * 2
        
        # 有效输入
        result = test_function(5)
        assert result == 10
        
        # 无效输入
        with pytest.raises(ValidationError):
            test_function(-1)
    
    def test_timeout_handler_decorator(self):
        """测试超时处理装饰器"""
        @timeout_handler(timeout=0.1)
        def slow_function():
            time.sleep(0.2)
            return "completed"
        
        with pytest.raises(TimeoutError):
            slow_function()
    
    def test_circuit_breaker_decorator(self):
        """测试断路器装饰器"""
        call_count = 0
        
        @circuit_breaker(failure_threshold=2, recovery_timeout=0.1)
        def unreliable_function():
            nonlocal call_count
            call_count += 1
            if call_count <= 3:
                raise ConnectionError("Service unavailable")
            return "success"
        
        # 前两次调用应该失败
        with pytest.raises(ConnectionError):
            unreliable_function()
        
        with pytest.raises(ConnectionError):
            unreliable_function()
        
        # 第三次调用应该被断路器阻止
        with pytest.raises(Exception):  # 断路器异常
            unreliable_function()


class TestExceptionUtils:
    """测试异常工具函数"""
    
    def test_format_exception(self):
        """测试异常格式化"""
        error = BambooFallError(
            message="Test error",
            error_code=ErrorCode.PROJECT_NOT_FOUND,
            severity=ErrorSeverity.HIGH
        )
        
        formatted = format_exception(error)
        
        assert formatted["type"] == "BambooFallError"
        assert formatted["message"] == "Test error"
        assert formatted["error_code"] == ErrorCode.PROJECT_NOT_FOUND.value
        assert formatted["severity"] == ErrorSeverity.HIGH.value
        assert "timestamp" in formatted
    
    def test_sanitize_exception_message(self):
        """测试异常消息清理"""
        message = "Error: password=secret123 and token=abc123"
        sanitized = sanitize_exception_message(message)
        
        assert "secret123" not in sanitized
        assert "abc123" not in sanitized
        assert "[REDACTED]" in sanitized
    
    def test_get_exception_chain(self):
        """测试异常链获取"""
        root_error = ValueError("Root error")
        middle_error = RuntimeError("Middle error")
        middle_error.__cause__ = root_error
        top_error = BambooFallError("Top error")
        top_error.__cause__ = middle_error
        
        chain = get_exception_chain(top_error)
        
        assert len(chain) == 3
        assert isinstance(chain[0], ValueError)
        assert isinstance(chain[1], RuntimeError)
        assert isinstance(chain[2], BambooFallError)
    
    def test_analyze_exception_severity(self):
        """测试异常严重程度分析"""
        # 测试自定义异常
        custom_error = BambooFallError(
            message="Test",
            severity=ErrorSeverity.CRITICAL
        )
        assert analyze_exception_severity(custom_error) == ErrorSeverity.CRITICAL
        
        # 测试系统异常
        memory_error = MemoryError("Out of memory")
        assert analyze_exception_severity(memory_error) == ErrorSeverity.CRITICAL
        
        # 测试普通异常
        value_error = ValueError("Invalid value")
        assert analyze_exception_severity(value_error) == ErrorSeverity.MEDIUM
    
    def test_create_error_report(self):
        """测试错误报告创建"""
        error = BambooFallError(
            message="Test error",
            error_code=ErrorCode.PROJECT_NOT_FOUND
        )
        
        context = {"user_id": "test_user", "action": "load_project"}
        report = create_error_report(error, context)
        
        assert "timestamp" in report
        assert "exception" in report
        assert "severity" in report
        assert "chain" in report
        assert "system_info" in report
        assert "context" in report
        assert report["context"] == context
    
    def test_is_recoverable_error(self):
        """测试异常可恢复性判断"""
        # 不可恢复的异常
        assert not is_recoverable_error(SystemExit())
        assert not is_recoverable_error(MemoryError())
        assert not is_recoverable_error(SyntaxError())
        
        # 可恢复的异常
        assert is_recoverable_error(ConnectionError())
        assert is_recoverable_error(FileNotFoundError())
        assert is_recoverable_error(ValueError())
        
        # 自定义异常
        low_severity_error = BambooFallError(
            message="Test",
            severity=ErrorSeverity.LOW
        )
        assert is_recoverable_error(low_severity_error)
        
        critical_error = BambooFallError(
            message="Test",
            severity=ErrorSeverity.CRITICAL
        )
        assert not is_recoverable_error(critical_error)
    
    def test_get_exception_suggestions(self):
        """测试异常处理建议获取"""
        # 文件未找到异常
        suggestions = get_exception_suggestions(FileNotFoundError())
        assert any("文件路径" in s for s in suggestions)
        
        # 连接错误异常
        suggestions = get_exception_suggestions(ConnectionError())
        assert any("网络连接" in s for s in suggestions)
        
        # 自定义异常带建议
        custom_error = BambooFallError(
            message="Test",
            details={"suggestions": ["自定义建议1", "自定义建议2"]}
        )
        suggestions = get_exception_suggestions(custom_error)
        assert "自定义建议1" in suggestions
        assert "自定义建议2" in suggestions
    
    def test_format_user_friendly_message(self):
        """测试用户友好消息格式化"""
        # 自定义异常带用户消息
        custom_error = BambooFallError(
            message="Technical error",
            user_message="用户友好的错误消息"
        )
        message = format_user_friendly_message(custom_error)
        assert message == "用户友好的错误消息"
        
        # 标准异常
        file_error = FileNotFoundError("File not found")
        message = format_user_friendly_message(file_error)
        assert "找不到指定的文件" in message
        
        # 未知异常
        unknown_error = RuntimeError("Unknown error")
        message = format_user_friendly_message(unknown_error)
        assert "发生了一个错误" in message


class TestIntegration:
    """集成测试"""
    
    def test_complete_exception_handling_flow(self, caplog):
        """测试完整的异常处理流程"""
        # 创建全局异常处理器
        global_handler = GlobalExceptionHandler()
        
        # 使用装饰器的函数
        @handle_exceptions()
        @log_exceptions()
        @retry_on_exception(max_retries=2, delay=0.1)
        def complex_operation(should_fail=True):
            if should_fail:
                raise AIServiceError(
                    service="test_service",
                    operation="test_operation",
                    details={"model": "test_model"}
                )
            return "success"
        
        # 测试失败情况
        result = complex_operation(should_fail=True)
        assert result is None  # 默认返回值
        
        # 检查日志
        assert "AIServiceError" in caplog.text
        
        # 测试成功情况（第三次重试成功）
        call_count = 0
        
        @retry_on_exception(max_retries=3, delay=0.1)
        def eventually_successful():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise NetworkError("Temporary network issue")
            return "finally_success"
        
        result = eventually_successful()
        assert result == "finally_success"
        assert call_count == 3
    
    def test_exception_chain_handling(self):
        """测试异常链处理"""
        try:
            try:
                raise ValueError("Original error")
            except ValueError as e:
                raise ProjectError(
                    project_id="test",
                    message="Project operation failed"
                ) from e
        except ProjectError as e:
            # 测试异常链
            chain = get_exception_chain(e)
            assert len(chain) == 2
            assert isinstance(chain[0], ValueError)
            assert isinstance(chain[1], ProjectError)
            
            # 测试错误报告
            report = create_error_report(e)
            assert len(report["chain"]) == 2
            
            # 测试格式化
            formatted = format_exception(e)
            assert formatted["type"] == "ProjectError"
    
    @pytest.mark.asyncio
    async def test_async_exception_handling(self):
        """测试异步异常处理"""
        @handle_async_exceptions()
        async def async_operation():
            raise AIServiceError(
                service="async_service",
                operation="async_operation"
            )
        
        result = await async_operation()
        assert result is None  # 默认返回值


if __name__ == "__main__":
    pytest.main([__file__, "-v"])