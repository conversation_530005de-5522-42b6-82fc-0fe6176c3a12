"""
角色数据模型

定义小说中的角色信息结构
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
import uuid
import json


class Gender(Enum):
    """性别枚举"""
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"
    UNKNOWN = "unknown"


class CharacterType(Enum):
    """角色类型枚举"""
    PROTAGONIST = "protagonist"      # 主角
    ANTAGONIST = "antagonist"        # 反派
    SUPPORTING = "supporting"        # 配角
    MINOR = "minor"                  # 龙套
    CAMEO = "cameo"                  # 客串


class CharacterStatus(Enum):
    """角色状态枚举"""
    ALIVE = "alive"                  # 活着
    DEAD = "dead"                    # 死亡
    MISSING = "missing"              # 失踪
    UNKNOWN = "unknown"              # 未知


@dataclass
class CharacterAbility:
    """角色能力"""
    name: str                        # 能力名称
    description: str                 # 能力描述
    level: int = 1                   # 能力等级(1-10)
    type: str = "general"            # 能力类型
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "name": self.name,
            "description": self.description,
            "level": self.level,
            "type": self.type,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CharacterAbility":
        """从字典创建"""
        return cls(**data)


@dataclass
class CharacterRelationship:
    """角色关系"""
    target_character_id: str         # 目标角色ID
    relationship_type: str           # 关系类型
    description: str                 # 关系描述
    strength: int = 5                # 关系强度(1-10)
    is_mutual: bool = True           # 是否双向关系
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "target_character_id": self.target_character_id,
            "relationship_type": self.relationship_type,
            "description": self.description,
            "strength": self.strength,
            "is_mutual": self.is_mutual,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CharacterRelationship":
        """从字典创建"""
        return cls(**data)


@dataclass
class Character:
    """角色模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    project_id: str = ""
    name: str = ""
    
    # 基本信息
    age: Optional[int] = None
    gender: Gender = Gender.UNKNOWN
    type: CharacterType = CharacterType.MINOR
    status: CharacterStatus = CharacterStatus.ALIVE
    
    # 外貌描述
    appearance: str = ""
    height: Optional[str] = None
    weight: Optional[str] = None
    hair_color: Optional[str] = None
    eye_color: Optional[str] = None
    
    # 性格和背景
    personality: str = ""
    background: str = ""
    motivation: str = ""
    goal: str = ""
    conflict: str = ""
    
    # 职业和身份
    occupation: Optional[str] = None
    social_status: Optional[str] = None
    education: Optional[str] = None
    
    # 能力系统
    abilities: List[CharacterAbility] = field(default_factory=list)
    
    # 关系网络
    relationships: List[CharacterRelationship] = field(default_factory=list)
    
    # 重要事件
    important_events: List[str] = field(default_factory=list)
    
    # 语言特征
    speech_pattern: str = ""
    catchphrase: str = ""
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # 标签和分类
    tags: List[str] = field(default_factory=list)
    
    # 扩展属性
    custom_fields: Dict[str, Any] = field(default_factory=dict)
    
    # 头像路径
    avatar_path: Optional[str] = None
    
    # 备注
    notes: str = ""
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()
    
    def add_ability(self, ability: CharacterAbility):
        """添加能力"""
        self.abilities.append(ability)
        self.update_timestamp()
    
    def remove_ability(self, ability_name: str):
        """移除能力"""
        self.abilities = [a for a in self.abilities if a.name != ability_name]
        self.update_timestamp()
    
    def add_relationship(self, relationship: CharacterRelationship):
        """添加关系"""
        self.relationships.append(relationship)
        self.update_timestamp()
    
    def remove_relationship(self, target_character_id: str):
        """移除关系"""
        self.relationships = [r for r in self.relationships if r.target_character_id != target_character_id]
        self.update_timestamp()
    
    def get_relationships_by_type(self, relationship_type: str) -> List[CharacterRelationship]:
        """根据类型获取关系"""
        return [r for r in self.relationships if r.relationship_type == relationship_type]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "project_id": self.project_id,
            "name": self.name,
            "age": self.age,
            "gender": self.gender.value,
            "type": self.type.value,
            "status": self.status.value,
            "appearance": self.appearance,
            "height": self.height,
            "weight": self.weight,
            "hair_color": self.hair_color,
            "eye_color": self.eye_color,
            "personality": self.personality,
            "background": self.background,
            "motivation": self.motivation,
            "goal": self.goal,
            "conflict": self.conflict,
            "occupation": self.occupation,
            "social_status": self.social_status,
            "education": self.education,
            "abilities": [a.to_dict() for a in self.abilities],
            "relationships": [r.to_dict() for r in self.relationships],
            "important_events": self.important_events,
            "speech_pattern": self.speech_pattern,
            "catchphrase": self.catchphrase,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "tags": self.tags,
            "custom_fields": self.custom_fields,
            "avatar_path": self.avatar_path,
            "notes": self.notes,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Character":
        """从字典创建角色"""
        # 处理枚举类型
        gender = Gender(data.get("gender", "unknown"))
        char_type = CharacterType(data.get("type", "minor"))
        status = CharacterStatus(data.get("status", "alive"))
        
        # 处理时间
        created_at = datetime.fromisoformat(data["created_at"]) if data.get("created_at") else datetime.now()
        updated_at = datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else datetime.now()
        
        # 处理能力
        abilities_data = data.get("abilities", [])
        abilities = [CharacterAbility.from_dict(a) for a in abilities_data]
        
        # 处理关系
        relationships_data = data.get("relationships", [])
        relationships = [CharacterRelationship.from_dict(r) for r in relationships_data]
        
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            project_id=data.get("project_id", ""),
            name=data.get("name", ""),
            age=data.get("age"),
            gender=gender,
            type=char_type,
            status=status,
            appearance=data.get("appearance", ""),
            height=data.get("height"),
            weight=data.get("weight"),
            hair_color=data.get("hair_color"),
            eye_color=data.get("eye_color"),
            personality=data.get("personality", ""),
            background=data.get("background", ""),
            motivation=data.get("motivation", ""),
            goal=data.get("goal", ""),
            conflict=data.get("conflict", ""),
            occupation=data.get("occupation"),
            social_status=data.get("social_status"),
            education=data.get("education"),
            abilities=abilities,
            relationships=relationships,
            important_events=data.get("important_events", []),
            speech_pattern=data.get("speech_pattern", ""),
            catchphrase=data.get("catchphrase", ""),
            created_at=created_at,
            updated_at=updated_at,
            tags=data.get("tags", []),
            custom_fields=data.get("custom_fields", {}),
            avatar_path=data.get("avatar_path"),
            notes=data.get("notes", ""),
        )
    
    def __str__(self) -> str:
        return f"Character(id={self.id}, name={self.name}, type={self.type.value})"