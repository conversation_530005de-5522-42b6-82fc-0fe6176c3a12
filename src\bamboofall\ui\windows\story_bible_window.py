"""故事圣经窗口类

用于管理小说的世界观、角色、设定等信息。
"""

from typing import Optional, Dict, Any, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter, QTabWidget,
    QTreeWidget, QTreeWidgetItem, QTextEdit, QLineEdit, QLabel,
    QPushButton, QFrame, QGroupBox, QFormLayout, QComboBox,
    QSpinBox, QDateEdit, QPlainTextEdit, QScrollArea, QListWidget,
    QListWidgetItem, QTableWidget, QTableWidgetItem, QHeaderView,
    QToolBar, QMenuBar, QMenu, QStatusBar, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QDate, QTimer, QThread, pyqtSlot
from PyQt6.QtGui import QAction, QIcon, QPixmap, QFont, QTextDocument
import json
import os
from pathlib import Path
from datetime import datetime

from .base_window import BaseWindow
from ..widgets.modern_widgets import ModernButton
from ..themes.theme_manager import get_theme_manager
from ...core.project_manager import ProjectManager
from ...core.character_manager import CharacterManager
from ...services.ai_service import AIService
from ...utils.config_utils import get_config_manager
from ...exceptions import handle_exceptions, ProjectNotFoundError


class StoryBibleWindow(BaseWindow):
    """故事圣经窗口类
    
    功能：
    - 世界观管理
    - 角色档案管理
    - 设定资料管理
    - 时间线管理
    - 地点管理
    - 关系图谱
    - AI辅助生成
    """
    
    # 信号
    character_selected = pyqtSignal(str)  # 角色ID
    world_setting_changed = pyqtSignal(str, dict)  # 设定类型, 设定数据
    timeline_updated = pyqtSignal(list)  # 时间线事件列表
    relationship_changed = pyqtSignal(str, str, str)  # 角色1, 角色2, 关系类型
    ai_generation_requested = pyqtSignal(str, dict)  # 生成类型, 参数
    
    def __init__(self, project_manager: ProjectManager, parent=None):
        # 管理器
        self.project_manager = project_manager
        self.character_manager = CharacterManager(project_manager)
        self.theme_manager = get_theme_manager()
        self.config_manager = get_config_manager()
        self.ai_service = AIService()
        
        # UI组件
        self.central_widget = None
        self.main_splitter = None
        self.left_panel = None
        self.right_panel = None
        
        # 左侧面板
        self.category_tree = None
        self.search_edit = None
        self.add_btn = None
        self.delete_btn = None
        
        # 右侧面板
        self.content_tabs = None
        self.character_tab = None
        self.world_tab = None
        self.timeline_tab = None
        self.location_tab = None
        self.relationship_tab = None
        
        # 角色管理
        self.character_form = None
        self.character_portrait = None
        self.character_notes = None
        
        # 世界观管理
        self.world_categories = None
        self.world_content = None
        
        # 时间线管理
        self.timeline_table = None
        self.timeline_details = None
        
        # 地点管理
        self.location_tree = None
        self.location_details = None
        
        # 关系图谱
        self.relationship_view = None
        
        # 数据
        self.current_project = None
        self.current_item = None
        self.story_bible_data = {}
        
        super().__init__(parent)
        
        # 加载项目数据
        self.load_project_data()
    
    def setup_window_properties(self):
        """设置窗口属性"""
        self.setWindowTitle("笔落 - 故事圣经")
        self.setMinimumSize(1000, 700)
        self.resize(1400, 900)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(self.central_widget)
        main_layout.setContentsMargins(8, 8, 8, 8)
        main_layout.setSpacing(8)
        
        # 创建分割器
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(self.main_splitter)
        
        # 创建左右面板
        self.setup_left_panel()
        self.setup_right_panel()
        
        # 设置分割器比例
        self.main_splitter.setSizes([300, 1100])
        self.main_splitter.setStretchFactor(0, 0)
        self.main_splitter.setStretchFactor(1, 1)
    
    def setup_left_panel(self):
        """设置左侧面板"""
        self.left_panel = QWidget()
        left_layout = QVBoxLayout(self.left_panel)
        left_layout.setContentsMargins(8, 8, 8, 8)
        left_layout.setSpacing(8)
        
        # 搜索框
        search_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索...")
        self.search_edit.textChanged.connect(self.on_search_changed)
        search_layout.addWidget(self.search_edit)
        
        search_btn = QPushButton("🔍")
        search_btn.setFixedSize(30, 30)
        search_btn.clicked.connect(self.perform_search)
        search_layout.addWidget(search_btn)
        
        left_layout.addLayout(search_layout)
        
        # 分类树
        self.category_tree = QTreeWidget()
        self.category_tree.setHeaderLabel("故事圣经")
        self.category_tree.itemClicked.connect(self.on_category_selected)
        self.category_tree.itemDoubleClicked.connect(self.on_category_double_clicked)
        self.setup_category_tree()
        left_layout.addWidget(self.category_tree)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.add_btn = ModernButton("添加")
        self.add_btn.clicked.connect(self.add_item)
        button_layout.addWidget(self.add_btn)
        
        self.delete_btn = ModernButton("删除")
        self.delete_btn.clicked.connect(self.delete_item)
        button_layout.addWidget(self.delete_btn)
        
        left_layout.addLayout(button_layout)
        
        self.main_splitter.addWidget(self.left_panel)
    
    def setup_right_panel(self):
        """设置右侧面板"""
        self.right_panel = QWidget()
        right_layout = QVBoxLayout(self.right_panel)
        right_layout.setContentsMargins(8, 8, 8, 8)
        right_layout.setSpacing(8)
        
        # 创建标签页
        self.content_tabs = QTabWidget()
        self.content_tabs.setTabPosition(QTabWidget.TabPosition.North)
        
        # 创建各个标签页
        self.setup_character_tab()
        self.setup_world_tab()
        self.setup_timeline_tab()
        self.setup_location_tab()
        self.setup_relationship_tab()
        
        right_layout.addWidget(self.content_tabs)
        
        self.main_splitter.addWidget(self.right_panel)
    
    def setup_category_tree(self):
        """设置分类树"""
        # 角色
        characters_item = QTreeWidgetItem(["角色"])
        characters_item.setData(0, Qt.ItemDataRole.UserRole, {"type": "category", "category": "characters"})
        self.category_tree.addTopLevelItem(characters_item)
        
        # 世界观
        world_item = QTreeWidgetItem(["世界观"])
        world_item.setData(0, Qt.ItemDataRole.UserRole, {"type": "category", "category": "world"})
        
        # 世界观子分类
        world_subcategories = [
            ("设定总览", "overview"),
            ("历史背景", "history"),
            ("地理环境", "geography"),
            ("社会制度", "society"),
            ("文化习俗", "culture"),
            ("科技水平", "technology"),
            ("魔法体系", "magic"),
            ("种族设定", "races")
        ]
        
        for name, key in world_subcategories:
            sub_item = QTreeWidgetItem([name])
            sub_item.setData(0, Qt.ItemDataRole.UserRole, {"type": "subcategory", "category": "world", "subcategory": key})
            world_item.addChild(sub_item)
        
        self.category_tree.addTopLevelItem(world_item)
        
        # 时间线
        timeline_item = QTreeWidgetItem(["时间线"])
        timeline_item.setData(0, Qt.ItemDataRole.UserRole, {"type": "category", "category": "timeline"})
        self.category_tree.addTopLevelItem(timeline_item)
        
        # 地点
        locations_item = QTreeWidgetItem(["地点"])
        locations_item.setData(0, Qt.ItemDataRole.UserRole, {"type": "category", "category": "locations"})
        self.category_tree.addTopLevelItem(locations_item)
        
        # 关系图谱
        relationships_item = QTreeWidgetItem(["关系图谱"])
        relationships_item.setData(0, Qt.ItemDataRole.UserRole, {"type": "category", "category": "relationships"})
        self.category_tree.addTopLevelItem(relationships_item)
        
        # 展开所有项目
        self.category_tree.expandAll()
    
    def setup_character_tab(self):
        """设置角色标签页"""
        self.character_tab = QScrollArea()
        character_widget = QWidget()
        character_layout = QVBoxLayout(character_widget)
        character_layout.setSpacing(16)
        
        # 角色基本信息
        basic_group = QGroupBox("基本信息")
        self.character_form = QFormLayout(basic_group)
        
        # 角色名称
        self.char_name_edit = QLineEdit()
        self.char_name_edit.textChanged.connect(self.on_character_data_changed)
        self.character_form.addRow("姓名:", self.char_name_edit)
        
        # 角色别名
        self.char_alias_edit = QLineEdit()
        self.char_alias_edit.textChanged.connect(self.on_character_data_changed)
        self.character_form.addRow("别名/昵称:", self.char_alias_edit)
        
        # 年龄
        self.char_age_spin = QSpinBox()
        self.char_age_spin.setRange(0, 1000)
        self.char_age_spin.valueChanged.connect(self.on_character_data_changed)
        self.character_form.addRow("年龄:", self.char_age_spin)
        
        # 性别
        self.char_gender_combo = QComboBox()
        self.char_gender_combo.addItems(["男", "女", "其他", "未知"])
        self.char_gender_combo.currentTextChanged.connect(self.on_character_data_changed)
        self.character_form.addRow("性别:", self.char_gender_combo)
        
        # 职业
        self.char_occupation_edit = QLineEdit()
        self.char_occupation_edit.textChanged.connect(self.on_character_data_changed)
        self.character_form.addRow("职业:", self.char_occupation_edit)
        
        # 出生地
        self.char_birthplace_edit = QLineEdit()
        self.char_birthplace_edit.textChanged.connect(self.on_character_data_changed)
        self.character_form.addRow("出生地:", self.char_birthplace_edit)
        
        character_layout.addWidget(basic_group)
        
        # 外貌描述
        appearance_group = QGroupBox("外貌描述")
        appearance_layout = QVBoxLayout(appearance_group)
        
        self.char_appearance_edit = QTextEdit()
        self.char_appearance_edit.setMaximumHeight(100)
        self.char_appearance_edit.textChanged.connect(self.on_character_data_changed)
        appearance_layout.addWidget(self.char_appearance_edit)
        
        # AI生成外貌按钮
        generate_appearance_btn = ModernButton("AI生成外貌描述")
        generate_appearance_btn.clicked.connect(self.generate_character_appearance)
        appearance_layout.addWidget(generate_appearance_btn)
        
        character_layout.addWidget(appearance_group)
        
        # 性格特征
        personality_group = QGroupBox("性格特征")
        personality_layout = QVBoxLayout(personality_group)
        
        self.char_personality_edit = QTextEdit()
        self.char_personality_edit.setMaximumHeight(100)
        self.char_personality_edit.textChanged.connect(self.on_character_data_changed)
        personality_layout.addWidget(self.char_personality_edit)
        
        # AI生成性格按钮
        generate_personality_btn = ModernButton("AI生成性格特征")
        generate_personality_btn.clicked.connect(self.generate_character_personality)
        personality_layout.addWidget(generate_personality_btn)
        
        character_layout.addWidget(personality_group)
        
        # 背景故事
        background_group = QGroupBox("背景故事")
        background_layout = QVBoxLayout(background_group)
        
        self.char_background_edit = QTextEdit()
        self.char_background_edit.textChanged.connect(self.on_character_data_changed)
        background_layout.addWidget(self.char_background_edit)
        
        # AI生成背景按钮
        generate_background_btn = ModernButton("AI生成背景故事")
        generate_background_btn.clicked.connect(self.generate_character_background)
        background_layout.addWidget(generate_background_btn)
        
        character_layout.addWidget(background_group)
        
        # 角色关系
        relations_group = QGroupBox("角色关系")
        relations_layout = QVBoxLayout(relations_group)
        
        self.char_relations_table = QTableWidget()
        self.char_relations_table.setColumnCount(3)
        self.char_relations_table.setHorizontalHeaderLabels(["关系对象", "关系类型", "关系描述"])
        self.char_relations_table.horizontalHeader().setStretchLastSection(True)
        relations_layout.addWidget(self.char_relations_table)
        
        # 添加关系按钮
        add_relation_btn = ModernButton("添加关系")
        add_relation_btn.clicked.connect(self.add_character_relation)
        relations_layout.addWidget(add_relation_btn)
        
        character_layout.addWidget(relations_group)
        
        character_layout.addStretch()
        self.character_tab.setWidget(character_widget)
        self.content_tabs.addTab(self.character_tab, "角色档案")
    
    def setup_world_tab(self):
        """设置世界观标签页"""
        self.world_tab = QWidget()
        world_layout = QHBoxLayout(self.world_tab)
        world_layout.setSpacing(8)
        
        # 左侧分类列表
        self.world_categories = QListWidget()
        self.world_categories.setMaximumWidth(200)
        self.world_categories.itemClicked.connect(self.on_world_category_selected)
        
        categories = [
            "设定总览", "历史背景", "地理环境", "社会制度",
            "文化习俗", "科技水平", "魔法体系", "种族设定"
        ]
        
        for category in categories:
            item = QListWidgetItem(category)
            item.setData(Qt.ItemDataRole.UserRole, category)
            self.world_categories.addItem(item)
        
        world_layout.addWidget(self.world_categories)
        
        # 右侧内容编辑区
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        # 标题
        self.world_title_label = QLabel("选择一个分类开始编辑")
        self.world_title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")
        content_layout.addWidget(self.world_title_label)
        
        # 内容编辑器
        self.world_content = QTextEdit()
        self.world_content.textChanged.connect(self.on_world_content_changed)
        content_layout.addWidget(self.world_content)
        
        # AI辅助按钮
        ai_buttons = QHBoxLayout()
        
        generate_world_btn = ModernButton("AI生成内容")
        generate_world_btn.clicked.connect(self.generate_world_content)
        ai_buttons.addWidget(generate_world_btn)
        
        expand_world_btn = ModernButton("AI扩展内容")
        expand_world_btn.clicked.connect(self.expand_world_content)
        ai_buttons.addWidget(expand_world_btn)
        
        ai_buttons.addStretch()
        content_layout.addLayout(ai_buttons)
        
        world_layout.addWidget(content_widget)
        
        self.content_tabs.addTab(self.world_tab, "世界观")
    
    def setup_timeline_tab(self):
        """设置时间线标签页"""
        self.timeline_tab = QWidget()
        timeline_layout = QVBoxLayout(self.timeline_tab)
        timeline_layout.setSpacing(8)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        add_event_btn = ModernButton("添加事件")
        add_event_btn.clicked.connect(self.add_timeline_event)
        toolbar_layout.addWidget(add_event_btn)
        
        delete_event_btn = ModernButton("删除事件")
        delete_event_btn.clicked.connect(self.delete_timeline_event)
        toolbar_layout.addWidget(delete_event_btn)
        
        toolbar_layout.addStretch()
        
        sort_by_date_btn = ModernButton("按日期排序")
        sort_by_date_btn.clicked.connect(self.sort_timeline_by_date)
        toolbar_layout.addWidget(sort_by_date_btn)
        
        timeline_layout.addLayout(toolbar_layout)
        
        # 时间线表格
        self.timeline_table = QTableWidget()
        self.timeline_table.setColumnCount(4)
        self.timeline_table.setHorizontalHeaderLabels(["日期", "事件标题", "重要性", "相关角色"])
        self.timeline_table.horizontalHeader().setStretchLastSection(True)
        self.timeline_table.itemSelectionChanged.connect(self.on_timeline_event_selected)
        timeline_layout.addWidget(self.timeline_table)
        
        # 事件详情
        details_group = QGroupBox("事件详情")
        details_layout = QVBoxLayout(details_group)
        
        self.timeline_details = QTextEdit()
        self.timeline_details.setMaximumHeight(150)
        self.timeline_details.textChanged.connect(self.on_timeline_details_changed)
        details_layout.addWidget(self.timeline_details)
        
        timeline_layout.addWidget(details_group)
        
        self.content_tabs.addTab(self.timeline_tab, "时间线")
    
    def setup_location_tab(self):
        """设置地点标签页"""
        self.location_tab = QWidget()
        location_layout = QHBoxLayout(self.location_tab)
        location_layout.setSpacing(8)
        
        # 左侧地点树
        self.location_tree = QTreeWidget()
        self.location_tree.setHeaderLabel("地点")
        self.location_tree.setMaximumWidth(300)
        self.location_tree.itemClicked.connect(self.on_location_selected)
        location_layout.addWidget(self.location_tree)
        
        # 右侧地点详情
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        
        # 地点信息表单
        location_form = QGroupBox("地点信息")
        form_layout = QFormLayout(location_form)
        
        self.location_name_edit = QLineEdit()
        self.location_name_edit.textChanged.connect(self.on_location_data_changed)
        form_layout.addRow("地点名称:", self.location_name_edit)
        
        self.location_type_combo = QComboBox()
        self.location_type_combo.addItems(["城市", "村庄", "建筑", "自然景观", "其他"])
        self.location_type_combo.currentTextChanged.connect(self.on_location_data_changed)
        form_layout.addRow("地点类型:", self.location_type_combo)
        
        self.location_parent_combo = QComboBox()
        self.location_parent_combo.currentTextChanged.connect(self.on_location_data_changed)
        form_layout.addRow("所属地区:", self.location_parent_combo)
        
        details_layout.addWidget(location_form)
        
        # 地点描述
        description_group = QGroupBox("地点描述")
        description_layout = QVBoxLayout(description_group)
        
        self.location_description = QTextEdit()
        self.location_description.textChanged.connect(self.on_location_data_changed)
        description_layout.addWidget(self.location_description)
        
        # AI生成描述按钮
        generate_location_btn = ModernButton("AI生成地点描述")
        generate_location_btn.clicked.connect(self.generate_location_description)
        description_layout.addWidget(generate_location_btn)
        
        details_layout.addWidget(description_group)
        
        # 相关事件
        events_group = QGroupBox("相关事件")
        events_layout = QVBoxLayout(events_group)
        
        self.location_events_list = QListWidget()
        events_layout.addWidget(self.location_events_list)
        
        details_layout.addWidget(events_group)
        
        location_layout.addWidget(details_widget)
        
        self.content_tabs.addTab(self.location_tab, "地点")
    
    def setup_relationship_tab(self):
        """设置关系图谱标签页"""
        self.relationship_tab = QWidget()
        relationship_layout = QVBoxLayout(self.relationship_tab)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        refresh_btn = ModernButton("刷新图谱")
        refresh_btn.clicked.connect(self.refresh_relationship_view)
        toolbar_layout.addWidget(refresh_btn)
        
        export_btn = ModernButton("导出图谱")
        export_btn.clicked.connect(self.export_relationship_diagram)
        toolbar_layout.addWidget(export_btn)
        
        toolbar_layout.addStretch()
        
        relationship_layout.addLayout(toolbar_layout)
        
        # 关系图谱视图（占位符）
        self.relationship_view = QLabel("关系图谱视图\n（功能开发中）")
        self.relationship_view.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.relationship_view.setStyleSheet("""
            QLabel {
                border: 2px dashed #ccc;
                border-radius: 8px;
                font-size: 16px;
                color: #666;
                background-color: #f9f9f9;
            }
        """)
        relationship_layout.addWidget(self.relationship_view)
        
        self.content_tabs.addTab(self.relationship_tab, "关系图谱")
    
    # 数据加载和保存
    @handle_exceptions()
    def load_project_data(self):
        """加载项目数据"""
        if not self.project_manager.current_project:
            return
        
        self.current_project = self.project_manager.current_project
        
        # 加载故事圣经数据
        story_bible_file = self.current_project.project_path / "story_bible.json"
        if story_bible_file.exists():
            try:
                with open(story_bible_file, 'r', encoding='utf-8') as f:
                    self.story_bible_data = json.load(f)
            except Exception as e:
                self.show_message("错误", f"加载故事圣经数据失败: {e}", "error")
                self.story_bible_data = {}
        else:
            self.story_bible_data = self.create_default_story_bible_data()
        
        # 更新UI
        self.update_category_tree()
        self.update_location_tree()
        self.update_timeline_table()
    
    def create_default_story_bible_data(self) -> Dict[str, Any]:
        """创建默认故事圣经数据"""
        return {
            "characters": {},
            "world": {
                "设定总览": "",
                "历史背景": "",
                "地理环境": "",
                "社会制度": "",
                "文化习俗": "",
                "科技水平": "",
                "魔法体系": "",
                "种族设定": ""
            },
            "timeline": [],
            "locations": {},
            "relationships": {},
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "version": "1.0"
            }
        }
    
    @handle_exceptions()
    def save_story_bible_data(self):
        """保存故事圣经数据"""
        if not self.current_project:
            return
        
        # 更新元数据
        self.story_bible_data["metadata"]["updated_at"] = datetime.now().isoformat()
        
        # 保存到文件
        story_bible_file = self.current_project.project_path / "story_bible.json"
        try:
            with open(story_bible_file, 'w', encoding='utf-8') as f:
                json.dump(self.story_bible_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.show_message("错误", f"保存故事圣经数据失败: {e}", "error")
    
    # 事件处理
    def on_category_selected(self, item: QTreeWidgetItem, column: int):
        """分类选择处理"""
        data = item.data(0, Qt.ItemDataRole.UserRole)
        if not data:
            return
        
        self.current_item = item
        category = data.get("category")
        
        # 切换到对应的标签页
        if category == "characters":
            self.content_tabs.setCurrentWidget(self.character_tab)
        elif category == "world":
            self.content_tabs.setCurrentWidget(self.world_tab)
            subcategory = data.get("subcategory")
            if subcategory:
                self.load_world_content(subcategory)
        elif category == "timeline":
            self.content_tabs.setCurrentWidget(self.timeline_tab)
        elif category == "locations":
            self.content_tabs.setCurrentWidget(self.location_tab)
        elif category == "relationships":
            self.content_tabs.setCurrentWidget(self.relationship_tab)
    
    def on_category_double_clicked(self, item: QTreeWidgetItem, column: int):
        """分类双击处理"""
        # TODO: 实现双击编辑功能
        pass
    
    def on_search_changed(self, text: str):
        """搜索文本变化处理"""
        # TODO: 实现实时搜索
        pass
    
    def perform_search(self):
        """执行搜索"""
        search_text = self.search_edit.text().strip()
        if not search_text:
            return
        
        # TODO: 实现搜索功能
        self.show_message("提示", f"搜索功能正在开发中: {search_text}")
    
    # 角色管理
    def on_character_data_changed(self):
        """角色数据变化处理"""
        # TODO: 实现角色数据保存
        pass
    
    def add_character_relation(self):
        """添加角色关系"""
        # TODO: 实现添加角色关系对话框
        self.show_message("提示", "添加角色关系功能正在开发中")
    
    @handle_exceptions()
    def generate_character_appearance(self):
        """AI生成角色外貌"""
        character_name = self.char_name_edit.text()
        if not character_name:
            self.show_message("提示", "请先输入角色姓名")
            return
        
        # TODO: 调用AI服务生成外貌描述
        self.ai_generation_requested.emit("character_appearance", {"name": character_name})
    
    @handle_exceptions()
    def generate_character_personality(self):
        """AI生成角色性格"""
        character_name = self.char_name_edit.text()
        if not character_name:
            self.show_message("提示", "请先输入角色姓名")
            return
        
        # TODO: 调用AI服务生成性格特征
        self.ai_generation_requested.emit("character_personality", {"name": character_name})
    
    @handle_exceptions()
    def generate_character_background(self):
        """AI生成角色背景"""
        character_name = self.char_name_edit.text()
        if not character_name:
            self.show_message("提示", "请先输入角色姓名")
            return
        
        # TODO: 调用AI服务生成背景故事
        self.ai_generation_requested.emit("character_background", {"name": character_name})
    
    # 世界观管理
    def on_world_category_selected(self, item: QListWidgetItem):
        """世界观分类选择处理"""
        category = item.data(Qt.ItemDataRole.UserRole)
        self.load_world_content(category)
    
    def load_world_content(self, category: str):
        """加载世界观内容"""
        self.world_title_label.setText(category)
        content = self.story_bible_data.get("world", {}).get(category, "")
        self.world_content.setPlainText(content)
    
    def on_world_content_changed(self):
        """世界观内容变化处理"""
        # TODO: 实现世界观内容保存
        pass
    
    @handle_exceptions()
    def generate_world_content(self):
        """AI生成世界观内容"""
        current_item = self.world_categories.currentItem()
        if not current_item:
            self.show_message("提示", "请先选择一个世界观分类")
            return
        
        category = current_item.data(Qt.ItemDataRole.UserRole)
        # TODO: 调用AI服务生成世界观内容
        self.ai_generation_requested.emit("world_content", {"category": category})
    
    @handle_exceptions()
    def expand_world_content(self):
        """AI扩展世界观内容"""
        current_item = self.world_categories.currentItem()
        if not current_item:
            self.show_message("提示", "请先选择一个世界观分类")
            return
        
        category = current_item.data(Qt.ItemDataRole.UserRole)
        current_content = self.world_content.toPlainText()
        
        # TODO: 调用AI服务扩展世界观内容
        self.ai_generation_requested.emit("expand_world_content", {
            "category": category,
            "current_content": current_content
        })
    
    # 时间线管理
    def add_timeline_event(self):
        """添加时间线事件"""
        # TODO: 实现添加时间线事件对话框
        self.show_message("提示", "添加时间线事件功能正在开发中")
    
    def delete_timeline_event(self):
        """删除时间线事件"""
        # TODO: 实现删除时间线事件
        self.show_message("提示", "删除时间线事件功能正在开发中")
    
    def sort_timeline_by_date(self):
        """按日期排序时间线"""
        # TODO: 实现时间线排序
        self.show_message("提示", "时间线排序功能正在开发中")
    
    def on_timeline_event_selected(self):
        """时间线事件选择处理"""
        # TODO: 实现事件详情显示
        pass
    
    def on_timeline_details_changed(self):
        """时间线详情变化处理"""
        # TODO: 实现事件详情保存
        pass
    
    def update_timeline_table(self):
        """更新时间线表格"""
        # TODO: 实现时间线表格更新
        pass
    
    # 地点管理
    def on_location_selected(self, item: QTreeWidgetItem, column: int):
        """地点选择处理"""
        # TODO: 实现地点详情显示
        pass
    
    def on_location_data_changed(self):
        """地点数据变化处理"""
        # TODO: 实现地点数据保存
        pass
    
    @handle_exceptions()
    def generate_location_description(self):
        """AI生成地点描述"""
        location_name = self.location_name_edit.text()
        if not location_name:
            self.show_message("提示", "请先输入地点名称")
            return
        
        # TODO: 调用AI服务生成地点描述
        self.ai_generation_requested.emit("location_description", {"name": location_name})
    
    def update_location_tree(self):
        """更新地点树"""
        # TODO: 实现地点树更新
        pass
    
    # 关系图谱管理
    def refresh_relationship_view(self):
        """刷新关系图谱视图"""
        # TODO: 实现关系图谱刷新
        self.show_message("提示", "关系图谱刷新功能正在开发中")
    
    def export_relationship_diagram(self):
        """导出关系图谱"""
        # TODO: 实现关系图谱导出
        self.show_message("提示", "关系图谱导出功能正在开发中")
    
    # 通用操作
    def add_item(self):
        """添加项目"""
        if not self.current_item:
            self.show_message("提示", "请先选择一个分类")
            return
        
        # TODO: 根据当前分类添加对应项目
        self.show_message("提示", "添加项目功能正在开发中")
    
    def delete_item(self):
        """删除项目"""
        if not self.current_item:
            self.show_message("提示", "请先选择一个项目")
            return
        
        # TODO: 删除当前选中项目
        self.show_message("提示", "删除项目功能正在开发中")
    
    def update_category_tree(self):
        """更新分类树"""
        # TODO: 根据数据更新分类树
        pass
    
    # 重写基类方法
    def on_save(self):
        """保存操作"""
        self.save_story_bible_data()
    
    def cleanup(self):
        """清理资源"""
        # 保存数据
        self.save_story_bible_data()