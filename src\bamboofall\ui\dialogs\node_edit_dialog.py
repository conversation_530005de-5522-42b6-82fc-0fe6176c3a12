# -*- coding: utf-8 -*-
"""
节点编辑对话框

提供详细的大纲节点编辑功能
"""

import logging
from typing import List, Optional, Dict, Any
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QTabWidget,
    QLineEdit, QTextEdit, QComboBox, QSpinBox, QCheckBox, QLabel,
    QPushButton, QDialogButtonBox, QGroupBox, QListWidget, QListWidgetItem,
    QSplitter, QTreeWidget, QTreeWidgetItem, QMessageBox, QProgressBar,
    QDateTimeEdit, QSlider, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QDateTime
from PyQt6.QtGui import QFont, QIcon, QColor

from ...models.outline import OutlineNode, OutlineNodeType, OutlineNodeStatus
from ...core.outline_manager import get_outline_manager
from ...exceptions.exceptions import ValidationError

logger = logging.getLogger(__name__)


class NodeEditDialog(QDialog):
    """节点编辑对话框"""
    
    node_updated = pyqtSignal(str)  # node_id
    
    def __init__(self, node: OutlineNode, parent=None):
        super().__init__(parent)
        self.node = node
        self.outline_manager = get_outline_manager()
        self.setup_ui()
        self.load_node_data()
        self.setup_connections()
        
    def setup_ui(self):
        """设置UI"""
        self.setWindowTitle("编辑节点")
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 基本信息标签页
        self.setup_basic_tab()
        
        # 内容标签页
        self.setup_content_tab()
        
        # 关联标签页
        self.setup_relations_tab()
        
        # 统计标签页
        self.setup_statistics_tab()
        
        # 元数据标签页
        self.setup_metadata_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel |
            QDialogButtonBox.StandardButton.Apply
        )
        
        self.ok_btn = button_box.button(QDialogButtonBox.StandardButton.Ok)
        self.cancel_btn = button_box.button(QDialogButtonBox.StandardButton.Cancel)
        self.apply_btn = button_box.button(QDialogButtonBox.StandardButton.Apply)
        
        layout.addWidget(button_box)
        
        # 连接按钮信号
        button_box.accepted.connect(self.accept_changes)
        button_box.rejected.connect(self.reject)
        self.apply_btn.clicked.connect(self.apply_changes)
        
    def setup_basic_tab(self):
        """设置基本信息标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)
        
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("节点标题")
        basic_layout.addRow("标题*:", self.title_edit)
        
        self.type_combo = QComboBox()
        for node_type in OutlineNodeType:
            self.type_combo.addItem(node_type.value, node_type)
        basic_layout.addRow("类型:", self.type_combo)
        
        self.status_combo = QComboBox()
        for status in OutlineNodeStatus:
            self.status_combo.addItem(status.value, status)
        basic_layout.addRow("状态:", self.status_combo)
        
        # 层级信息
        self.level_spin = QSpinBox()
        self.level_spin.setRange(0, 10)
        self.level_spin.setReadOnly(True)
        basic_layout.addRow("层级:", self.level_spin)
        
        self.order_spin = QSpinBox()
        self.order_spin.setRange(0, 9999)
        basic_layout.addRow("排序:", self.order_spin)
        
        layout.addWidget(basic_group)
        
        # 时间信息组
        time_group = QGroupBox("时间信息")
        time_layout = QFormLayout(time_group)
        
        self.created_label = QLabel()
        time_layout.addRow("创建时间:", self.created_label)
        
        self.updated_label = QLabel()
        time_layout.addRow("更新时间:", self.updated_label)
        
        layout.addWidget(time_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "基本信息")
        
    def setup_content_tab(self):
        """设置内容标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 内容编辑
        content_group = QGroupBox("内容描述")
        content_layout = QVBoxLayout(content_group)
        
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("详细描述节点内容...")
        content_layout.addWidget(self.content_edit)
        
        # 字数统计
        stats_layout = QHBoxLayout()
        self.content_stats_label = QLabel("字数: 0")
        stats_layout.addWidget(self.content_stats_label)
        stats_layout.addStretch()
        content_layout.addLayout(stats_layout)
        
        layout.addWidget(content_group)
        
        # 备注
        notes_group = QGroupBox("备注")
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("备注信息...")
        self.notes_edit.setMaximumHeight(150)
        notes_layout.addWidget(self.notes_edit)
        
        layout.addWidget(notes_group)
        
        self.tab_widget.addTab(tab, "内容")
        
    def setup_relations_tab(self):
        """设置关联标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 章节关联
        chapter_group = QGroupBox("章节关联")
        chapter_layout = QFormLayout(chapter_group)
        
        self.chapter_edit = QLineEdit()
        self.chapter_edit.setPlaceholderText("关联的章节ID")
        chapter_layout.addRow("关联章节:", self.chapter_edit)
        
        layout.addWidget(chapter_group)
        
        # 场景关联
        scene_group = QGroupBox("场景关联")
        scene_layout = QVBoxLayout(scene_group)
        
        self.scene_list = QListWidget()
        self.scene_list.setMaximumHeight(100)
        scene_layout.addWidget(self.scene_list)
        
        scene_btn_layout = QHBoxLayout()
        self.add_scene_btn = QPushButton("添加场景")
        self.remove_scene_btn = QPushButton("移除场景")
        scene_btn_layout.addWidget(self.add_scene_btn)
        scene_btn_layout.addWidget(self.remove_scene_btn)
        scene_btn_layout.addStretch()
        scene_layout.addLayout(scene_btn_layout)
        
        layout.addWidget(scene_group)
        
        # 角色关联
        character_group = QGroupBox("角色关联")
        character_layout = QVBoxLayout(character_group)
        
        self.character_list = QListWidget()
        self.character_list.setMaximumHeight(100)
        character_layout.addWidget(self.character_list)
        
        char_btn_layout = QHBoxLayout()
        self.add_character_btn = QPushButton("添加角色")
        self.remove_character_btn = QPushButton("移除角色")
        char_btn_layout.addWidget(self.add_character_btn)
        char_btn_layout.addWidget(self.remove_character_btn)
        char_btn_layout.addStretch()
        character_layout.addLayout(char_btn_layout)
        
        layout.addWidget(character_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "关联")
        
    def setup_statistics_tab(self):
        """设置统计标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 字数统计
        word_group = QGroupBox("字数统计")
        word_layout = QFormLayout(word_group)
        
        self.estimated_words_spin = QSpinBox()
        self.estimated_words_spin.setRange(0, 999999)
        self.estimated_words_spin.setSuffix(" 字")
        word_layout.addRow("预计字数:", self.estimated_words_spin)
        
        self.actual_words_spin = QSpinBox()
        self.actual_words_spin.setRange(0, 999999)
        self.actual_words_spin.setSuffix(" 字")
        word_layout.addRow("实际字数:", self.actual_words_spin)
        
        # 进度条
        self.progress_bar = QProgressBar()
        word_layout.addRow("完成进度:", self.progress_bar)
        
        # 进度滑块
        progress_layout = QHBoxLayout()
        self.progress_slider = QSlider(Qt.Orientation.Horizontal)
        self.progress_slider.setRange(0, 100)
        self.progress_label = QLabel("0%")
        progress_layout.addWidget(self.progress_slider)
        progress_layout.addWidget(self.progress_label)
        word_layout.addRow("手动设置进度:", progress_layout)
        
        layout.addWidget(word_group)
        
        # 统计信息显示
        stats_group = QGroupBox("统计信息")
        stats_layout = QFormLayout(stats_group)
        
        self.child_count_label = QLabel("0")
        stats_layout.addRow("子节点数量:", self.child_count_label)
        
        self.total_words_label = QLabel("0")
        stats_layout.addRow("总字数(含子节点):", self.total_words_label)
        
        layout.addWidget(stats_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "统计")
        
    def setup_metadata_tab(self):
        """设置元数据标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 标签
        tags_group = QGroupBox("标签")
        tags_layout = QVBoxLayout(tags_group)
        
        self.tags_edit = QLineEdit()
        self.tags_edit.setPlaceholderText("标签，用逗号分隔")
        tags_layout.addWidget(self.tags_edit)
        
        layout.addWidget(tags_group)
        
        # 自定义属性
        metadata_group = QGroupBox("自定义属性")
        metadata_layout = QVBoxLayout(metadata_group)
        
        self.metadata_edit = QTextEdit()
        self.metadata_edit.setPlaceholderText("JSON格式的自定义属性")
        self.metadata_edit.setMaximumHeight(200)
        metadata_layout.addWidget(self.metadata_edit)
        
        layout.addWidget(metadata_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "元数据")
        
    def setup_connections(self):
        """设置信号连接"""
        # 内容变化时更新字数统计
        self.content_edit.textChanged.connect(self.update_content_stats)
        
        # 进度滑块
        self.progress_slider.valueChanged.connect(self.update_progress_label)
        self.progress_slider.valueChanged.connect(self.update_progress_bar)
        
        # 字数变化时更新进度
        self.actual_words_spin.valueChanged.connect(self.update_progress_from_words)
        self.estimated_words_spin.valueChanged.connect(self.update_progress_from_words)
        
        # 场景和角色按钮
        self.add_scene_btn.clicked.connect(self.add_scene)
        self.remove_scene_btn.clicked.connect(self.remove_scene)
        self.add_character_btn.clicked.connect(self.add_character)
        self.remove_character_btn.clicked.connect(self.remove_character)
        
    def load_node_data(self):
        """加载节点数据"""
        # 基本信息
        self.title_edit.setText(self.node.title or "")
        
        # 设置类型
        type_index = self.type_combo.findData(self.node.type)
        if type_index >= 0:
            self.type_combo.setCurrentIndex(type_index)
            
        # 设置状态
        status_index = self.status_combo.findData(self.node.status)
        if status_index >= 0:
            self.status_combo.setCurrentIndex(status_index)
            
        self.level_spin.setValue(self.node.level or 0)
        self.order_spin.setValue(self.node.order_index or 0)
        
        # 时间信息
        if self.node.created_at:
            self.created_label.setText(self.node.created_at.strftime("%Y-%m-%d %H:%M:%S"))
        if self.node.updated_at:
            self.updated_label.setText(self.node.updated_at.strftime("%Y-%m-%d %H:%M:%S"))
            
        # 内容
        self.content_edit.setPlainText(self.node.content or "")
        self.notes_edit.setPlainText(self.node.notes or "")
        
        # 关联
        self.chapter_edit.setText(self.node.chapter_id or "")
        
        # 场景列表
        if self.node.scene_ids:
            for scene_id in self.node.scene_ids:
                self.scene_list.addItem(scene_id)
                
        # 角色列表
        if self.node.character_ids:
            for char_id in self.node.character_ids:
                self.character_list.addItem(char_id)
                
        # 统计
        self.estimated_words_spin.setValue(self.node.estimated_word_count or 0)
        self.actual_words_spin.setValue(self.node.actual_word_count or 0)
        
        # 进度
        progress = self.node.get_progress()
        self.progress_slider.setValue(int(progress * 100))
        self.progress_bar.setValue(int(progress * 100))
        
        # 统计信息
        self.child_count_label.setText(str(len(self.node.children_ids)))
        
        # 标签
        if self.node.tags:
            self.tags_edit.setText(",".join(self.node.tags))
            
        # 元数据
        if self.node.metadata:
            import json
            self.metadata_edit.setPlainText(json.dumps(self.node.metadata, indent=2, ensure_ascii=False))
            
        # 更新内容统计
        self.update_content_stats()
        
    def update_content_stats(self):
        """更新内容统计"""
        content = self.content_edit.toPlainText()
        word_count = len(content.replace(" ", "").replace("\n", ""))
        self.content_stats_label.setText(f"字数: {word_count}")
        
    def update_progress_label(self, value):
        """更新进度标签"""
        self.progress_label.setText(f"{value}%")
        
    def update_progress_bar(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
        
    def update_progress_from_words(self):
        """根据字数更新进度"""
        estimated = self.estimated_words_spin.value()
        actual = self.actual_words_spin.value()
        
        if estimated > 0:
            progress = min(100, int((actual / estimated) * 100))
            self.progress_slider.setValue(progress)
            
    def add_scene(self):
        """添加场景"""
        from PyQt6.QtWidgets import QInputDialog
        
        scene_id, ok = QInputDialog.getText(self, "添加场景", "场景ID:")
        if ok and scene_id.strip():
            self.scene_list.addItem(scene_id.strip())
            
    def remove_scene(self):
        """移除场景"""
        current_item = self.scene_list.currentItem()
        if current_item:
            row = self.scene_list.row(current_item)
            self.scene_list.takeItem(row)
            
    def add_character(self):
        """添加角色"""
        from PyQt6.QtWidgets import QInputDialog
        
        char_id, ok = QInputDialog.getText(self, "添加角色", "角色ID:")
        if ok and char_id.strip():
            self.character_list.addItem(char_id.strip())
            
    def remove_character(self):
        """移除角色"""
        current_item = self.character_list.currentItem()
        if current_item:
            row = self.character_list.row(current_item)
            self.character_list.takeItem(row)
            
    def validate_data(self) -> bool:
        """验证数据"""
        if not self.title_edit.text().strip():
            QMessageBox.warning(self, "验证错误", "标题不能为空")
            self.tab_widget.setCurrentIndex(0)
            self.title_edit.setFocus()
            return False
            
        # 验证元数据JSON格式
        metadata_text = self.metadata_edit.toPlainText().strip()
        if metadata_text:
            try:
                import json
                json.loads(metadata_text)
            except json.JSONDecodeError as e:
                QMessageBox.warning(self, "验证错误", f"元数据JSON格式错误: {e}")
                self.tab_widget.setCurrentIndex(4)
                self.metadata_edit.setFocus()
                return False
                
        return True
        
    def save_node_data(self):
        """保存节点数据"""
        if not self.validate_data():
            return False
            
        try:
            # 更新基本信息
            self.node.title = self.title_edit.text().strip()
            self.node.type = self.type_combo.currentData()
            self.node.status = self.status_combo.currentData()
            self.node.order_index = self.order_spin.value()
            
            # 更新内容
            self.node.content = self.content_edit.toPlainText().strip()
            self.node.notes = self.notes_edit.toPlainText().strip()
            
            # 更新关联
            self.node.chapter_id = self.chapter_edit.text().strip() or None
            
            # 更新场景列表
            scene_ids = []
            for i in range(self.scene_list.count()):
                scene_ids.append(self.scene_list.item(i).text())
            self.node.scene_ids = scene_ids
            
            # 更新角色列表
            character_ids = []
            for i in range(self.character_list.count()):
                character_ids.append(self.character_list.item(i).text())
            self.node.character_ids = character_ids
            
            # 更新统计
            self.node.estimated_word_count = self.estimated_words_spin.value()
            self.node.actual_word_count = self.actual_words_spin.value()
            
            # 更新标签
            tags_text = self.tags_edit.text().strip()
            if tags_text:
                self.node.tags = [tag.strip() for tag in tags_text.split(",") if tag.strip()]
            else:
                self.node.tags = []
                
            # 更新元数据
            metadata_text = self.metadata_edit.toPlainText().strip()
            if metadata_text:
                import json
                self.node.metadata = json.loads(metadata_text)
            else:
                self.node.metadata = {}
                
            # 更新时间戳
            self.node.update_timestamp()
            
            # 保存到数据库
            self.outline_manager.update_node(self.node)
            
            # 发送信号
            self.node_updated.emit(self.node.id)
            
            return True
            
        except Exception as e:
            logger.error(f"保存节点失败: {e}")
            QMessageBox.warning(self, "错误", f"保存失败: {e}")
            return False
            
    def apply_changes(self):
        """应用更改"""
        if self.save_node_data():
            QMessageBox.information(self, "成功", "节点已保存")
            
    def accept_changes(self):
        """接受更改并关闭"""
        if self.save_node_data():
            self.accept()