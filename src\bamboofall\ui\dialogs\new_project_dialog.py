"""
新建项目对话框

提供美观的项目创建界面
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit,
    QTextEdit, QComboBox, QPushButton, QLabel, QGroupBox,
    QFileDialog, QMessageBox, QProgressBar, QCheckBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QObject
from PyQt6.QtGui import QFont, QPixmap
from typing import Optional, Dict, Any
import logging

from ...models.project import ProjectType
from ...core.project_manager import get_project_manager
from ..widgets.modern_widgets import ModernButton, create_modern_button
from ..themes.theme_manager import get_theme_manager
from ...utils.logger import LoggerMixin

logger = logging.getLogger(__name__)


class ProjectCreationWorker(QObject):
    """项目创建工作线程"""
    
    finished = pyqtSignal(object)  # 完成信号，传递项目对象或异常
    progress = pyqtSignal(int)     # 进度信号
    
    def __init__(self, project_data: Dict[str, Any]):
        super().__init__()
        self.project_data = project_data
        self.project_manager = get_project_manager()
    
    def run(self):
        """执行项目创建"""
        try:
            # 模拟创建进度
            self.progress.emit(20)
            
            # 创建项目
            project = self.project_manager.create_project(
                name=self.project_data['name'],
                genre=self.project_data['type'],
                description=self.project_data['description'],
                author=self.project_data['author']
            )
            
            self.progress.emit(100)
            self.finished.emit(project)
            
        except Exception as e:
            logger.error(f"创建项目失败: {e}")
            self.finished.emit(e)


class NewProjectDialog(QDialog, LoggerMixin):
    """新建项目对话框"""
    
    # 信号定义
    project_created = pyqtSignal(object)  # 项目创建成功信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.project_manager = get_project_manager()
        
        # 工作线程
        self.worker_thread: Optional[QThread] = None
        self.worker: Optional[ProjectCreationWorker] = None
        
        self.setup_ui()
        self.setup_style()
        self.setup_signals()
        
        self.logger.info("新建项目对话框初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("创建新项目")
        self.setModal(True)
        self.setFixedSize(500, 600)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("创建新项目")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 基本信息组
        basic_group = self.create_basic_info_group()
        main_layout.addWidget(basic_group)
        
        # 项目设置组
        settings_group = self.create_settings_group()
        main_layout.addWidget(settings_group)
        
        # 进度条（初始隐藏）
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_button = create_modern_button("取消", "secondary")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        self.create_button = create_modern_button("创建项目", "primary")
        self.create_button.clicked.connect(self.create_project)
        button_layout.addWidget(self.create_button)
        
        main_layout.addLayout(button_layout)
    
    def create_basic_info_group(self) -> QGroupBox:
        """创建基本信息组"""
        group = QGroupBox("基本信息")
        layout = QFormLayout(group)
        layout.setSpacing(15)
        
        # 项目名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入项目名称")
        self.name_edit.textChanged.connect(self.validate_form)
        layout.addRow("项目名称 *:", self.name_edit)
        
        # 作者名称
        self.author_edit = QLineEdit()
        self.author_edit.setPlaceholderText("请输入作者名称")
        # 从配置中加载默认作者名称
        default_author = self.project_manager.config_manager.config.ui.__dict__.get('default_author', '')
        if default_author:
            self.author_edit.setText(default_author)
        layout.addRow("作者:", self.author_edit)
        
        # 项目类型
        self.type_combo = QComboBox()
        type_options = [
            ("fantasy", "🗡️ 玄幻小说"),
            ("urban", "🏙️ 都市小说"),
            ("scifi", "🚀 科幻小说"),
            ("romance", "💕 言情小说"),
            ("mystery", "🔍 悬疑小说"),
            ("historical", "📜 历史小说"),
            ("custom", "✨ 自定义")
        ]
        
        for value, display in type_options:
            self.type_combo.addItem(display, value)
        
        layout.addRow("项目类型:", self.type_combo)
        
        # 项目描述
        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText("请输入项目描述（可选）")
        self.description_edit.setMaximumHeight(100)
        layout.addRow("项目描述:", self.description_edit)
        
        return group
    
    def create_settings_group(self) -> QGroupBox:
        """创建项目设置组"""
        group = QGroupBox("项目设置")
        layout = QFormLayout(group)
        layout.setSpacing(15)
        
        # 项目路径
        path_layout = QHBoxLayout()
        
        self.path_edit = QLineEdit()
        self.path_edit.setPlaceholderText("将自动生成项目路径")
        self.path_edit.setReadOnly(True)
        path_layout.addWidget(self.path_edit)
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_project_path)
        path_layout.addWidget(self.browse_button)
        
        layout.addRow("保存位置:", path_layout)
        
        # 自动生成路径的复选框
        self.auto_path_checkbox = QCheckBox("自动生成项目路径")
        self.auto_path_checkbox.setChecked(True)
        self.auto_path_checkbox.toggled.connect(self.on_auto_path_toggled)
        layout.addRow("", self.auto_path_checkbox)
        
        # 创建示例内容
        self.create_samples_checkbox = QCheckBox("创建示例内容")
        self.create_samples_checkbox.setChecked(True)
        self.create_samples_checkbox.setToolTip("创建示例角色、场景和章节，帮助快速上手")
        layout.addRow("", self.create_samples_checkbox)
        
        return group
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {colors['background']};
                color: {colors['text_primary']};
            }}
            
            QGroupBox {{
                font-weight: bold;
                border: 1px solid {colors['border']};
                border-radius: 6px;
                margin-top: 10px;
                padding-top: 10px;
                color: {colors['text_primary']};
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: {colors['background']};
            }}
            
            QLineEdit, QTextEdit, QComboBox {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 4px;
                padding: 8px;
                color: {colors['text_primary']};
            }}
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
                border-color: {colors['primary']};
                border-width: 2px;
            }}
            
            QCheckBox {{
                color: {colors['text_primary']};
            }}
            
            QProgressBar {{
                border: 1px solid {colors['border']};
                border-radius: 4px;
                background-color: {colors['surface']};
                text-align: center;
                color: {colors['text_primary']};
                height: 20px;
            }}
            
            QProgressBar::chunk {{
                background-color: {colors['primary']};
                border-radius: 3px;
            }}
        """)
    
    def setup_signals(self):
        """设置信号连接"""
        # 名称改变时更新路径预览
        self.name_edit.textChanged.connect(self.update_path_preview)
        
        # 主题变化
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def validate_form(self) -> bool:
        """验证表单"""
        is_valid = bool(self.name_edit.text().strip())
        self.create_button.setEnabled(is_valid)
        return is_valid
    
    def update_path_preview(self):
        """更新路径预览"""
        if self.auto_path_checkbox.isChecked():
            project_name = self.name_edit.text().strip()
            if project_name:
                try:
                    preview_path = self.project_manager._get_default_project_path(project_name)
                    self.path_edit.setText(preview_path)
                except Exception:
                    self.path_edit.setText("将自动生成项目路径")
            else:
                self.path_edit.setText("将自动生成项目路径")
    
    def on_auto_path_toggled(self, checked: bool):
        """自动路径切换处理"""
        if checked:
            self.path_edit.setReadOnly(True)
            self.browse_button.setEnabled(False)
            self.update_path_preview()
        else:
            self.path_edit.setReadOnly(False)
            self.browse_button.setEnabled(True)
            self.path_edit.clear()
            self.path_edit.setPlaceholderText("请选择项目保存位置")
    
    def browse_project_path(self):
        """浏览项目路径"""
        folder = QFileDialog.getExistingDirectory(
            self, 
            "选择项目保存位置",
            str(self.project_manager.config_manager.config_dir)
        )
        
        if folder:
            project_name = self.name_edit.text().strip() or "NewProject"
            full_path = str(Path(folder) / project_name)
            self.path_edit.setText(full_path)
    
    def create_project(self):
        """创建项目"""
        if not self.validate_form():
            return
        
        # 收集项目数据
        project_data = {
            'name': self.name_edit.text().strip(),
            'author': self.author_edit.text().strip(),
            'type': self.type_combo.currentData(),
            'description': self.description_edit.toPlainText().strip(),
            'project_path': self.path_edit.text().strip() if not self.auto_path_checkbox.isChecked() else None
        }
        
        # 禁用界面
        self.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 创建工作线程
        self.worker_thread = QThread()
        self.worker = ProjectCreationWorker(project_data)
        self.worker.moveToThread(self.worker_thread)
        
        # 连接信号
        self.worker_thread.started.connect(self.worker.run)
        self.worker.progress.connect(self.progress_bar.setValue)
        self.worker.finished.connect(self.on_project_created)
        self.worker_thread.finished.connect(self.worker_thread.deleteLater)
        
        # 启动线程
        self.worker_thread.start()
        
        self.logger.info("开始创建项目")
    
    def on_project_created(self, result):
        """项目创建完成处理"""
        # 恢复界面
        self.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        # 清理工作线程
        if self.worker_thread:
            self.worker_thread.quit()
            self.worker_thread.wait()
            self.worker_thread = None
            self.worker = None
        
        if isinstance(result, Exception):
            # 创建失败
            self.logger.error(f"项目创建失败: {result}")
            QMessageBox.critical(
                self, 
                "创建失败", 
                f"项目创建失败:\n{str(result)}"
            )
        else:
            # 创建成功
            self.logger.info(f"项目创建成功: {result.name}")
            QMessageBox.information(
                self, 
                "创建成功", 
                f"项目 '{result.name}' 创建成功！"
            )
            
            # 发送信号
            self.project_created.emit(result)
            
            # 关闭对话框
            self.accept()
    
    def on_theme_changed(self):
        """主题变化处理"""
        self.setup_style()
    
    def closeEvent(self, event):
        """关闭事件"""
        # 如果正在创建项目，询问是否取消
        if self.worker_thread and self.worker_thread.isRunning():
            reply = QMessageBox.question(
                self, 
                "确认取消", 
                "项目正在创建中，确定要取消吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                if self.worker_thread:
                    self.worker_thread.quit()
                    self.worker_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()