"""
提示词管理器

管理AI服务的提示词模板，支持动态参数和上下文管理
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path
import json
import yaml
from jinja2 import Template, Environment, FileSystemLoader

from .ai_service_base import AIMessage, MessageRole
from ..utils.logger import LoggerMixin
from ..utils.config_utils import get_config_manager


@dataclass
class PromptTemplate:
    """提示词模板"""
    id: str
    name: str
    description: str
    template: str
    parameters: List[str] = field(default_factory=list)
    category: str = "general"
    language: str = "zh"
    author: str = ""
    version: str = "1.0"
    tags: List[str] = field(default_factory=list)
    
    def render(self, **kwargs) -> str:
        """渲染模板"""
        template = Template(self.template)
        return template.render(**kwargs)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "template": self.template,
            "parameters": self.parameters,
            "category": self.category,
            "language": self.language,
            "author": self.author,
            "version": self.version,
            "tags": self.tags
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "PromptTemplate":
        """从字典创建"""
        return cls(**data)


class PromptManager(LoggerMixin):
    """提示词管理器"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.templates: Dict[str, PromptTemplate] = {}
        self.categories: Dict[str, List[str]] = {}
        
        # 初始化Jinja2环境
        self.jinja_env = Environment()
        
        # 加载内置提示词模板
        self._load_builtin_templates()
        
        # 加载用户自定义模板
        self._load_user_templates()
        
        self.logger.info(f"提示词管理器初始化完成，已加载{len(self.templates)}个模板")
    
    def _load_builtin_templates(self):
        """加载内置提示词模板"""
        builtin_templates = [
            # 小说创作相关模板
            PromptTemplate(
                id="novel_character_creation",
                name="角色创建助手",
                description="帮助创建小说角色的详细信息",
                template="""请帮我创建一个小说角色，要求如下：

角色基本信息：
- 姓名：{{character_name}}
- 年龄：{{age}}
- 性别：{{gender}}
- 职业：{{occupation}}

请为这个角色设计：
1. 外貌特征（身高、体型、面部特征、着装风格等）
2. 性格特点（至少5个主要性格特征）
3. 背景故事（成长经历、重要事件等）
4. 能力特长（专业技能、天赋等）
5. 人际关系（家庭、朋友、同事等）
6. 目标动机（内在驱动力、人生目标等）
7. 弱点缺陷（性格缺点、恐惧等）

要求角色塑造要立体、有层次感，符合{{genre}}小说的风格特点。""",
                parameters=["character_name", "age", "gender", "occupation", "genre"],
                category="character",
                tags=["创作", "角色", "小说"]
            ),
            
            PromptTemplate(
                id="plot_development",
                name="情节发展助手", 
                description="协助发展小说情节和冲突",
                template="""基于以下信息，请帮我发展小说情节：

当前情况：
{{current_situation}}

主要角色：
{{main_characters}}

故事背景：
{{story_background}}

请设计接下来的情节发展：
1. 即将发生的关键事件（3-5个）
2. 角色面临的主要冲突和挑战
3. 情节转折点的设置建议
4. 角色成长和变化的机会
5. 悬念和张力的营造方法

要求情节要有逻辑性，符合角色性格，推动故事主题发展。""",
                parameters=["current_situation", "main_characters", "story_background"],
                category="plot",
                tags=["创作", "情节", "故事"]
            ),
            
            PromptTemplate(
                id="dialogue_enhancement",
                name="对话优化助手",
                description="优化和改进小说中的对话",
                template="""请帮我优化以下对话，使其更加生动自然：

对话场景：{{scene_context}}

参与角色：
{% for character in characters %}
- {{character.name}}：{{character.personality}}
{% endfor %}

原始对话：
{{original_dialogue}}

请优化对话，注意：
1. 符合每个角色的性格特点和说话风格
2. 增加潜台词和情感层次
3. 适当添加动作描写和心理活动
4. 推动情节发展或揭示角色信息
5. 保持对话的自然流畅性

优化后的对话：""",
                parameters=["scene_context", "characters", "original_dialogue"],
                category="dialogue",
                tags=["创作", "对话", "优化"]
            ),
            
            PromptTemplate(
                id="scene_description",
                name="场景描写助手",
                description="生成生动的场景描写",
                template="""请为以下场景创作生动的描写：

场景设定：{{scene_setting}}
时间：{{time_context}}
天气/氛围：{{atmosphere}}
主要角色：{{characters_present}}
情节目的：{{scene_purpose}}

请创作场景描写，包含：
1. 环境细节（视觉、听觉、嗅觉、触觉等）
2. 氛围营造（情绪基调、紧张感等）
3. 角色在环境中的状态和反应
4. 与情节相关的环境元素
5. 适当的象征意义或暗示

描写要求：
- 文字生动形象，有画面感
- 符合{{genre}}风格
- 长度控制在{{word_count}}字左右""",
                parameters=["scene_setting", "time_context", "atmosphere", "characters_present", "scene_purpose", "genre", "word_count"],
                category="description",
                tags=["创作", "描写", "场景"]
            ),
            
            # 文本处理模板
            PromptTemplate(
                id="text_polish",
                name="文本润色助手",
                description="对文本进行润色和改进",
                template="""请对以下文本进行润色改进：

原文：
{{original_text}}

润色要求：
- 保持原意不变
- 提升语言表达的流畅性和美感
- 增强文字的感染力
- 修正语法和用词问题
- 适合{{target_audience}}阅读

润色后的文本：""",
                parameters=["original_text", "target_audience"],
                category="editing",
                tags=["润色", "编辑", "改进"]
            ),
            
            PromptTemplate(
                id="content_expansion",
                name="内容扩展助手",
                description="扩展和丰富现有内容",
                template="""请基于以下内容进行扩展：

原始内容：
{{base_content}}

扩展方向：
{{expansion_direction}}

扩展要求：
1. 保持与原内容的一致性
2. 增加细节和深度
3. 目标字数：{{target_word_count}}字
4. 保持{{writing_style}}风格

扩展后的内容：""",
                parameters=["base_content", "expansion_direction", "target_word_count", "writing_style"],
                category="expansion",
                tags=["扩展", "丰富", "创作"]
            ),
            
            # 故事圣经模板
            PromptTemplate(
                id="story_bible_summary",
                name="故事圣经总结",
                description="生成故事圣经的总结内容",
                template="""基于以下信息，请生成故事圣经总结：

作品信息：
- 标题：{{title}}
- 类型：{{genre}}
- 目标读者：{{target_audience}}

核心设定：
{{core_settings}}

主要角色：
{{main_characters}}

世界观：
{{world_building}}

请生成包含以下内容的故事圣经总结：
1. 作品概述（一句话概括+详细简介）
2. 核心主题和价值观
3. 故事结构和关键情节点
4. 角色关系图谱
5. 世界观设定要点
6. 写作风格指南
7. 创作注意事项""",
                parameters=["title", "genre", "target_audience", "core_settings", "main_characters", "world_building"],
                category="story_bible",
                tags=["故事圣经", "总结", "设定"]
            )
        ]
        
        for template in builtin_templates:
            self.templates[template.id] = template
            self._add_to_category(template.category, template.id)
    
    def _load_user_templates(self):
        """加载用户自定义模板"""
        try:
            templates_dir = Path(self.config_manager.get_user_data_dir()) / "templates"
            if not templates_dir.exists():
                templates_dir.mkdir(parents=True)
                return
            
            for template_file in templates_dir.glob("*.json"):
                try:
                    with open(template_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        template = PromptTemplate.from_dict(data)
                        self.templates[template.id] = template
                        self._add_to_category(template.category, template.id)
                        
                except Exception as e:
                    self.logger.error(f"加载模板文件失败 {template_file}: {e}")
            
            # 也支持YAML格式
            for template_file in templates_dir.glob("*.yaml"):
                try:
                    with open(template_file, 'r', encoding='utf-8') as f:
                        data = yaml.safe_load(f)
                        template = PromptTemplate.from_dict(data)
                        self.templates[template.id] = template
                        self._add_to_category(template.category, template.id)
                        
                except Exception as e:
                    self.logger.error(f"加载YAML模板文件失败 {template_file}: {e}")
                    
        except Exception as e:
            self.logger.error(f"加载用户模板失败: {e}")
    
    def _add_to_category(self, category: str, template_id: str):
        """添加到分类"""
        if category not in self.categories:
            self.categories[category] = []
        if template_id not in self.categories[category]:
            self.categories[category].append(template_id)
    
    def get_template(self, template_id: str) -> Optional[PromptTemplate]:
        """获取模板"""
        return self.templates.get(template_id)
    
    def get_templates_by_category(self, category: str) -> List[PromptTemplate]:
        """按分类获取模板"""
        template_ids = self.categories.get(category, [])
        return [self.templates[tid] for tid in template_ids if tid in self.templates]
    
    def search_templates(self, query: str) -> List[PromptTemplate]:
        """搜索模板"""
        results = []
        query_lower = query.lower()
        
        for template in self.templates.values():
            if (query_lower in template.name.lower() or 
                query_lower in template.description.lower() or
                any(query_lower in tag.lower() for tag in template.tags)):
                results.append(template)
        
        return results
    
    def render_template(self, template_id: str, **kwargs) -> str:
        """渲染模板"""
        template = self.get_template(template_id)
        if not template:
            raise ValueError(f"模板不存在: {template_id}")
        
        try:
            return template.render(**kwargs)
        except Exception as e:
            self.logger.error(f"模板渲染失败: {e}")
            raise
    
    def create_messages_from_template(
        self, 
        template_id: str, 
        system_message: Optional[str] = None,
        **kwargs
    ) -> List[AIMessage]:
        """从模板创建消息列表"""
        messages = []
        
        # 添加系统消息
        if system_message:
            messages.append(AIMessage(
                role=MessageRole.SYSTEM,
                content=system_message
            ))
        
        # 渲染用户消息
        user_content = self.render_template(template_id, **kwargs)
        messages.append(AIMessage(
            role=MessageRole.USER,
            content=user_content
        ))
        
        return messages
    
    def save_template(self, template: PromptTemplate) -> bool:
        """保存模板到用户目录"""
        try:
            templates_dir = Path(self.config_manager.get_user_data_dir()) / "templates"
            templates_dir.mkdir(parents=True, exist_ok=True)
            
            template_file = templates_dir / f"{template.id}.json"
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(template.to_dict(), f, ensure_ascii=False, indent=2)
            
            # 更新内存中的模板
            self.templates[template.id] = template
            self._add_to_category(template.category, template.id)
            
            self.logger.info(f"模板已保存: {template.id}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存模板失败: {e}")
            return False
    
    def delete_template(self, template_id: str) -> bool:
        """删除模板"""
        try:
            if template_id in self.templates:
                template = self.templates[template_id]
                
                # 删除文件
                templates_dir = Path(self.config_manager.get_user_data_dir()) / "templates"
                for ext in ['.json', '.yaml']:
                    template_file = templates_dir / f"{template_id}{ext}"
                    if template_file.exists():
                        template_file.unlink()
                
                # 从内存中删除
                del self.templates[template_id]
                if template.category in self.categories:
                    self.categories[template.category] = [
                        tid for tid in self.categories[template.category] 
                        if tid != template_id
                    ]
                
                self.logger.info(f"模板已删除: {template_id}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"删除模板失败: {e}")
            return False
    
    def get_all_categories(self) -> List[str]:
        """获取所有分类"""
        return list(self.categories.keys())
    
    def get_template_count(self) -> int:
        """获取模板总数"""
        return len(self.templates)


# 全局提示词管理器实例
_prompt_manager: Optional[PromptManager] = None


def get_prompt_manager() -> PromptManager:
    """获取全局提示词管理器实例"""
    global _prompt_manager
    if _prompt_manager is None:
        _prompt_manager = PromptManager()
    return _prompt_manager
