"""
项目数据模型

定义小说项目的核心数据结构
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
import uuid
import json


class ProjectType(Enum):
    """项目类型枚举"""
    FANTASY = "fantasy"      # 玄幻
    URBAN = "urban"          # 都市
    SCIFI = "scifi"          # 科幻
    ROMANCE = "romance"      # 言情
    MYSTERY = "mystery"      # 悬疑
    HISTORICAL = "historical" # 历史
    CUSTOM = "custom"        # 自定义


class ProjectStatus(Enum):
    """项目状态枚举"""
    DRAFT = "draft"           # 草稿
    WRITING = "writing"       # 写作中
    EDITING = "editing"       # 编辑中
    COMPLETE = "complete"     # 已完成
    PUBLISHED = "published"   # 已发布
    ARCHIVED = "archived"     # 已归档


class ProjectPriority(Enum):
    """项目优先级枚举"""
    LOW = "low"               # 低优先级
    MEDIUM = "medium"         # 中等优先级
    HIGH = "high"             # 高优先级
    URGENT = "urgent"         # 紧急


@dataclass
class ProjectSettings:
    """项目设置"""
    word_count_goal: int = 100000               # 目标字数
    daily_word_goal: int = 2000                 # 日目标字数
    auto_save_interval: int = 300               # 自动保存间隔(秒)
    backup_enabled: bool = True                 # 启用备份
    ai_assistant_enabled: bool = True           # 启用AI助手
    preferred_ai_model: str = "gpt-4"          # 首选AI模型
    writing_mode: str = "focus"                 # 写作模式
    theme: str = "auto"                         # 主题
    font_family: str = "JetBrains Mono"        # 字体
    font_size: int = 14                         # 字体大小
    line_spacing: float = 1.6                   # 行间距
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "word_count_goal": self.word_count_goal,
            "daily_word_goal": self.daily_word_goal,
            "auto_save_interval": self.auto_save_interval,
            "backup_enabled": self.backup_enabled,
            "ai_assistant_enabled": self.ai_assistant_enabled,
            "preferred_ai_model": self.preferred_ai_model,
            "writing_mode": self.writing_mode,
            "theme": self.theme,
            "font_family": self.font_family,
            "font_size": self.font_size,
            "line_spacing": self.line_spacing,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ProjectSettings":
        """从字典创建"""
        return cls(**data)


@dataclass
class Project:
    """项目模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    type: ProjectType = ProjectType.FANTASY
    status: ProjectStatus = ProjectStatus.DRAFT
    priority: ProjectPriority = ProjectPriority.MEDIUM
    description: Optional[str] = None
    author: str = ""
    genre: str = ""
    language: str = "zh-CN"
    target_audience: str = ""
    tags: List[str] = field(default_factory=list)
    
    # 统计信息
    word_count: int = 0
    word_count_goal: Optional[int] = None
    current_word_count: int = 0
    chapter_count: int = 0
    character_count: int = 0
    scene_count: int = 0
    
    # 截止日期
    deadline: Optional[datetime] = None
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    last_opened: Optional[datetime] = None
    
    # 项目设置
    settings: ProjectSettings = field(default_factory=ProjectSettings)
    
    # 文件路径
    project_path: Optional[str] = None
    cover_image_path: Optional[str] = None

    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 世界观设定
    world_building: str = ""
    
    # 备注
    notes: str = ""
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()
    
    def set_last_opened(self):
        """设置最后打开时间"""
        self.last_opened = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典（用于JSON序列化）"""
        return {
            "id": self.id,
            "name": self.name,
            "type": self.type.value,
            "status": self.status.value,
            "priority": self.priority.value,
            "description": self.description,
            "author": self.author,
            "genre": self.genre,
            "language": self.language,
            "target_audience": self.target_audience,
            "tags": self.tags,
            "word_count": self.word_count,
            "word_count_goal": self.word_count_goal,
            "current_word_count": self.current_word_count,
            "chapter_count": self.chapter_count,
            "character_count": self.character_count,
            "scene_count": self.scene_count,
            "deadline": self.deadline.isoformat() if self.deadline else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_opened": self.last_opened.isoformat() if self.last_opened else None,
            "settings": self.settings.to_dict(),
            "project_path": self.project_path,
            "metadata": self.metadata,
            "world_building": self.world_building,
            "notes": self.notes,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Project":
        """从字典创建项目"""
        # 处理枚举类型
        project_type = ProjectType(data.get("type", "fantasy"))
        project_status = ProjectStatus(data.get("status", "draft"))
        project_priority = ProjectPriority(data.get("priority", "medium"))
        
        # 处理时间
        created_at = datetime.fromisoformat(data["created_at"]) if data.get("created_at") else datetime.now()
        updated_at = datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else datetime.now()
        last_opened = datetime.fromisoformat(data["last_opened"]) if data.get("last_opened") else None
        deadline = datetime.fromisoformat(data["deadline"]) if data.get("deadline") else None
        
        # 处理设置
        settings_data = data.get("settings", {})
        settings = ProjectSettings.from_dict(settings_data)
        
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            name=data.get("name", ""),
            type=project_type,
            status=project_status,
            priority=project_priority,
            description=data.get("description"),
            author=data.get("author", ""),
            genre=data.get("genre", ""),
            language=data.get("language", "zh-CN"),
            target_audience=data.get("target_audience", ""),
            tags=data.get("tags", []),
            word_count=data.get("word_count", 0),
            word_count_goal=data.get("word_count_goal"),
            current_word_count=data.get("current_word_count", 0),
            chapter_count=data.get("chapter_count", 0),
            character_count=data.get("character_count", 0),
            scene_count=data.get("scene_count", 0),
            deadline=deadline,
            created_at=created_at,
            updated_at=updated_at,
            last_opened=last_opened,
            settings=settings,
            project_path=data.get("project_path"),
            metadata=data.get("metadata", {}),
            world_building=data.get("world_building", ""),
            notes=data.get("notes", ""),
        )
    
    def save_to_file(self, file_path: str):
        """保存到文件"""
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
    
    @classmethod
    def load_from_file(cls, file_path: str) -> "Project":
        """从文件加载"""
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        return cls.from_dict(data)
    
    def __str__(self) -> str:
        return f"Project(id={self.id}, name={self.name}, type={self.type.value})"