"""
主窗口类

应用程序的主窗口实现
"""

import sys
from typing import Optional, Dict, Any
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QToolBar, QStackedWidget, QMessageBox,
    QPushButton, QApplication
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, QObject
from PyQt6.QtGui import QAction, QIcon, QKeySequence, QPixmap
from pathlib import Path
import logging

from ..utils.logger import LoggerMixin
from ..utils.config_utils import get_config_manager
from ..database.db_manager import get_database_manager
from ..core.project_manager import get_project_manager
from .themes.theme_manager import get_theme_manager
from .widgets.modern_widgets import ModernButton, create_modern_button
from .dialogs.new_project_dialog import NewProjectDialog
from .dialogs.open_project_dialog import OpenProjectDialog
from .dashboard.dashboard_widget import DashboardWidget
from .editor.text_editor_widget import TextEditorWidget

logger = logging.getLogger(__name__)


class MainWindow(QMainWindow, LoggerMixin):
    """主窗口类"""
    
    # 信号定义
    project_opened = pyqtSignal(str)    # 项目已打开
    project_closed = pyqtSignal()       # 项目已关闭
    theme_changed = pyqtSignal(str)     # 主题已改变
    
    def __init__(self):
        super().__init__()
        
        # 初始化配置管理器
        self.config_manager = get_config_manager()
        self.config = self.config_manager.config
        
        # 初始化数据库
        self.db_manager = get_database_manager()
        
        # 初始化项目管理器
        self.project_manager = get_project_manager()
        
        # 窗口状态
        self.current_project_id: Optional[str] = None
        self.is_project_open = False
        
        # 初始化UI
        self.setup_ui()
        self.setup_menu()
        self.setup_toolbar()
        self.setup_shortcuts()
        self.setup_signals()

        # 隐藏状态栏
        self.statusBar().hide()
        
        # 应用配置
        self.apply_config()
        
        # 启动检查
        QTimer.singleShot(100, self.startup_check)
        
        self.logger.info("主窗口初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        # 设置窗口属性
        self.setWindowTitle("笔落App - AI辅助小说创作平台")
        self.setMinimumSize(1000, 700)
        
        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建主分割器
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.main_layout.addWidget(self.main_splitter)
        
        # 创建左侧面板（项目导航）
        self.left_panel = QWidget()
        self.left_panel.setMinimumWidth(250)
        self.left_panel.setMaximumWidth(400)
        self.main_splitter.addWidget(self.left_panel)
        
        # 创建中央工作区
        self.work_area = QStackedWidget()
        self.main_splitter.addWidget(self.work_area)
        
        # 创建右侧面板（工具面板）
        self.right_panel = QWidget()
        self.right_panel.setMinimumWidth(200)
        self.right_panel.setMaximumWidth(350)
        self.main_splitter.addWidget(self.right_panel)
        
        # 设置分割器比例
        self.main_splitter.setStretchFactor(0, 0)  # 左侧面板不拉伸
        self.main_splitter.setStretchFactor(1, 1)  # 中央区域可拉伸
        self.main_splitter.setStretchFactor(2, 0)  # 右侧面板不拉伸
        
        # 设置初始分割比例
        self.main_splitter.setSizes([300, 600, 300])
        
        # 创建仪表板页面
        self.create_dashboard_page()

        # 创建文本编辑器页面
        self.create_editor_page()

    def create_dashboard_page(self):
        """创建仪表板页面"""
        self.dashboard_widget = DashboardWidget()

        # 连接仪表板信号
        self.dashboard_widget.new_project_requested.connect(self.new_project)
        self.dashboard_widget.open_project_requested.connect(self.open_project)
        self.dashboard_widget.project_selected.connect(self.on_project_opened_from_dialog)

    def create_editor_page(self):
        """创建文本编辑器页面"""
        self.editor_widget = TextEditorWidget()
        
        # 连接编辑器信号
        self.editor_widget.content_changed.connect(self.on_editor_content_changed)
        self.editor_widget.save_requested.connect(self.save_current)
        
        # 添加到工作区
        self.work_area.addWidget(self.editor_widget)
    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 新建项目
        new_action = QAction("新建项目(&N)", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.triggered.connect(self.show_new_project_dialog)
        file_menu.addAction(new_action)
        
        # 打开项目
        open_action = QAction("打开项目(&O)", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.triggered.connect(self.show_open_project_dialog)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 保存
        save_action = QAction("保存(&S)", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.triggered.connect(self.save_current)
        file_menu.addAction(save_action)
        
        # 另存为
        save_as_action = QAction("另存为(&A)", self)
        save_as_action.setShortcut(QKeySequence.StandardKey.SaveAs)
        save_as_action.triggered.connect(self.save_as)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        # 撤销
        undo_action = QAction("撤销(&U)", self)
        undo_action.setShortcut(QKeySequence.StandardKey.Undo)
        edit_menu.addAction(undo_action)
        
        # 重做
        redo_action = QAction("重做(&R)", self)
        redo_action.setShortcut(QKeySequence.StandardKey.Redo)
        edit_menu.addAction(redo_action)
        
        edit_menu.addSeparator()
        
        # 查找
        find_action = QAction("查找(&F)", self)
        find_action.setShortcut(QKeySequence.StandardKey.Find)
        edit_menu.addAction(find_action)
        
        # 替换
        replace_action = QAction("替换(&H)", self)
        replace_action.setShortcut(QKeySequence.StandardKey.Replace)
        edit_menu.addAction(replace_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 全屏
        fullscreen_action = QAction("全屏(&F)", self)
        fullscreen_action.setShortcut(QKeySequence.StandardKey.FullScreen)
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        view_menu.addSeparator()
        
        # 主题切换
        theme_menu = view_menu.addMenu("主题(&T)")
        
        light_theme_action = QAction("浅色主题", self)
        light_theme_action.triggered.connect(lambda: self.change_theme("light"))
        theme_menu.addAction(light_theme_action)
        
        dark_theme_action = QAction("深色主题", self)
        dark_theme_action.triggered.connect(lambda: self.change_theme("dark"))
        theme_menu.addAction(dark_theme_action)
        
        auto_theme_action = QAction("自动主题", self)
        auto_theme_action.triggered.connect(lambda: self.change_theme("auto"))
        theme_menu.addAction(auto_theme_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        # AI助手
        ai_action = QAction("AI助手(&A)", self)
        ai_action.setShortcut("Ctrl+Shift+A")
        tools_menu.addAction(ai_action)
        
        # 设置
        settings_action = QAction("设置(&S)", self)
        settings_action.setShortcut("Ctrl+,")
        settings_action.triggered.connect(self.show_settings_dialog)
        tools_menu.addAction(settings_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """设置工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        
        # 新建按钮
        new_action = QAction("新建", self)
        new_action.triggered.connect(self.show_new_project_dialog)
        toolbar.addAction(new_action)
        
        # 打开按钮
        open_action = QAction("打开", self)
        open_action.triggered.connect(self.show_open_project_dialog)
        toolbar.addAction(open_action)
        
        # 保存按钮
        save_action = QAction("保存", self)
        save_action.triggered.connect(self.save_current)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # AI助手按钮
        ai_action = QAction("AI助手", self)
        toolbar.addAction(ai_action)
    

    
    def setup_shortcuts(self):
        """设置快捷键"""
        # 这里可以添加额外的快捷键
        pass
    
    def setup_signals(self):
        """设置信号连接"""
        # 项目信号连接
        self.project_opened.connect(self.on_project_opened)
        self.project_closed.connect(self.on_project_closed)
        self.theme_changed.connect(self.on_theme_changed)
    
    def apply_config(self):
        """应用配置"""
        ui_config = self.config.ui
        
        # 设置窗口大小
        self.resize(ui_config.window_width, ui_config.window_height)
        
        # 初始化并应用主题
        self.theme_manager = get_theme_manager()
        self.theme_manager.set_theme(ui_config.theme)
    
    def startup_check(self):
        """启动检查"""
        # 检查数据库连接
        if not self.db_manager.test_connection():
            self.show_error_message("数据库连接失败", "无法连接到数据库，请检查配置。")
            return
        
        # 检查AI配置
        if not self.config_manager.get_ai_api_key("openai"):
            self.logger.warning("未配置AI服务，请在设置中配置API密钥")

        self.logger.info("应用程序启动检查完成")
    
    def change_theme(self, theme: str):
        """改变主题"""
        self.theme_manager.set_theme(theme)
        self.theme_changed.emit(theme)
        self.logger.info(f"主题已切换到: {theme}")
    
    def toggle_fullscreen(self):
        """切换全屏模式"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    # 项目操作方法
    def new_project(self):
        """新建项目"""
        self.show_new_project_dialog()

    def open_project(self):
        """打开项目"""
        self.show_open_project_dialog()

    # 对话框方法（占位符）
    def show_new_project_dialog(self):
        """显示新建项目对话框"""
        self.logger.info("显示新建项目对话框")
        
        dialog = NewProjectDialog(self)
        dialog.project_created.connect(self.on_project_created)
        dialog.exec()
    
    def show_open_project_dialog(self):
        """显示打开项目对话框"""
        self.logger.info("显示打开项目对话框")
        
        dialog = OpenProjectDialog(self)
        dialog.project_opened.connect(self.on_project_opened_from_dialog)
        dialog.exec()
    
    def show_settings_dialog(self):
        """显示设置对话框"""
        self.logger.info("显示设置对话框")
        # TODO: 实现设置对话框
        self.show_info_message("功能开发中", "设置功能正在开发中...")
    
    def show_about_dialog(self):
        """显示关于对话框"""
        about_text = """
        <h3>笔落App v1.0.0</h3>
        <p>AI辅助小说创作平台</p>
        <p>基于Python + PyQt6开发</p>
        <p>Copyright © 2025 BambooFall Team</p>
        """
        QMessageBox.about(self, "关于笔落App", about_text)
    
    # 文件操作方法（占位符）
    def save_current(self):
        """保存当前内容"""
        self.logger.info("保存当前内容")
        
        if self.project_manager.current_project:
            success = self.project_manager.save_project()
            if success:
                self.logger.info("项目已保存")
                self.show_info_message("保存成功", "项目已保存")
            else:
                self.logger.error("保存失败")
                self.show_error_message("保存失败", "无法保存项目，请检查文件权限")
        else:
            self.logger.warning("没有打开的项目")
            self.show_warning_message("保存失败", "没有打开的项目需要保存")
    
    def save_as(self):
        """另存为"""
        self.logger.info("另存为")
        # TODO: 实现另存为功能
    
    # 信号处理方法
    def on_project_created(self, project):
        """项目创建时的处理"""
        self.logger.info(f"项目已创建: {project.name}")
        # 自动打开新创建的项目
        self.on_project_opened_from_dialog(project)
        
        # 刷新仪表板数据
        self.dashboard_widget.refresh_data()
    
    def on_project_opened_from_dialog(self, project):
        """从对话框打开项目时的处理"""
        self.project_opened.emit(project.id)
    
    def on_project_opened(self, project_id: str):
        """项目打开时的处理"""
        self.current_project_id = project_id
        self.is_project_open = True
        
        # 获取项目信息
        project = self.project_manager.current_project
        if project:
            self.logger.info(f"项目 '{project.name}' 已打开")
        else:
            self.logger.info("项目已打开")
        
        # 切换到编辑器视图
        self.switch_to_editor()
        
        self.logger.info(f"项目已打开: {project_id}")
    
    def open_project_by_id(self, project_id: str):
        """根据ID打开项目"""
        try:
            project = self.project_manager.open_project(project_id)
            self.on_project_opened_from_dialog(project)
        except Exception as e:
            self.logger.error(f"打开项目失败: {e}")
            self.show_error_message("打开失败", f"无法打开项目:\n{str(e)}")
    
    def on_project_closed(self):
        """项目关闭时的处理"""
        # 关闭项目管理器中的项目
        self.project_manager.close_project()
        
        self.current_project_id = None
        self.is_project_open = False
        self.switch_to_dashboard()
        
        # 刷新仪表板数据
        self.dashboard_widget.refresh_data()
        self.logger.info("项目已关闭")
    
    def on_theme_changed(self, theme: str):
        """主题改变时的处理"""
        self.logger.info(f"主题已切换到: {theme}")
    
    # 工具方法
    def show_info_message(self, title: str, message: str):
        """显示信息消息"""
        QMessageBox.information(self, title, message)
    
    def show_warning_message(self, title: str, message: str):
        """显示警告消息"""
        QMessageBox.warning(self, title, message)
    
    def show_error_message(self, title: str, message: str):
        """显示错误消息"""
        QMessageBox.critical(self, title, message)
    
    def show_progress(self, value: int, maximum: int = 100):
        """显示进度"""
        self.logger.info(f"进度: {value}/{maximum}")

    def hide_progress(self):
        """隐藏进度条"""
        self.logger.info("进度完成")
    
    def switch_to_editor(self):
        """切换到编辑器视图"""
        self.work_area.setCurrentWidget(self.editor_widget)
    
    def switch_to_dashboard(self):
        """切换到仪表板视图"""
        self.work_area.setCurrentWidget(self.dashboard_widget)
    
    def on_editor_content_changed(self):
        """编辑器内容改变处理"""
        if self.is_project_open:
            self.logger.debug("项目内容已修改")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 检查是否有未保存的项目
        if self.project_manager.current_project:
            reply = QMessageBox.question(
                self,
                "确认退出",
                "确定要退出吗？未保存的更改将会丢失。",
                QMessageBox.StandardButton.Save | 
                QMessageBox.StandardButton.Discard | 
                QMessageBox.StandardButton.Cancel
            )
            
            if reply == QMessageBox.StandardButton.Save:
                if not self.project_manager.save_project():
                    event.ignore()
                    return
            elif reply == QMessageBox.StandardButton.Cancel:
                event.ignore()
                return
        
        # 保存窗口状态
        self.config_manager.update_config(
            ui={
                "window_width": self.width(),
                "window_height": self.height()
            }
        )
        
        # 关闭项目
        self.project_manager.close_project()
        
        # 关闭数据库连接
        self.db_manager.close()
        
        self.logger.info("应用程序正在关闭")
        event.accept()