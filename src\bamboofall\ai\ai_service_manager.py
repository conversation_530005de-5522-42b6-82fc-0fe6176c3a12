"""
AI服务管理器

统一管理不同AI服务提供商，提供服务发现和路由功能
"""

from typing import Dict, List, Optional, Type, Any, AsyncGenerator
import logging
from dataclasses import dataclass
import hashlib
import json

from .ai_service_base import (
    AIServiceBase, AIMessage, AIResponse, AIConfig, 
    AIServiceError, APIKeyError
)
from ..utils.logger import LoggerMixin
from ..utils.config_utils import get_config_manager
from ..core.cache_manager import get_ai_cache, get_cache_manager
from ..core.performance_monitor import performance_monitor


@dataclass
class ProviderInfo:
    """服务提供商信息"""
    name: str
    display_name: str
    service_class: Type[AIServiceBase]
    default_model: str
    available_models: List[str]
    description: str = ""
    website: str = ""
    

class AIServiceManager(LoggerMixin):
    """AI服务管理器"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.services: Dict[str, AIServiceBase] = {}
        self.providers: Dict[str, ProviderInfo] = {}
        self.default_provider: Optional[str] = None
        
        # 缓存和性能优化
        self.cache_manager = get_cache_manager()
        self.ai_cache = get_ai_cache()
        
        # 缓存配置
        self.enable_cache = self.config_manager.get("ai_cache_enabled", True)
        self.cache_ttl = self.config_manager.get("ai_cache_ttl", 3600)  # 1小时
        
        # 注册内置服务提供商
        self._register_builtin_providers()
        
        # 初始化服务
        self._initialize_services()
        
        self.logger.info("AI服务管理器初始化完成")
    
    def _register_builtin_providers(self):
        """注册内置服务提供商"""
        # 延迟导入避免循环依赖
        try:
            from .openai_service import OpenAIService
            
            self.providers["openai"] = ProviderInfo(
                name="openai",
                display_name="OpenAI",
                service_class=OpenAIService,
                default_model="gpt-3.5-turbo",
                available_models=[
                    "gpt-4", "gpt-4-turbo", "gpt-3.5-turbo", 
                    "gpt-3.5-turbo-16k", "text-davinci-003"
                ],
                description="OpenAI GPT系列模型",
                website="https://openai.com"
            )
            self.logger.info("OpenAI服务注册成功")
        except ImportError as e:
            self.logger.warning(f"无法导入OpenAI服务: {e}")
        except Exception as e:
            self.logger.error(f"注册OpenAI服务时发生错误: {e}")
        
        # 注册Anthropic服务
        try:
            from .anthropic_service import AnthropicService
            
            self.providers["anthropic"] = ProviderInfo(
                name="anthropic",
                display_name="Anthropic",
                service_class=AnthropicService,
                default_model="claude-3-sonnet-20240229",
                available_models=[
                    "claude-3-opus-20240229", "claude-3-sonnet-20240229", 
                    "claude-3-haiku-20240307", "claude-2.1", "claude-2.0",
                    "claude-instant-1.2", "claude-instant-1.1"
                ],
                description="Anthropic Claude系列模型",
                website="https://www.anthropic.com"
            )
            self.logger.info("Anthropic服务注册成功")
        except ImportError as e:
            self.logger.warning(f"无法导入Anthropic服务: {e}")
        except Exception as e:
            self.logger.error(f"注册Anthropic服务时发生错误: {e}")
        
        # 可以在这里注册更多提供商
        # self._register_azure_provider()
    
    def clear_cache(self, pattern: Optional[str] = None):
        """清理AI缓存"""
        if pattern:
            self.ai_cache.invalidate_pattern(pattern)
            self.cache_manager.invalidate_pattern(pattern)
        else:
            self.ai_cache.clear()
            # 清理所有AI相关缓存
            self.cache_manager.invalidate_pattern("ai_*")
        
        self.logger.info(f"AI缓存清理完成: {pattern or '全部'}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'ai_cache_stats': self.ai_cache.get_stats(),
            'global_cache_stats': self.cache_manager.get_stats(),
            'cache_enabled': self.enable_cache,
            'cache_ttl': self.cache_ttl
        }
    
    def set_cache_config(self, enabled: bool = True, ttl: int = 3600):
        """设置缓存配置"""
        self.enable_cache = enabled
        self.cache_ttl = ttl
        
        # 更新配置管理器
        self.config_manager.set("ai_cache_enabled", enabled)
        self.config_manager.set("ai_cache_ttl", ttl)
        
        self.logger.info(f"AI缓存配置更新: enabled={enabled}, ttl={ttl}")
    
    def _initialize_services(self):
        """初始化服务实例"""
        config = self.config_manager.config
        
        for provider_name, provider_info in self.providers.items():
            try:
                # 获取API密钥
                api_key = self.config_manager.get_ai_api_key(provider_name)
                if not api_key:
                    self.logger.warning(f"未配置{provider_info.display_name}的API密钥")
                    continue
                
                # 创建配置
                ai_config = AIConfig(
                    api_key=api_key,
                    model=provider_info.default_model,
                    max_tokens=config.ai.max_tokens,
                    temperature=config.ai.temperature,
                    timeout=config.ai.timeout
                )
                
                # 创建服务实例
                service = provider_info.service_class(ai_config)
                
                # 验证配置
                if service.validate_config():
                    self.services[provider_name] = service
                    
                    # 设置默认提供商
                    if self.default_provider is None:
                        self.default_provider = provider_name
                        
                    self.logger.info(f"{provider_info.display_name}服务初始化成功")
                else:
                    self.logger.error(f"{provider_info.display_name}配置验证失败")
                    
            except Exception as e:
                self.logger.error(f"初始化{provider_info.display_name}服务失败: {e}")
    
    def get_service(self, provider: Optional[str] = None) -> Optional[AIServiceBase]:
        """获取AI服务实例"""
        if provider is None:
            provider = self.default_provider
        
        if provider and provider in self.services:
            return self.services[provider]
        
        self.logger.warning(f"未找到服务提供商: {provider}")
        return None
    
    def get_available_providers(self) -> List[str]:
        """获取可用的服务提供商列表"""
        return list(self.services.keys())
    
    def get_provider_info(self, provider: str) -> Optional[ProviderInfo]:
        """获取提供商信息"""
        return self.providers.get(provider)
    
    def get_available_models(self, provider: Optional[str] = None) -> List[str]:
        """获取可用模型列表"""
        if provider is None:
            provider = self.default_provider
        
        if provider and provider in self.providers:
            return self.providers[provider].available_models
        
        return []
    
    def _generate_cache_key(self, messages: List[AIMessage], provider: str, model: str, **kwargs) -> str:
        """生成缓存键"""
        # 创建消息内容的哈希
        message_data = [{
            'role': msg.role.value,
            'content': msg.content
        } for msg in messages]
        
        # 包含所有影响响应的参数
        cache_data = {
            'messages': message_data,
            'provider': provider,
            'model': model,
            'kwargs': {k: v for k, v in kwargs.items() if k in ['temperature', 'max_tokens', 'top_p']}
        }
        
        # 生成MD5哈希
        cache_str = json.dumps(cache_data, sort_keys=True)
        return f"ai_chat_{hashlib.md5(cache_str.encode()).hexdigest()}"
    
    @performance_monitor(category="ai")
    async def chat(
        self,
        messages: List[AIMessage],
        provider: Optional[str] = None,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> AIResponse:
        """聊天接口"""
        service = self.get_service(provider)
        if not service:
            raise AIServiceError(f"服务提供商不可用: {provider}")
        
        # 检查缓存
        if self.enable_cache and use_cache:
            cache_key = self._generate_cache_key(messages, provider or self.default_provider, model or service.config.model, **kwargs)
            cached_response = self.ai_cache.get_result(cache_key)
            if cached_response:
                self.logger.debug(f"从缓存返回AI响应: {cache_key[:16]}...")
                return cached_response
        
        try:
            response = await service.chat(messages, model, **kwargs)
            
            # 缓存响应
            if self.enable_cache and use_cache and response:
                cache_key = self._generate_cache_key(messages, provider or self.default_provider, model or service.config.model, **kwargs)
                self.ai_cache.cache_result(cache_key, response, ttl=self.cache_ttl)
                self.logger.debug(f"缓存AI响应: {cache_key[:16]}...")
            
            return response
        except Exception as e:
            self.logger.error(f"聊天请求失败: {e}")
            raise
    
    async def stream_chat(
        self,
        messages: List[AIMessage],
        provider: Optional[str] = None, 
        model: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式聊天接口"""
        service = self.get_service(provider)
        if not service:
            raise AIServiceError(f"服务提供商不可用: {provider}")
        
        try:
            async for chunk in service.stream_chat(messages, model, **kwargs):
                yield chunk
        except Exception as e:
            self.logger.error(f"流式聊天请求失败: {e}")
            raise
    
    @performance_monitor(category="ai")
    async def complete(
        self,
        prompt: str,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> AIResponse:
        """文本补全接口"""
        service = self.get_service(provider)
        if not service:
            raise AIServiceError(f"服务提供商不可用: {provider}")
        
        # 检查缓存
        if self.enable_cache and use_cache:
            cache_key = f"ai_complete_{hashlib.md5((prompt + (provider or self.default_provider) + (model or service.config.model)).encode()).hexdigest()}"
            cached_response = self.ai_cache.get_result(cache_key)
            if cached_response:
                self.logger.debug(f"从缓存返回AI补全响应: {cache_key[:16]}...")
                return cached_response
        
        try:
            response = await service.complete(prompt, model, **kwargs)
            
            # 缓存响应
            if self.enable_cache and use_cache and response:
                cache_key = f"ai_complete_{hashlib.md5((prompt + (provider or self.default_provider) + (model or service.config.model)).encode()).hexdigest()}"
                self.ai_cache.cache_result(cache_key, response, ttl=self.cache_ttl)
                self.logger.debug(f"缓存AI补全响应: {cache_key[:16]}...")
            
            return response
        except Exception as e:
            self.logger.error(f"文本补全请求失败: {e}")
            raise
    
    @performance_monitor(category="ai")
    async def get_embedding(
        self,
        text: str,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        use_cache: bool = True
    ) -> List[float]:
        """获取文本嵌入"""
        service = self.get_service(provider)
        if not service:
            raise AIServiceError(f"服务提供商不可用: {provider}")
        
        # 检查缓存
        if self.enable_cache and use_cache:
            cache_key = f"ai_embedding_{hashlib.md5((text + (provider or self.default_provider) + (model or service.config.model)).encode()).hexdigest()}"
            cached_result = self.cache_manager.get(cache_key)
            if cached_result:
                self.logger.debug(f"从缓存返回文本嵌入: {cache_key[:16]}...")
                return cached_result
        
        try:
            result = await service.get_embedding(text, model)
            
            # 缓存结果
            if self.enable_cache and use_cache and result:
                cache_key = f"ai_embedding_{hashlib.md5((text + (provider or self.default_provider) + (model or service.config.model)).encode()).hexdigest()}"
                self.cache_manager.set(cache_key, result, ttl=self.cache_ttl * 2)  # 嵌入缓存时间更长
                self.logger.debug(f"缓存文本嵌入: {cache_key[:16]}...")
            
            return result
        except Exception as e:
            self.logger.error(f"获取文本嵌入失败: {e}")
            raise
    
    def set_default_provider(self, provider: str):
        """设置默认服务提供商"""
        if provider in self.services:
            self.default_provider = provider
            self.logger.info(f"默认AI服务提供商已设置为: {provider}")
        else:
            raise ValueError(f"未知的服务提供商: {provider}")
    
    def reload_services(self):
        """重新加载服务（用于配置更新后）"""
        self.services.clear()
        self.default_provider = None
        self._initialize_services()
        self.logger.info("AI服务已重新加载")
    
    @performance_monitor(category="ai")
    async def test_all_connections(self) -> Dict[str, bool]:
        """测试所有服务连接"""
        results = {}
        
        for provider_name, service in self.services.items():
            try:
                results[provider_name] = await service.test_connection()
                self.logger.info(f"{provider_name}连接测试: {'成功' if results[provider_name] else '失败'}")
            except Exception as e:
                results[provider_name] = False
                self.logger.error(f"{provider_name}连接测试异常: {e}")
        
        return results
    
    def clear_cache(self, pattern: Optional[str] = None):
        """清除AI缓存"""
        if pattern:
            # 清除特定模式的缓存
            self.ai_cache.clear_pattern(pattern)
            self.cache_manager.invalidate_pattern(f"ai_{pattern}")
        else:
            # 清除所有AI缓存
            self.ai_cache.clear()
            self.cache_manager.invalidate_pattern("ai_*")
            
        self.logger.info(f"AI缓存已清除: {pattern or '全部'}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "ai_cache_stats": self.ai_cache.get_stats(),
            "cache_enabled": self.enable_cache,
            "cache_ttl": self.cache_ttl,
            "global_cache_stats": self.cache_manager.get_stats()
        }
    
    def set_cache_config(self, enabled: bool, ttl: Optional[int] = None):
        """设置缓存配置"""
        self.enable_cache = enabled
        if ttl is not None:
            self.cache_ttl = ttl
            
        # 更新配置文件
        self.config_manager.set("ai_cache_enabled", enabled)
        if ttl is not None:
            self.config_manager.set("ai_cache_ttl", ttl)
            
        self.logger.info(f"AI缓存配置已更新: enabled={enabled}, ttl={ttl}")


# 全局服务管理器实例
_ai_service_manager: Optional[AIServiceManager] = None


def get_ai_service_manager() -> AIServiceManager:
    """获取全局AI服务管理器实例"""
    global _ai_service_manager
    if _ai_service_manager is None:
        _ai_service_manager = AIServiceManager()
    return _ai_service_manager


def reset_ai_service_manager():
    """重置AI服务管理器（用于测试）"""
    global _ai_service_manager
    _ai_service_manager = None