"""
AI服务管理器

统一管理不同AI服务提供商，提供服务发现和路由功能
"""

from typing import Dict, List, Optional, Type, Any, AsyncGenerator
import logging
from dataclasses import dataclass

from .ai_service_base import (
    AIServiceBase, AIMessage, AIResponse, AIConfig, 
    AIServiceError, APIKeyError
)
from ..utils.logger import LoggerMixin
from ..utils.config_utils import get_config_manager


@dataclass
class ProviderInfo:
    """服务提供商信息"""
    name: str
    display_name: str
    service_class: Type[AIServiceBase]
    default_model: str
    available_models: List[str]
    description: str = ""
    website: str = ""
    

class AIServiceManager(LoggerMixin):
    """AI服务管理器"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.services: Dict[str, AIServiceBase] = {}
        self.providers: Dict[str, ProviderInfo] = {}
        self.default_provider: Optional[str] = None
        
        # 注册内置服务提供商
        self._register_builtin_providers()
        
        # 初始化服务
        self._initialize_services()
        
        self.logger.info("AI服务管理器初始化完成")
    
    def _register_builtin_providers(self):
        """注册内置服务提供商"""
        # 延迟导入避免循环依赖
        try:
            from .openai_service import OpenAIService
            
            self.providers["openai"] = ProviderInfo(
                name="openai",
                display_name="OpenAI",
                service_class=OpenAIService,
                default_model="gpt-3.5-turbo",
                available_models=[
                    "gpt-4", "gpt-4-turbo", "gpt-3.5-turbo",
                    "text-davinci-003", "text-embedding-ada-002"
                ],
                description="OpenAI GPT系列模型",
                website="https://openai.com"
            )
        except ImportError:
            self.logger.warning("无法导入OpenAI服务")
        
        # 可以在这里注册更多提供商
        # self._register_anthropic_provider()
        # self._register_azure_provider()
    
    def _initialize_services(self):
        """初始化服务实例"""
        config = self.config_manager.config
        
        for provider_name, provider_info in self.providers.items():
            try:
                # 获取API密钥
                api_key = self.config_manager.get_ai_api_key(provider_name)
                if not api_key:
                    self.logger.warning(f"未配置{provider_info.display_name}的API密钥")
                    continue
                
                # 创建配置
                ai_config = AIConfig(
                    api_key=api_key,
                    model=provider_info.default_model,
                    max_tokens=config.ai.max_tokens,
                    temperature=config.ai.temperature,
                    timeout=config.ai.timeout
                )
                
                # 创建服务实例
                service = provider_info.service_class(ai_config)
                
                # 验证配置
                if service.validate_config():
                    self.services[provider_name] = service
                    
                    # 设置默认提供商
                    if self.default_provider is None:
                        self.default_provider = provider_name
                        
                    self.logger.info(f"{provider_info.display_name}服务初始化成功")
                else:
                    self.logger.error(f"{provider_info.display_name}配置验证失败")
                    
            except Exception as e:
                self.logger.error(f"初始化{provider_info.display_name}服务失败: {e}")
    
    def get_service(self, provider: Optional[str] = None) -> Optional[AIServiceBase]:
        """获取AI服务实例"""
        if provider is None:
            provider = self.default_provider
        
        if provider and provider in self.services:
            return self.services[provider]
        
        self.logger.warning(f"未找到服务提供商: {provider}")
        return None
    
    def get_available_providers(self) -> List[str]:
        """获取可用的服务提供商列表"""
        return list(self.services.keys())
    
    def get_provider_info(self, provider: str) -> Optional[ProviderInfo]:
        """获取提供商信息"""
        return self.providers.get(provider)
    
    def get_available_models(self, provider: Optional[str] = None) -> List[str]:
        """获取可用模型列表"""
        if provider is None:
            provider = self.default_provider
        
        if provider and provider in self.providers:
            return self.providers[provider].available_models
        
        return []
    
    async def chat(
        self,
        messages: List[AIMessage],
        provider: Optional[str] = None,
        model: Optional[str] = None,
        **kwargs
    ) -> AIResponse:
        """聊天接口"""
        service = self.get_service(provider)
        if not service:
            raise AIServiceError(f"服务提供商不可用: {provider}")
        
        try:
            return await service.chat(messages, model, **kwargs)
        except Exception as e:
            self.logger.error(f"聊天请求失败: {e}")
            raise
    
    async def stream_chat(
        self,
        messages: List[AIMessage],
        provider: Optional[str] = None, 
        model: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式聊天接口"""
        service = self.get_service(provider)
        if not service:
            raise AIServiceError(f"服务提供商不可用: {provider}")
        
        try:
            async for chunk in service.stream_chat(messages, model, **kwargs):
                yield chunk
        except Exception as e:
            self.logger.error(f"流式聊天请求失败: {e}")
            raise
    
    async def complete(
        self,
        prompt: str,
        provider: Optional[str] = None,
        model: Optional[str] = None,
        **kwargs
    ) -> AIResponse:
        """文本补全接口"""
        service = self.get_service(provider)
        if not service:
            raise AIServiceError(f"服务提供商不可用: {provider}")
        
        try:
            return await service.complete(prompt, model, **kwargs)
        except Exception as e:
            self.logger.error(f"文本补全请求失败: {e}")
            raise
    
    async def get_embedding(
        self,
        text: str,
        provider: Optional[str] = None,
        model: Optional[str] = None
    ) -> List[float]:
        """获取文本嵌入"""
        service = self.get_service(provider)
        if not service:
            raise AIServiceError(f"服务提供商不可用: {provider}")
        
        try:
            return await service.get_embedding(text, model)
        except Exception as e:
            self.logger.error(f"获取文本嵌入失败: {e}")
            raise
    
    def set_default_provider(self, provider: str):
        """设置默认服务提供商"""
        if provider in self.services:
            self.default_provider = provider
            self.logger.info(f"默认AI服务提供商已设置为: {provider}")
        else:
            raise ValueError(f"未知的服务提供商: {provider}")
    
    def reload_services(self):
        """重新加载服务（用于配置更新后）"""
        self.services.clear()
        self.default_provider = None
        self._initialize_services()
        self.logger.info("AI服务已重新加载")
    
    async def test_all_connections(self) -> Dict[str, bool]:
        """测试所有服务连接"""
        results = {}
        
        for provider_name, service in self.services.items():
            try:
                results[provider_name] = await service.test_connection()
                self.logger.info(f"{provider_name}连接测试: {'成功' if results[provider_name] else '失败'}")
            except Exception as e:
                results[provider_name] = False
                self.logger.error(f"{provider_name}连接测试异常: {e}")
        
        return results


# 全局服务管理器实例
_ai_service_manager: Optional[AIServiceManager] = None


def get_ai_service_manager() -> AIServiceManager:
    """获取全局AI服务管理器实例"""
    global _ai_service_manager
    if _ai_service_manager is None:
        _ai_service_manager = AIServiceManager()
    return _ai_service_manager


def reset_ai_service_manager():
    """重置AI服务管理器（用于测试）"""
    global _ai_service_manager
    _ai_service_manager = None