"""统一异常处理模块

提供项目的异常处理机制，包括：
- 自定义异常类
- 异常处理器
- 异常处理装饰器
- 异常工具函数
"""

# 导入基础异常类
from .base import (
    BambooFallError,
    BambooFallWarning,
    ErrorCode,
    ErrorSeverity
)

# 导入业务异常类
from .business import (
    ProjectError,
    ProjectNotFoundError,
    ProjectValidationError,
    CharacterError,
    ChapterError,
    OutlineError,
    AIServiceError,
    DatabaseError,
    FileOperationError,
    ConfigurationError,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    NetworkError,
    TimeoutError,
    ResourceNotFoundError,
    ResourceConflictError,
    ResourceExhaustedError
)

# 导入异常处理器
from .handlers import (
    ExceptionHandler,
    GlobalExceptionHandler,
    UIExceptionHandler,
    ServiceExceptionHandler,
    DatabaseExceptionHandler,
    FileExceptionHandler,
    NetworkExceptionHandler
)

# 导入异常装饰器
from .decorators import (
    handle_exceptions,
    handle_async_exceptions,
    retry_on_exception,
    log_exceptions,
    validate_input,
    timeout_handler,
    circuit_breaker,
    get_circuit_breaker_stats,
    reset_circuit_breaker
)

# 导入工具函数
from .utils import (
    format_exception,
    sanitize_exception_message,
    get_exception_chain,
    analyze_exception_severity,
    create_error_report,
    log_exception_with_context,
    is_recoverable_error,
    get_exception_suggestions,
    format_user_friendly_message
)

__all__ = [
    # 基础异常
    'BambooFallError',
    'BambooFallWarning',
    'ErrorCode',
    'ErrorSeverity',
    
    # 业务异常
    'ProjectError',
    'ProjectNotFoundError',
    'ProjectValidationError',
    'CharacterError',
    'ChapterError',
    'OutlineError',
    'AIServiceError',
    'DatabaseError',
    'FileOperationError',
    'ConfigurationError',
    'ValidationError',
    'AuthenticationError',
    'AuthorizationError',
    'NetworkError',
    'TimeoutError',
    'ResourceNotFoundError',
    'ResourceConflictError',
    'ResourceExhaustedError',
    
    # 异常处理器
    'ExceptionHandler',
    'GlobalExceptionHandler',
    'UIExceptionHandler',
    'ServiceExceptionHandler',
    'DatabaseExceptionHandler',
    'FileExceptionHandler',
    'NetworkExceptionHandler',
    
    # 装饰器
    'handle_exceptions',
    'handle_async_exceptions',
    'retry_on_exception',
    'log_exceptions',
    'validate_input',
    'timeout_handler',
    'circuit_breaker',
    'get_circuit_breaker_stats',
    'reset_circuit_breaker',
    
    # 工具函数
    'format_exception',
    'sanitize_exception_message',
    'get_exception_chain',
    'analyze_exception_severity',
    'create_error_report',
    'log_exception_with_context',
    'is_recoverable_error',
    'get_exception_suggestions',
    'format_user_friendly_message',
]