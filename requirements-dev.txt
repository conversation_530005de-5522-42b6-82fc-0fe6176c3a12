# 开发环境依赖
# 包含生产依赖
-r requirements.txt

# 代码质量工具
black>=23.9.0
flake8>=6.0.0
mypy>=1.5.0
isort>=5.12.0
pylint>=3.0.0

# 测试框架
pytest>=7.4.0
pytest-qt>=4.2.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0

# 类型检查和静态分析
types-requests>=2.31.0
types-toml>=0.10.8

# 文档生成
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

# 开发工具
pre-commit>=3.5.0
tox>=4.11.0
pip-tools>=7.3.0

# 调试工具
pudb>=2023.1
icecream>=2.1.0

# 性能分析
memory-profiler>=0.61.0
line-profiler>=4.1.0

# 打包工具
pyinstaller>=6.0.0
cx-freeze>=6.15.0
auto-py-to-exe>=2.40.0