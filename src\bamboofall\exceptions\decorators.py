"""异常处理装饰器模块

提供各种实用的异常处理装饰器，包括重试、超时、熔断器等功能。
"""

import asyncio
import functools
import logging
import time
import threading
from typing import (
    Any, Callable, Dict, List, Optional, Type, Union, Tuple,
    TypeVar, ParamSpec, Awaitable
)
from datetime import datetime, timedelta
from collections import defaultdict

from .base import BambooFallError, ErrorCode, ErrorSeverity
from .business import TimeoutError, NetworkError, ValidationError

# 类型变量
P = ParamSpec('P')
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Any])


def handle_exceptions(
    exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
    default_return: Any = None,
    log_level: int = logging.ERROR,
    reraise: bool = False,
    callback: Optional[Callable[[Exception], None]] = None
):
    """异常处理装饰器
    
    Args:
        exceptions: 要捕获的异常类型
        default_return: 异常时的默认返回值
        log_level: 日志级别
        reraise: 是否重新抛出异常
        callback: 异常回调函数
    """
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @functools.wraps(func)
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            try:
                return func(*args, **kwargs)
            except exceptions as e:
                logger = logging.getLogger(func.__module__)
                logger.log(log_level, f"函数 {func.__name__} 发生异常: {e}", exc_info=True)
                
                if callback:
                    try:
                        callback(e)
                    except Exception as cb_error:
                        logger.error(f"异常回调执行失败: {cb_error}")
                
                if reraise:
                    raise
                
                return default_return
        
        return wrapper
    return decorator


def handle_async_exceptions(
    exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
    default_return: Any = None,
    log_level: int = logging.ERROR,
    reraise: bool = False,
    callback: Optional[Callable[[Exception], Awaitable[None]]] = None
):
    """异步异常处理装饰器"""
    def decorator(func: Callable[P, Awaitable[T]]) -> Callable[P, Awaitable[T]]:
        @functools.wraps(func)
        async def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            try:
                return await func(*args, **kwargs)
            except exceptions as e:
                logger = logging.getLogger(func.__module__)
                logger.log(log_level, f"异步函数 {func.__name__} 发生异常: {e}", exc_info=True)
                
                if callback:
                    try:
                        await callback(e)
                    except Exception as cb_error:
                        logger.error(f"异步异常回调执行失败: {cb_error}")
                
                if reraise:
                    raise
                
                return default_return
        
        return wrapper
    return decorator


def retry_on_exception(
    exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
    max_retries: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    max_delay: float = 60.0,
    jitter: bool = True,
    on_retry: Optional[Callable[[Exception, int], None]] = None
):
    """重试装饰器
    
    Args:
        exceptions: 触发重试的异常类型
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 退避倍数
        max_delay: 最大延迟时间（秒）
        jitter: 是否添加随机抖动
        on_retry: 重试时的回调函数
    """
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @functools.wraps(func)
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            logger = logging.getLogger(func.__module__)
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logger.error(f"函数 {func.__name__} 重试 {max_retries} 次后仍然失败: {e}")
                        raise
                    
                    # 计算延迟时间
                    current_delay = min(delay * (backoff ** attempt), max_delay)
                    if jitter:
                        import random
                        current_delay *= (0.5 + random.random() * 0.5)  # 添加50%的随机抖动
                    
                    logger.warning(
                        f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败: {e}，"
                        f"{current_delay:.2f}秒后重试"
                    )
                    
                    if on_retry:
                        try:
                            on_retry(e, attempt + 1)
                        except Exception as cb_error:
                            logger.error(f"重试回调执行失败: {cb_error}")
                    
                    time.sleep(current_delay)
            
            # 这行代码理论上不会执行到，但为了类型检查
            raise last_exception
        
        return wrapper
    return decorator


def log_exceptions(
    logger: Optional[logging.Logger] = None,
    level: int = logging.ERROR,
    message: Optional[str] = None,
    include_args: bool = False,
    include_result: bool = False
):
    """异常日志装饰器
    
    Args:
        logger: 日志记录器
        level: 日志级别
        message: 自定义日志消息
        include_args: 是否包含函数参数
        include_result: 是否包含函数返回值
    """
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        nonlocal logger
        if logger is None:
            logger = logging.getLogger(func.__module__)
        
        @functools.wraps(func)
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                
                if include_result:
                    execution_time = time.time() - start_time
                    logger.debug(
                        f"函数 {func.__name__} 执行成功 (耗时: {execution_time:.4f}秒)，"
                        f"返回值: {result}"
                    )
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                
                log_msg = message or f"函数 {func.__name__} 执行失败 (耗时: {execution_time:.4f}秒)"
                
                extra_info = {}
                if include_args:
                    extra_info['args'] = args
                    extra_info['kwargs'] = kwargs
                
                logger.log(level, f"{log_msg}: {e}", exc_info=True, extra=extra_info)
                raise
        
        return wrapper
    return decorator


def validate_input(
    validators: Dict[str, Callable[[Any], bool]],
    error_messages: Optional[Dict[str, str]] = None
):
    """输入验证装饰器
    
    Args:
        validators: 验证器字典，键为参数名，值为验证函数
        error_messages: 错误消息字典
    """
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @functools.wraps(func)
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            import inspect
            
            # 获取函数签名
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # 验证参数
            for param_name, validator in validators.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    
                    if not validator(value):
                        error_msg = (
                            error_messages.get(param_name, f"参数 {param_name} 验证失败")
                            if error_messages else f"参数 {param_name} 验证失败"
                        )
                        
                        raise ValidationError(
                            message=error_msg,
                            field=param_name,
                            value=value
                        )
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def timeout_handler(
    timeout_seconds: float,
    timeout_exception: Type[Exception] = TimeoutError,
    timeout_message: Optional[str] = None
):
    """超时处理装饰器
    
    Args:
        timeout_seconds: 超时时间（秒）
        timeout_exception: 超时异常类型
        timeout_message: 超时消息
    """
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        @functools.wraps(func)
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            result = [None]
            exception = [None]
            
            def target():
                try:
                    result[0] = func(*args, **kwargs)
                except Exception as e:
                    exception[0] = e
            
            thread = threading.Thread(target=target)
            thread.daemon = True
            thread.start()
            thread.join(timeout_seconds)
            
            if thread.is_alive():
                # 超时了
                msg = timeout_message or f"函数 {func.__name__} 执行超时 ({timeout_seconds}秒)"
                raise timeout_exception(msg, timeout_seconds=timeout_seconds, operation=func.__name__)
            
            if exception[0]:
                raise exception[0]
            
            return result[0]
        
        return wrapper
    return decorator


class CircuitBreakerState:
    """熔断器状态"""
    CLOSED = "closed"      # 关闭状态，正常工作
    OPEN = "open"          # 开启状态，拒绝请求
    HALF_OPEN = "half_open" # 半开状态，尝试恢复


class CircuitBreakerStats:
    """熔断器统计信息"""
    
    def __init__(self):
        self.total_requests = 0
        self.failed_requests = 0
        self.success_requests = 0
        self.last_failure_time = None
        self.last_success_time = None
    
    def record_success(self):
        """记录成功"""
        self.total_requests += 1
        self.success_requests += 1
        self.last_success_time = datetime.now()
    
    def record_failure(self):
        """记录失败"""
        self.total_requests += 1
        self.failed_requests += 1
        self.last_failure_time = datetime.now()
    
    def get_failure_rate(self) -> float:
        """获取失败率"""
        if self.total_requests == 0:
            return 0.0
        return self.failed_requests / self.total_requests
    
    def reset(self):
        """重置统计"""
        self.total_requests = 0
        self.failed_requests = 0
        self.success_requests = 0
        self.last_failure_time = None
        self.last_success_time = None


# 全局熔断器实例字典
_circuit_breakers: Dict[str, Dict[str, Any]] = defaultdict(dict)
_circuit_breaker_lock = threading.Lock()


def circuit_breaker(
    failure_threshold: int = 5,
    recovery_timeout: float = 60.0,
    expected_exception: Union[Type[Exception], Tuple[Type[Exception], ...]] = Exception,
    fallback: Optional[Callable[..., Any]] = None,
    name: Optional[str] = None
):
    """熔断器装饰器
    
    Args:
        failure_threshold: 失败阈值
        recovery_timeout: 恢复超时时间（秒）
        expected_exception: 预期的异常类型
        fallback: 降级函数
        name: 熔断器名称
    """
    def decorator(func: Callable[P, T]) -> Callable[P, T]:
        circuit_name = name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
            with _circuit_breaker_lock:
                if circuit_name not in _circuit_breakers:
                    _circuit_breakers[circuit_name] = {
                        'state': CircuitBreakerState.CLOSED,
                        'stats': CircuitBreakerStats(),
                        'last_failure_time': None
                    }
                
                circuit = _circuit_breakers[circuit_name]
                state = circuit['state']
                stats = circuit['stats']
                
                logger = logging.getLogger(func.__module__)
                
                # 检查熔断器状态
                if state == CircuitBreakerState.OPEN:
                    # 检查是否可以尝试恢复
                    if (circuit['last_failure_time'] and 
                        datetime.now() - circuit['last_failure_time'] > timedelta(seconds=recovery_timeout)):
                        circuit['state'] = CircuitBreakerState.HALF_OPEN
                        logger.info(f"熔断器 {circuit_name} 进入半开状态")
                    else:
                        # 熔断器仍然开启，使用降级函数
                        if fallback:
                            logger.warning(f"熔断器 {circuit_name} 开启，使用降级函数")
                            return fallback(*args, **kwargs)
                        else:
                            raise BambooFallError(
                                message=f"服务 {circuit_name} 暂时不可用（熔断器开启）",
                                error_code=ErrorCode.AI_SERVICE_UNAVAILABLE,
                                severity=ErrorSeverity.HIGH
                            )
                
                # 尝试执行函数
                try:
                    result = func(*args, **kwargs)
                    
                    # 执行成功
                    stats.record_success()
                    
                    if state == CircuitBreakerState.HALF_OPEN:
                        # 半开状态下成功，关闭熔断器
                        circuit['state'] = CircuitBreakerState.CLOSED
                        stats.reset()
                        logger.info(f"熔断器 {circuit_name} 恢复正常，状态变为关闭")
                    
                    return result
                    
                except expected_exception as e:
                    # 执行失败
                    stats.record_failure()
                    circuit['last_failure_time'] = datetime.now()
                    
                    # 检查是否需要开启熔断器
                    if (state == CircuitBreakerState.CLOSED and 
                        stats.failed_requests >= failure_threshold):
                        circuit['state'] = CircuitBreakerState.OPEN
                        logger.error(
                            f"熔断器 {circuit_name} 开启，失败次数: {stats.failed_requests}, "
                            f"失败率: {stats.get_failure_rate():.2%}"
                        )
                    elif state == CircuitBreakerState.HALF_OPEN:
                        # 半开状态下失败，重新开启熔断器
                        circuit['state'] = CircuitBreakerState.OPEN
                        logger.error(f"熔断器 {circuit_name} 半开状态下失败，重新开启")
                    
                    # 如果有降级函数，使用降级函数
                    if fallback and circuit['state'] == CircuitBreakerState.OPEN:
                        logger.warning(f"熔断器 {circuit_name} 开启，使用降级函数")
                        return fallback(*args, **kwargs)
                    
                    raise
        
        return wrapper
    return decorator


def get_circuit_breaker_stats(name: Optional[str] = None) -> Dict[str, Any]:
    """获取熔断器统计信息
    
    Args:
        name: 熔断器名称，如果为None则返回所有熔断器的统计信息
    
    Returns:
        熔断器统计信息字典
    """
    with _circuit_breaker_lock:
        if name:
            if name in _circuit_breakers:
                circuit = _circuit_breakers[name]
                return {
                    'name': name,
                    'state': circuit['state'],
                    'stats': {
                        'total_requests': circuit['stats'].total_requests,
                        'failed_requests': circuit['stats'].failed_requests,
                        'success_requests': circuit['stats'].success_requests,
                        'failure_rate': circuit['stats'].get_failure_rate(),
                        'last_failure_time': circuit['stats'].last_failure_time,
                        'last_success_time': circuit['stats'].last_success_time
                    }
                }
            else:
                return {}
        else:
            result = {}
            for circuit_name, circuit in _circuit_breakers.items():
                result[circuit_name] = {
                    'state': circuit['state'],
                    'stats': {
                        'total_requests': circuit['stats'].total_requests,
                        'failed_requests': circuit['stats'].failed_requests,
                        'success_requests': circuit['stats'].success_requests,
                        'failure_rate': circuit['stats'].get_failure_rate(),
                        'last_failure_time': circuit['stats'].last_failure_time,
                        'last_success_time': circuit['stats'].last_success_time
                    }
                }
            return result


def reset_circuit_breaker(name: str):
    """重置熔断器
    
    Args:
        name: 熔断器名称
    """
    with _circuit_breaker_lock:
        if name in _circuit_breakers:
            circuit = _circuit_breakers[name]
            circuit['state'] = CircuitBreakerState.CLOSED
            circuit['stats'].reset()
            circuit['last_failure_time'] = None
            
            logger = logging.getLogger(__name__)
            logger.info(f"熔断器 {name} 已重置")