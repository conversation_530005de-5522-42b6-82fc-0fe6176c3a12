"""
AI内容生成器

提供高级的AI内容生成功能，整合模板、上下文和服务调用
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable, AsyncGenerator
from dataclasses import dataclass
from datetime import datetime
import uuid

from .ai_service_base import AIMessage, AIResponse, MessageRole
from .ai_service_manager import get_ai_service_manager
from .prompt_manager import get_prompt_manager
from ..models.project import Project
from ..models.character import Character
from ..models.chapter import Chapter
from ..utils.logger import LoggerMixin


@dataclass
class GenerationRequest:
    """内容生成请求"""
    id: str
    type: str  # 生成类型：character, plot, dialogue, scene, etc.
    template_id: str
    parameters: Dict[str, Any]
    context: Optional[Dict[str, Any]] = None
    provider: Optional[str] = None
    model: Optional[str] = None
    stream: bool = False
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class GenerationResult:
    """内容生成结果"""
    request_id: str
    content: str
    metadata: Dict[str, Any]
    success: bool
    error_message: Optional[str] = None
    response_time: float = 0.0
    token_usage: Optional[Dict[str, int]] = None


class ContentGenerator(LoggerMixin):
    """AI内容生成器"""
    
    def __init__(self):
        self.ai_manager = get_ai_service_manager()
        self.prompt_manager = get_prompt_manager()
        self.generation_history: List[GenerationResult] = []
        
        self.logger.info("AI内容生成器初始化完成")
    
    async def generate_character(
        self,
        character_name: str,
        age: int,
        gender: str,
        occupation: str,
        genre: str = "现代都市",
        provider: Optional[str] = None,
        **kwargs
    ) -> GenerationResult:
        """生成角色信息"""
        request = GenerationRequest(
            id=str(uuid.uuid4()),
            type="character",
            template_id="novel_character_creation",
            parameters={
                "character_name": character_name,
                "age": age,
                "gender": gender,
                "occupation": occupation,
                "genre": genre
            },
            provider=provider,
            **kwargs
        )
        
        return await self._execute_generation(request)
    
    async def generate_plot_development(
        self,
        current_situation: str,
        main_characters: str,
        story_background: str,
        provider: Optional[str] = None,
        **kwargs
    ) -> GenerationResult:
        """生成情节发展"""
        request = GenerationRequest(
            id=str(uuid.uuid4()),
            type="plot",
            template_id="plot_development",
            parameters={
                "current_situation": current_situation,
                "main_characters": main_characters,
                "story_background": story_background
            },
            provider=provider,
            **kwargs
        )
        
        return await self._execute_generation(request)
    
    async def enhance_dialogue(
        self,
        scene_context: str,
        characters: List[Dict[str, str]],
        original_dialogue: str,
        provider: Optional[str] = None,
        **kwargs
    ) -> GenerationResult:
        """优化对话"""
        request = GenerationRequest(
            id=str(uuid.uuid4()),
            type="dialogue",
            template_id="dialogue_enhancement",
            parameters={
                "scene_context": scene_context,
                "characters": characters,
                "original_dialogue": original_dialogue
            },
            provider=provider,
            **kwargs
        )
        
        return await self._execute_generation(request)
    
    async def generate_scene_description(
        self,
        scene_setting: str,
        time_context: str,
        atmosphere: str,
        characters_present: str,
        scene_purpose: str,
        genre: str = "现代都市",
        word_count: int = 300,
        provider: Optional[str] = None,
        **kwargs
    ) -> GenerationResult:
        """生成场景描写"""
        request = GenerationRequest(
            id=str(uuid.uuid4()),
            type="scene",
            template_id="scene_description",
            parameters={
                "scene_setting": scene_setting,
                "time_context": time_context,
                "atmosphere": atmosphere,
                "characters_present": characters_present,
                "scene_purpose": scene_purpose,
                "genre": genre,
                "word_count": word_count
            },
            provider=provider,
            **kwargs
        )
        
        return await self._execute_generation(request)
    
    async def polish_text(
        self,
        original_text: str,
        target_audience: str = "成年读者",
        provider: Optional[str] = None,
        **kwargs
    ) -> GenerationResult:
        """文本润色"""
        request = GenerationRequest(
            id=str(uuid.uuid4()),
            type="polish",
            template_id="text_polish",
            parameters={
                "original_text": original_text,
                "target_audience": target_audience
            },
            provider=provider,
            **kwargs
        )
        
        return await self._execute_generation(request)
    
    async def expand_content(
        self,
        base_content: str,
        expansion_direction: str,
        target_word_count: int,
        writing_style: str = "叙述性",
        provider: Optional[str] = None,
        **kwargs
    ) -> GenerationResult:
        """内容扩展"""
        request = GenerationRequest(
            id=str(uuid.uuid4()),
            type="expansion",
            template_id="content_expansion",
            parameters={
                "base_content": base_content,
                "expansion_direction": expansion_direction,
                "target_word_count": target_word_count,
                "writing_style": writing_style
            },
            provider=provider,
            **kwargs
        )
        
        return await self._execute_generation(request)
    
    async def generate_story_bible_summary(
        self,
        project: Project,
        characters: List[Character],
        provider: Optional[str] = None,
        **kwargs
    ) -> GenerationResult:
        """生成故事圣经总结"""
        # 准备角色信息
        character_info = []
        for char in characters:
            character_info.append(f"- {char.name}: {char.description}")
        
        request = GenerationRequest(
            id=str(uuid.uuid4()),
            type="story_bible",
            template_id="story_bible_summary",
            parameters={
                "title": project.name,
                "genre": project.genre,
                "target_audience": project.target_audience or "一般读者",
                "core_settings": project.description,
                "main_characters": "\n".join(character_info),
                "world_building": project.world_building or "现实世界背景"
            },
            context={
                "project_id": project.id,
                "character_count": len(characters)
            },
            provider=provider,
            **kwargs
        )
        
        return await self._execute_generation(request)
    
    async def stream_generate_content(
        self,
        template_id: str,
        parameters: Dict[str, Any],
        provider: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式生成内容"""
        try:
            # 准备消息
            messages = self.prompt_manager.create_messages_from_template(
                template_id, **parameters
            )
            
            # 流式生成
            async for chunk in self.ai_manager.stream_chat(
                messages, provider=provider, **kwargs
            ):
                yield chunk
                
        except Exception as e:
            self.logger.error(f"流式内容生成失败: {e}")
            yield f"生成失败: {str(e)}"
    
    async def generate_with_custom_prompt(
        self,
        prompt: str,
        system_message: Optional[str] = None,
        provider: Optional[str] = None,
        **kwargs
    ) -> GenerationResult:
        """使用自定义提示词生成内容"""
        request_id = str(uuid.uuid4())
        
        try:
            messages = []
            if system_message:
                messages.append(AIMessage(
                    role=MessageRole.SYSTEM,
                    content=system_message
                ))
            
            messages.append(AIMessage(
                role=MessageRole.USER,
                content=prompt
            ))
            
            # 发送请求
            response = await self.ai_manager.chat(
                messages, provider=provider, **kwargs
            )
            
            result = GenerationResult(
                request_id=request_id,
                content=response.content,
                metadata=response.metadata,
                success=True,
                response_time=response.response_time,
                token_usage=response.usage
            )
            
            self._record_result(result)
            return result
            
        except Exception as e:
            error_result = GenerationResult(
                request_id=request_id,
                content="",
                metadata={},
                success=False,
                error_message=str(e)
            )
            
            self._record_result(error_result)
            self.logger.error(f"自定义提示词生成失败: {e}")
            return error_result
    
    async def _execute_generation(self, request: GenerationRequest) -> GenerationResult:
        """执行内容生成"""
        start_time = datetime.now()
        
        try:
            # 准备消息
            messages = self.prompt_manager.create_messages_from_template(
                request.template_id, **request.parameters
            )
            
            # 添加上下文信息（如果有）
            if request.context:
                context_info = self._format_context(request.context)
                if context_info:
                    messages.insert(0, AIMessage(
                        role=MessageRole.SYSTEM,
                        content=context_info
                    ))
            
            # 发送请求
            if request.stream:
                # 流式处理（这里返回第一个结果）
                content_chunks = []
                async for chunk in self.ai_manager.stream_chat(
                    messages, provider=request.provider, model=request.model
                ):
                    content_chunks.append(chunk)
                
                content = "".join(content_chunks)
                response_time = (datetime.now() - start_time).total_seconds()
                
                result = GenerationResult(
                    request_id=request.id,
                    content=content,
                    metadata={"type": request.type, "template": request.template_id},
                    success=True,
                    response_time=response_time
                )
            else:
                # 普通请求
                response = await self.ai_manager.chat(
                    messages, provider=request.provider, model=request.model
                )
                
                result = GenerationResult(
                    request_id=request.id,
                    content=response.content,
                    metadata={
                        "type": request.type,
                        "template": request.template_id,
                        **response.metadata
                    },
                    success=True,
                    response_time=response.response_time,
                    token_usage=response.usage
                )
            
            self._record_result(result)
            self.logger.info(f"内容生成成功: {request.type}, 耗时: {result.response_time:.2f}s")
            return result
            
        except Exception as e:
            error_result = GenerationResult(
                request_id=request.id,
                content="",
                metadata={"type": request.type, "template": request.template_id},
                success=False,
                error_message=str(e),
                response_time=(datetime.now() - start_time).total_seconds()
            )
            
            self._record_result(error_result)
            self.logger.error(f"内容生成失败: {e}")
            return error_result
    
    def _format_context(self, context: Dict[str, Any]) -> str:
        """格式化上下文信息"""
        if not context:
            return ""
        
        context_parts = []
        for key, value in context.items():
            if value is not None:
                context_parts.append(f"{key}: {value}")
        
        if context_parts:
            return f"上下文信息：\n{chr(10).join(context_parts)}\n\n请基于以上上下文生成内容。"
        
        return ""
    
    def _record_result(self, result: GenerationResult):
        """记录生成结果"""
        self.generation_history.append(result)
        
        # 保持历史记录不超过1000条
        if len(self.generation_history) > 1000:
            self.generation_history = self.generation_history[-1000:]
    
    def get_generation_history(
        self, 
        limit: int = 100,
        content_type: Optional[str] = None
    ) -> List[GenerationResult]:
        """获取生成历史"""
        history = self.generation_history
        
        if content_type:
            history = [r for r in history if r.metadata.get("type") == content_type]
        
        return history[-limit:]
    
    def get_usage_statistics(self) -> Dict[str, Any]:
        """获取使用统计"""
        successful_generations = [r for r in self.generation_history if r.success]
        
        total_tokens = sum(
            result.token_usage.get("total_tokens", 0) 
            for result in successful_generations 
            if result.token_usage
        )
        
        avg_response_time = sum(
            result.response_time for result in successful_generations
        ) / len(successful_generations) if successful_generations else 0
        
        type_counts = {}
        for result in self.generation_history:
            content_type = result.metadata.get("type", "unknown")
            type_counts[content_type] = type_counts.get(content_type, 0) + 1
        
        return {
            "total_generations": len(self.generation_history),
            "successful_generations": len(successful_generations),
            "failed_generations": len(self.generation_history) - len(successful_generations),
            "success_rate": len(successful_generations) / len(self.generation_history) * 100 if self.generation_history else 0,
            "total_tokens_used": total_tokens,
            "average_response_time": avg_response_time,
            "generation_types": type_counts
        }


# 全局内容生成器实例
_content_generator: Optional[ContentGenerator] = None


def get_content_generator() -> ContentGenerator:
    """获取全局内容生成器实例"""
    global _content_generator
    if _content_generator is None:
        _content_generator = ContentGenerator()
    return _content_generator