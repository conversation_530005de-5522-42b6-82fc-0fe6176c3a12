# -*- coding: utf-8 -*-
"""
Pytest配置文件

定义测试的全局配置、fixtures和工具函数
"""

import pytest
import sys
import os
from pathlib import Path
from unittest.mock import Mock, patch

# 添加项目路径到sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))


@pytest.fixture(scope="session")
def project_root_path():
    """项目根目录路径"""
    return project_root


@pytest.fixture(scope="session")
def test_data_dir():
    """测试数据目录"""
    return Path(__file__).parent / "data"


@pytest.fixture
def mock_config():
    """模拟配置对象"""
    from bamboofall.ai.ai_service_base import AIConfig
    return AIConfig(
        api_key="test-api-key",
        model="test-model",
        max_tokens=1000,
        temperature=0.7,
        timeout=30
    )


@pytest.fixture
def mock_ai_response():
    """模拟AI响应对象"""
    from bamboofall.ai.ai_service_base import AIResponse
    return AIResponse(
        content="这是一个测试响应",
        model="test-model",
        usage={"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30},
        finish_reason="stop"
    )


@pytest.fixture
def mock_ai_messages():
    """模拟AI消息列表"""
    from bamboofall.ai.ai_service_base import AIMessage, MessageRole
    return [
        AIMessage(role=MessageRole.SYSTEM, content="你是一个有用的助手。"),
        AIMessage(role=MessageRole.USER, content="你好！"),
        AIMessage(role=MessageRole.ASSISTANT, content="你好！我是你的AI助手，有什么可以帮助你的吗？")
    ]


@pytest.fixture
def mock_project():
    """模拟项目对象"""
    from bamboofall.models.project import Project, ProjectStatus, ProjectPriority
    return Project(
        id="test-project-id",
        name="测试项目",
        description="这是一个测试项目",
        status=ProjectStatus.ACTIVE,
        priority=ProjectPriority.MEDIUM,
        path="/test/path"
    )


@pytest.fixture
def mock_character():
    """模拟角色对象"""
    from bamboofall.models.character import Character
    return Character(
        id="test-character-id",
        name="测试角色",
        description="这是一个测试角色",
        personality="友善、聪明",
        background="来自测试世界"
    )


@pytest.fixture
def temp_db_path(tmp_path):
    """临时数据库路径"""
    return tmp_path / "test.db"


@pytest.fixture
def mock_config_manager():
    """模拟配置管理器"""
    with patch('bamboofall.utils.config_utils.get_config_manager') as mock:
        config_manager = Mock()
        config_manager.config.ai.max_tokens = 1000
        config_manager.config.ai.temperature = 0.7
        config_manager.config.ai.timeout = 30
        config_manager.get_ai_api_key.return_value = None
        mock.return_value = config_manager
        yield config_manager


@pytest.fixture
def mock_anthropic_client():
    """模拟Anthropic客户端"""
    with patch('anthropic.Anthropic') as mock_client:
        client = Mock()
        
        # 模拟同步聊天响应
        mock_response = Mock()
        mock_response.content = [Mock(text="测试响应内容")]
        mock_response.model = "claude-3-sonnet-20240229"
        mock_response.usage = Mock(
            input_tokens=10,
            output_tokens=20
        )
        mock_response.stop_reason = "end_turn"
        
        client.messages.create.return_value = mock_response
        
        # 模拟流式响应
        def mock_stream():
            yield Mock(delta=Mock(text="测试"))
            yield Mock(delta=Mock(text="流式"))
            yield Mock(delta=Mock(text="响应"))
        
        client.messages.stream.return_value = mock_stream()
        
        mock_client.return_value = client
        yield client


@pytest.fixture
def mock_openai_client():
    """模拟OpenAI客户端"""
    with patch('openai.OpenAI') as mock_client:
        client = Mock()
        
        # 模拟聊天响应
        mock_response = Mock()
        mock_response.choices = [Mock(
            message=Mock(content="测试响应内容"),
            finish_reason="stop"
        )]
        mock_response.model = "gpt-3.5-turbo"
        mock_response.usage = Mock(
            prompt_tokens=10,
            completion_tokens=20,
            total_tokens=30
        )
        
        client.chat.completions.create.return_value = mock_response
        
        mock_client.return_value = client
        yield client


# 测试标记
pytest_plugins = []


def pytest_configure(config):
    """Pytest配置"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "e2e: 端到端测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
    config.addinivalue_line(
        "markers", "api: API测试"
    )
    config.addinivalue_line(
        "markers", "ui: UI测试"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    # 为没有标记的测试添加unit标记
    for item in items:
        if not any(item.iter_markers()):
            item.add_marker(pytest.mark.unit)