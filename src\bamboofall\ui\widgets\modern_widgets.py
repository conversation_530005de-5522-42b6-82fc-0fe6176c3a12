"""
现代化控件库

提供美观的自定义控件
"""

from PyQt6.QtWidgets import (
    QPushButton, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QFrame, QGraphicsDropShadowEffect, QTextEdit, QListWidget,
    QListWidgetItem, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect, QTimer
from PyQt6.QtGui import QPainter, QColor, QFont, QPen, QBrush, QPalette
from typing import Optional, List
import logging

from ..themes.theme_manager import get_theme_manager

logger = logging.getLogger(__name__)


class ModernButton(QPushButton):
    """现代化按钮控件"""
    
    def __init__(self, text: str = "", button_type: str = "primary", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.theme_manager = get_theme_manager()
        
        self.setup_style()
        self.setup_animation()
        
        # 连接主题变化信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_style(self):
        """设置按钮样式"""
        colors = self.theme_manager.current_colors
        fonts = self.theme_manager.FONTS
        
        base_style = f"""
            QPushButton {{
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: 500;
                font-size: 14px;
                font-family: {fonts['ui']};
                min-height: 20px;
            }}
        """
        
        if self.button_type == "primary":
            color_style = f"""
                QPushButton {{
                    background-color: {colors['primary']};
                    color: white;
                }}
                QPushButton:hover {{
                    background-color: {colors['primary_hover']};
                }}
            """
        elif self.button_type == "secondary":
            color_style = f"""
                QPushButton {{
                    background-color: {colors['secondary']};
                    color: white;
                }}
                QPushButton:hover {{
                    background-color: {colors['secondary_hover']};
                }}
            """
        elif self.button_type == "outline":
            color_style = f"""
                QPushButton {{
                    background-color: transparent;
                    color: {colors['primary']};
                    border: 1px solid {colors['primary']};
                }}
                QPushButton:hover {{
                    background-color: {colors['primary']};
                    color: white;
                }}
            """
        else:
            color_style = ""
        
        self.setStyleSheet(base_style + color_style)
    
    def setup_animation(self):
        """设置动画效果"""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(150)
        self.animation.setEasingCurve(QEasingCurve.Type.OutCubic)
    
    def on_theme_changed(self):
        """主题变化时更新样式"""
        self.setup_style()


class ProjectCardWidget(QFrame):
    """项目卡片控件"""
    
    clicked = pyqtSignal(str)  # 发送项目ID
    
    def __init__(self, project_data, parent=None):
        super().__init__(parent)
        self.project_data = project_data
        self.theme_manager = get_theme_manager()
        
        self.setup_ui()
        self.setup_style()
        self.setup_effects()
        
        # 连接主题变化信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_ui(self):
        """设置UI布局"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)
        
        # 项目标题
        self.title_label = QLabel(self.project_data.get("name", "未命名项目"))
        self.title_label.setWordWrap(True)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        layout.addWidget(self.title_label)
        
        # 项目描述
        description = self.project_data.get("description", "暂无描述")
        if len(description) > 100:
            description = description[:97] + "..."
        
        self.desc_label = QLabel(description)
        self.desc_label.setWordWrap(True)
        self.desc_label.setMaximumHeight(60)
        layout.addWidget(self.desc_label)
        
        # 项目信息
        info_layout = QHBoxLayout()
        
        # 类型标签
        self.type_label = QLabel(self.project_data.get("type", "unknown"))
        self.type_label.setStyleSheet("""
            QLabel {
                background-color: #E3F2FD;
                color: #1976D2;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
                font-weight: 500;
            }
        """)
        info_layout.addWidget(self.type_label)
        
        info_layout.addStretch()
        
        # 字数统计
        word_count = self.project_data.get("word_count", 0)
        self.word_count_label = QLabel(f"{word_count:,} 字")
        info_layout.addWidget(self.word_count_label)
        
        layout.addLayout(info_layout)
        
        # 最后修改时间
        updated_at = self.project_data.get("updated_at", "")
        if updated_at:
            self.time_label = QLabel(f"更新于 {updated_at}")
            time_font = QFont()
            time_font.setPointSize(10)
            self.time_label.setFont(time_font)
            layout.addWidget(self.time_label)
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        self.setStyleSheet(f"""
            ProjectCardWidget {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 12px;
            }}
            ProjectCardWidget:hover {{
                border-color: {colors['primary']};
            }}
        """)
        
        # 设置标签颜色
        self.title_label.setStyleSheet(f"color: {colors['text_primary']};")
        self.desc_label.setStyleSheet(f"color: {colors['text_secondary']};")
        self.word_count_label.setStyleSheet(f"color: {colors['text_secondary']};")
        
        if hasattr(self, 'time_label'):
            self.time_label.setStyleSheet(f"color: {colors['text_disabled']};")
    
    def setup_effects(self):
        """设置视觉效果"""
        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            project_id = self.project_data.get("id", "")
            if project_id:
                self.clicked.emit(project_id)
        super().mousePressEvent(event)
    
    def on_theme_changed(self):
        """主题变化时更新样式"""
        self.setup_style()


class ModernProgressBar(QProgressBar):
    """现代化进度条"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.setup_style()
        
        # 连接主题变化信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        self.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {colors['border']};
                border-radius: 6px;
                background-color: {colors['surface']};
                text-align: center;
                color: {colors['text_primary']};
                font-weight: 500;
                height: 20px;
            }}
            
            QProgressBar::chunk {{
                background-color: {colors['primary']};
                border-radius: 5px;
                margin: 1px;
            }}
        """)
    
    def on_theme_changed(self):
        """主题变化时更新样式"""
        self.setup_style()


class ModernTextEdit(QTextEdit):
    """现代化文本编辑器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.setup_style()
        self.setup_font()
        
        # 连接主题变化信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        self.setStyleSheet(f"""
            QTextEdit {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 8px;
                padding: 16px;
                color: {colors['text_primary']};
                selection-background-color: {colors['primary']};
                selection-color: white;
            }}
            
            QTextEdit:focus {{
                border-color: {colors['primary']};
                border-width: 2px;
            }}
        """)
    
    def setup_font(self):
        """设置字体"""
        font = QFont(self.theme_manager.get_font("editor"))
        font.setPointSize(14)
        self.setFont(font)
    
    def on_theme_changed(self):
        """主题变化时更新样式"""
        self.setup_style()


class NotificationWidget(QFrame):
    """通知组件"""
    
    def __init__(self, message: str, notification_type: str = "info", parent=None):
        super().__init__(parent)
        self.message = message
        self.notification_type = notification_type
        self.theme_manager = get_theme_manager()
        
        self.setup_ui()
        self.setup_style()
        self.setup_animation()
        
        # 自动隐藏定时器
        self.hide_timer = QTimer()
        self.hide_timer.timeout.connect(self.hide_notification)
        self.hide_timer.setSingleShot(True)
    
    def setup_ui(self):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)
        
        # 消息文本
        self.message_label = QLabel(self.message)
        self.message_label.setWordWrap(True)
        layout.addWidget(self.message_label)
        
        # 关闭按钮
        self.close_button = QPushButton("×")
        self.close_button.setFixedSize(20, 20)
        self.close_button.clicked.connect(self.hide_notification)
        layout.addWidget(self.close_button)
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        type_colors = {
            "info": colors['primary'],
            "success": colors['success'],
            "warning": colors['warning'],
            "error": colors['error']
        }
        
        bg_color = type_colors.get(self.notification_type, colors['primary'])
        
        self.setStyleSheet(f"""
            NotificationWidget {{
                background-color: {bg_color};
                border-radius: 6px;
                color: white;
            }}
        """)
        
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                color: white;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 10px;
            }
        """)
    
    def setup_animation(self):
        """设置动画"""
        self.slide_animation = QPropertyAnimation(self, b"geometry")
        self.slide_animation.setDuration(300)
        self.slide_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
    
    def show_notification(self, duration: int = 5000):
        """显示通知"""
        self.show()
        if duration > 0:
            self.hide_timer.start(duration)
    
    def hide_notification(self):
        """隐藏通知"""
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        self.fade_animation.finished.connect(self.hide)
        self.fade_animation.start()


class ModernListWidget(QListWidget):
    """现代化列表控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.setup_style()
        
        # 连接主题变化信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        self.setStyleSheet(f"""
            QListWidget {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 6px;
                color: {colors['text_primary']};
                font-family: {self.theme_manager.get_font('ui')};
                outline: none;
            }}
            
            QListWidget::item {{
                padding: 12px;
                border-bottom: 1px solid {colors['border']};
            }}
            
            QListWidget::item:hover {{
                background-color: {colors['surface_hover']};
            }}
            
            QListWidget::item:selected {{
                background-color: {colors['primary']};
                color: white;
            }}
            
            QListWidget::item:last {{
                border-bottom: none;
            }}
        """)
    
    def on_theme_changed(self):
        """主题变化时更新样式"""
        self.setup_style()


class StatusIndicator(QWidget):
    """状态指示器"""
    
    def __init__(self, status: str = "idle", parent=None):
        super().__init__(parent)
        self.status = status
        self.setFixedSize(12, 12)
    
    def set_status(self, status: str):
        """设置状态"""
        self.status = status
        self.update()
    
    def paintEvent(self, event):
        """绘制状态指示器"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        colors = {
            "idle": "#9E9E9E",      # 灰色
            "active": "#4CAF50",    # 绿色
            "busy": "#FF9800",      # 橙色
            "error": "#F44336",     # 红色
            "warning": "#FFC107"    # 黄色
        }
        
        color = colors.get(self.status, colors["idle"])
        
        painter.setBrush(QBrush(QColor(color)))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(2, 2, 8, 8)


# 工具函数
def create_modern_button(text: str, button_type: str = "primary", parent=None) -> ModernButton:
    """创建现代化按钮"""
    return ModernButton(text, button_type, parent)


def create_project_card(project_data, parent=None) -> ProjectCardWidget:
    """创建项目卡片"""
    return ProjectCardWidget(project_data, parent)


def create_notification(message: str, notification_type: str = "info", parent=None) -> NotificationWidget:
    """创建通知组件"""
    return NotificationWidget(message, notification_type, parent)