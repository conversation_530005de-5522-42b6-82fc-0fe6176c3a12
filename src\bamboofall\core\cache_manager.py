# -*- coding: utf-8 -*-
"""
缓存管理系统

提供多级缓存机制，包括内存缓存、磁盘缓存和AI结果缓存
"""

import logging
import json
import pickle
import hashlib
import time
import threading
from typing import Any, Dict, Optional, List, Callable, Union
from datetime import datetime, timedelta
from pathlib import Path
from functools import wraps, lru_cache
from dataclasses import dataclass, field
from collections import OrderedDict
import weakref

from ..utils.file_utils import FileUtils
from ..exceptions.exceptions import CacheError

logger = logging.getLogger(__name__)


@dataclass
class CacheStats:
    """缓存统计信息"""
    memory_hits: int = 0
    memory_misses: int = 0
    disk_hits: int = 0
    disk_misses: int = 0
    total_requests: int = 0
    cache_size: int = 0
    last_cleanup: datetime = field(default_factory=datetime.now)
    
    @property
    def hit_rate(self) -> float:
        """缓存命中率"""
        if self.total_requests == 0:
            return 0.0
        return (self.memory_hits + self.disk_hits) / self.total_requests
        
    @property
    def memory_hit_rate(self) -> float:
        """内存缓存命中率"""
        total_memory_requests = self.memory_hits + self.memory_misses
        if total_memory_requests == 0:
            return 0.0
        return self.memory_hits / total_memory_requests
        
    def record_hit(self, cache_type: str):
        """记录缓存命中"""
        self.total_requests += 1
        if cache_type == 'memory':
            self.memory_hits += 1
        elif cache_type == 'disk':
            self.disk_hits += 1
            
    def record_miss(self):
        """记录缓存未命中"""
        self.total_requests += 1
        self.memory_misses += 1
        self.disk_misses += 1
        
    def reset(self):
        """重置统计"""
        self.memory_hits = 0
        self.memory_misses = 0
        self.disk_hits = 0
        self.disk_misses = 0
        self.total_requests = 0
        self.cache_size = 0
        self.last_cleanup = datetime.now()


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime] = None
    access_count: int = 0
    last_accessed: datetime = field(default_factory=datetime.now)
    size: int = 0
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at
        
    def access(self):
        """记录访问"""
        self.access_count += 1
        self.last_accessed = datetime.now()
        
    def calculate_size(self) -> int:
        """计算条目大小"""
        try:
            if isinstance(self.value, (str, bytes)):
                self.size = len(self.value)
            else:
                # 使用pickle估算大小
                self.size = len(pickle.dumps(self.value))
        except Exception:
            self.size = 1024  # 默认1KB
        return self.size


class MemoryCache:
    """内存缓存"""
    
    def __init__(self, max_size: int = 1000, max_memory: int = 100 * 1024 * 1024):
        self.max_size = max_size  # 最大条目数
        self.max_memory = max_memory  # 最大内存使用（字节）
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        self._current_memory = 0
        
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            entry = self._cache.get(key)
            if entry is None:
                return None
                
            if entry.is_expired():
                self._remove_entry(key)
                return None
                
            # 更新访问信息并移到末尾（LRU）
            entry.access()
            self._cache.move_to_end(key)
            return entry.value
            
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        with self._lock:
            # 创建缓存条目
            expires_at = None
            if ttl is not None:
                expires_at = datetime.now() + timedelta(seconds=ttl)
                
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(),
                expires_at=expires_at
            )
            entry.calculate_size()
            
            # 检查是否需要清理空间
            if key in self._cache:
                old_entry = self._cache[key]
                self._current_memory -= old_entry.size
                
            # 确保有足够空间
            while (len(self._cache) >= self.max_size or 
                   self._current_memory + entry.size > self.max_memory):
                if not self._evict_lru():
                    return False  # 无法腾出空间
                    
            # 添加新条目
            self._cache[key] = entry
            self._current_memory += entry.size
            return True
            
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self._lock:
            return self._remove_entry(key)
            
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._current_memory = 0
            
    def _remove_entry(self, key: str) -> bool:
        """移除缓存条目"""
        entry = self._cache.pop(key, None)
        if entry:
            self._current_memory -= entry.size
            return True
        return False
        
    def _evict_lru(self) -> bool:
        """淘汰最近最少使用的条目"""
        if not self._cache:
            return False
            
        # 移除最旧的条目（OrderedDict的第一个）
        key, entry = self._cache.popitem(last=False)
        self._current_memory -= entry.size
        logger.debug(f"淘汰缓存条目: {key}")
        return True
        
    def cleanup_expired(self) -> int:
        """清理过期条目"""
        with self._lock:
            expired_keys = []
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
                    
            for key in expired_keys:
                self._remove_entry(key)
                
            return len(expired_keys)
            
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'memory_usage': self._current_memory,
                'max_memory': self.max_memory,
                'memory_usage_percent': (self._current_memory / self.max_memory) * 100
            }


class DiskCache:
    """磁盘缓存"""
    
    def __init__(self, cache_dir: str, max_size: int = 1000):
        self.cache_dir = Path(cache_dir)
        self.max_size = max_size
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self._index_file = self.cache_dir / 'cache_index.json'
        self._lock = threading.RLock()
        self._index = self._load_index()
        
    def _load_index(self) -> Dict[str, Dict[str, Any]]:
        """加载缓存索引"""
        if self._index_file.exists():
            try:
                with open(self._index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                    # 转换时间字符串为datetime对象
                    for key, info in index_data.items():
                        if 'created_at' in info:
                            info['created_at'] = datetime.fromisoformat(info['created_at'])
                        if 'expires_at' in info and info['expires_at']:
                            info['expires_at'] = datetime.fromisoformat(info['expires_at'])
                    return index_data
            except Exception as e:
                logger.warning(f"加载缓存索引失败: {e}")
        return {}
        
    def _save_index(self):
        """保存缓存索引"""
        try:
            # 转换datetime对象为字符串
            index_data = {}
            for key, info in self._index.items():
                info_copy = info.copy()
                if 'created_at' in info_copy:
                    info_copy['created_at'] = info_copy['created_at'].isoformat()
                if 'expires_at' in info_copy and info_copy['expires_at']:
                    info_copy['expires_at'] = info_copy['expires_at'].isoformat()
                index_data[key] = info_copy
                
            with open(self._index_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存缓存索引失败: {e}")
            
    def _get_cache_file(self, key: str) -> Path:
        """获取缓存文件路径"""
        # 使用MD5哈希作为文件名
        hash_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{hash_key}.cache"
        
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._index:
                return None
                
            info = self._index[key]
            
            # 检查是否过期
            if info.get('expires_at') and datetime.now() > info['expires_at']:
                self.delete(key)
                return None
                
            cache_file = self._get_cache_file(key)
            if not cache_file.exists():
                # 文件不存在，清理索引
                del self._index[key]
                self._save_index()
                return None
                
            try:
                with open(cache_file, 'rb') as f:
                    value = pickle.load(f)
                    
                # 更新访问信息
                info['access_count'] = info.get('access_count', 0) + 1
                info['last_accessed'] = datetime.now()
                self._save_index()
                
                return value
            except Exception as e:
                logger.error(f"读取缓存文件失败: {e}")
                self.delete(key)
                return None
                
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        with self._lock:
            try:
                # 确保有足够空间
                while len(self._index) >= self.max_size:
                    if not self._evict_lru():
                        return False
                        
                cache_file = self._get_cache_file(key)
                
                # 保存数据到文件
                with open(cache_file, 'wb') as f:
                    pickle.dump(value, f)
                    
                # 更新索引
                expires_at = None
                if ttl is not None:
                    expires_at = datetime.now() + timedelta(seconds=ttl)
                    
                self._index[key] = {
                    'created_at': datetime.now(),
                    'expires_at': expires_at,
                    'access_count': 0,
                    'last_accessed': datetime.now(),
                    'file_size': cache_file.stat().st_size
                }
                
                self._save_index()
                return True
                
            except Exception as e:
                logger.error(f"保存缓存文件失败: {e}")
                return False
                
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        with self._lock:
            if key not in self._index:
                return False
                
            cache_file = self._get_cache_file(key)
            try:
                if cache_file.exists():
                    cache_file.unlink()
                del self._index[key]
                self._save_index()
                return True
            except Exception as e:
                logger.error(f"删除缓存文件失败: {e}")
                return False
                
    def clear(self):
        """清空缓存"""
        with self._lock:
            try:
                # 删除所有缓存文件
                for cache_file in self.cache_dir.glob('*.cache'):
                    cache_file.unlink()
                    
                self._index.clear()
                self._save_index()
            except Exception as e:
                logger.error(f"清空缓存失败: {e}")
                
    def _evict_lru(self) -> bool:
        """淘汰最近最少使用的条目"""
        if not self._index:
            return False
            
        # 找到最旧的条目
        oldest_key = min(self._index.keys(), 
                        key=lambda k: self._index[k].get('last_accessed', datetime.min))
        
        self.delete(oldest_key)
        logger.debug(f"淘汰磁盘缓存条目: {oldest_key}")
        return True
        
    def cleanup_expired(self) -> int:
        """清理过期条目"""
        with self._lock:
            expired_keys = []
            now = datetime.now()
            
            for key, info in self._index.items():
                if info.get('expires_at') and now > info['expires_at']:
                    expired_keys.append(key)
                    
            for key in expired_keys:
                self.delete(key)
                
            return len(expired_keys)
            
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            total_size = sum(info.get('file_size', 0) for info in self._index.values())
            return {
                'size': len(self._index),
                'max_size': self.max_size,
                'total_file_size': total_size,
                'cache_dir': str(self.cache_dir)
            }


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, cache_dir: str = None, 
                 memory_max_size: int = 1000,
                 memory_max_memory: int = 100 * 1024 * 1024,
                 disk_max_size: int = 10000):
        
        if cache_dir is None:
            cache_dir = FileUtils.get_app_data_dir() / 'cache'
            
        self.memory_cache = MemoryCache(memory_max_size, memory_max_memory)
        self.disk_cache = DiskCache(str(cache_dir), disk_max_size)
        self.stats = CacheStats()
        self._cleanup_thread = None
        self._cleanup_interval = 300  # 5分钟
        self._start_cleanup_thread()
        
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        # 先查内存缓存
        value = self.memory_cache.get(key)
        if value is not None:
            self.stats.record_hit('memory')
            return value
            
        # 再查磁盘缓存
        value = self.disk_cache.get(key)
        if value is not None:
            self.stats.record_hit('disk')
            # 提升到内存缓存
            self.memory_cache.set(key, value)
            return value
            
        self.stats.record_miss()
        return default
        
    def set(self, key: str, value: Any, ttl: Optional[int] = None, 
            memory_only: bool = False) -> bool:
        """设置缓存值"""
        # 设置内存缓存
        memory_success = self.memory_cache.set(key, value, ttl)
        
        # 如果不是仅内存模式，也设置磁盘缓存
        disk_success = True
        if not memory_only:
            disk_success = self.disk_cache.set(key, value, ttl)
            
        return memory_success or disk_success
        
    def delete(self, key: str) -> bool:
        """删除缓存条目"""
        memory_deleted = self.memory_cache.delete(key)
        disk_deleted = self.disk_cache.delete(key)
        return memory_deleted or disk_deleted
        
    def clear(self):
        """清空所有缓存"""
        self.memory_cache.clear()
        self.disk_cache.clear()
        self.stats.reset()
        
    def invalidate_pattern(self, pattern: str):
        """按模式失效缓存"""
        import re
        regex = re.compile(pattern)
        
        # 收集匹配的键
        keys_to_remove = []
        
        # 检查内存缓存
        for key in list(self.memory_cache._cache.keys()):
            if regex.match(key):
                keys_to_remove.append(key)
                
        # 检查磁盘缓存
        for key in list(self.disk_cache._index.keys()):
            if regex.match(key):
                keys_to_remove.append(key)
                
        # 删除匹配的键
        for key in set(keys_to_remove):
            self.delete(key)
            
        logger.info(f"按模式 '{pattern}' 失效了 {len(set(keys_to_remove))} 个缓存条目")
        
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        memory_stats = self.memory_cache.get_stats()
        disk_stats = self.disk_cache.get_stats()
        
        return {
            'hit_rate': self.stats.hit_rate,
            'memory_hit_rate': self.stats.memory_hit_rate,
            'total_requests': self.stats.total_requests,
            'memory_cache': memory_stats,
            'disk_cache': disk_stats,
            'last_cleanup': self.stats.last_cleanup.isoformat()
        }
        
    def _start_cleanup_thread(self):
        """启动清理线程"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(self._cleanup_interval)
                    self._cleanup_expired()
                except Exception as e:
                    logger.error(f"缓存清理线程错误: {e}")
                    
        self._cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        self._cleanup_thread.start()
        
    def _cleanup_expired(self):
        """清理过期条目"""
        memory_cleaned = self.memory_cache.cleanup_expired()
        disk_cleaned = self.disk_cache.cleanup_expired()
        
        if memory_cleaned > 0 or disk_cleaned > 0:
            logger.info(f"清理过期缓存: 内存 {memory_cleaned} 个, 磁盘 {disk_cleaned} 个")
            
        self.stats.last_cleanup = datetime.now()


class AIResultCache:
    """AI结果缓存"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache_manager = cache_manager
        
    def _generate_prompt_hash(self, prompt: str, model: str = '', 
                            temperature: float = 0.7, **kwargs) -> str:
        """生成提示词哈希"""
        # 包含所有影响结果的参数
        content = f"{prompt}|{model}|{temperature}|{json.dumps(kwargs, sort_keys=True)}"
        return hashlib.sha256(content.encode()).hexdigest()
        
    def get_cached_result(self, prompt: str, model: str = '', 
                         temperature: float = 0.7, **kwargs) -> Optional[str]:
        """获取缓存的AI结果"""
        prompt_hash = self._generate_prompt_hash(prompt, model, temperature, **kwargs)
        cache_key = f"ai_result:{prompt_hash}"
        return self.cache_manager.get(cache_key)
        
    def cache_result(self, prompt: str, result: str, model: str = '', 
                    temperature: float = 0.7, ttl: int = 7 * 24 * 3600, **kwargs):
        """缓存AI结果"""
        prompt_hash = self._generate_prompt_hash(prompt, model, temperature, **kwargs)
        cache_key = f"ai_result:{prompt_hash}"
        
        # AI结果默认缓存7天
        self.cache_manager.set(cache_key, result, ttl=ttl)
        
    def invalidate_model_cache(self, model: str):
        """失效特定模型的缓存"""
        pattern = f"ai_result:.*{model}.*"
        self.cache_manager.invalidate_pattern(pattern)
        
    def clear_ai_cache(self):
        """清空所有AI缓存"""
        self.cache_manager.invalidate_pattern("ai_result:.*")


class ProjectDataCache:
    """项目数据缓存"""
    
    def __init__(self, cache_manager: CacheManager):
        self.cache_manager = cache_manager
        
    def get_project(self, project_id: str) -> Optional[Any]:
        """获取项目缓存"""
        return self.cache_manager.get(f"project:{project_id}")
        
    def cache_project(self, project_id: str, project_data: Any, ttl: int = 3600):
        """缓存项目数据"""
        self.cache_manager.set(f"project:{project_id}", project_data, ttl=ttl)
        
    def get_chapter(self, chapter_id: str) -> Optional[Any]:
        """获取章节缓存"""
        return self.cache_manager.get(f"chapter:{chapter_id}")
        
    def cache_chapter(self, chapter_id: str, chapter_data: Any, ttl: int = 1800):
        """缓存章节数据"""
        self.cache_manager.set(f"chapter:{chapter_id}", chapter_data, ttl=ttl)
        
    def get_character(self, character_id: str) -> Optional[Any]:
        """获取角色缓存"""
        return self.cache_manager.get(f"character:{character_id}")
        
    def cache_character(self, character_id: str, character_data: Any, ttl: int = 1800):
        """缓存角色数据"""
        self.cache_manager.set(f"character:{character_id}", character_data, ttl=ttl)
        
    def invalidate_project_cache(self, project_id: str):
        """失效项目相关缓存"""
        patterns = [
            f"project:{project_id}",
            f"chapter:.*{project_id}.*",
            f"character:.*{project_id}.*",
            f"outline:.*{project_id}.*"
        ]
        
        for pattern in patterns:
            self.cache_manager.invalidate_pattern(pattern)


def cached(ttl: int = 3600, key_func: Optional[Callable] = None):
    """缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默认使用函数名和参数生成键
                args_str = str(args) + str(sorted(kwargs.items()))
                cache_key = f"{func.__name__}:{hashlib.md5(args_str.encode()).hexdigest()}"
                
            # 尝试从缓存获取
            cache_manager = get_cache_manager()
            result = cache_manager.get(cache_key)
            
            if result is not None:
                return result
                
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl=ttl)
            
            return result
            
        return wrapper
    return decorator


def cache_clear(pattern: str = None):
    """清理缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # 执行后清理相关缓存
            cache_manager = get_cache_manager()
            if pattern:
                cache_manager.invalidate_pattern(pattern)
            else:
                # 默认清理与函数名相关的缓存
                cache_manager.invalidate_pattern(f"{func.__name__}:.*")
                
            return result
            
        return wrapper
    return decorator


# 全局缓存管理器实例
_cache_manager: Optional[CacheManager] = None
_ai_cache: Optional[AIResultCache] = None
_project_cache: Optional[ProjectDataCache] = None


def get_cache_manager() -> CacheManager:
    """获取缓存管理器实例"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
    return _cache_manager


def get_ai_cache() -> AIResultCache:
    """获取AI结果缓存实例"""
    global _ai_cache
    if _ai_cache is None:
        _ai_cache = AIResultCache(get_cache_manager())
    return _ai_cache


def get_project_cache() -> ProjectDataCache:
    """获取项目数据缓存实例"""
    global _project_cache
    if _project_cache is None:
        _project_cache = ProjectDataCache(get_cache_manager())
    return _project_cache


def initialize_cache_system(cache_dir: str = None, **config):
    """初始化缓存系统"""
    global _cache_manager, _ai_cache, _project_cache
    
    _cache_manager = CacheManager(cache_dir=cache_dir, **config)
    _ai_cache = AIResultCache(_cache_manager)
    _project_cache = ProjectDataCache(_cache_manager)
    
    logger.info("缓存系统初始化完成")


def shutdown_cache_system():
    """关闭缓存系统"""
    global _cache_manager, _ai_cache, _project_cache
    
    if _cache_manager:
        _cache_manager.clear()
        
    _cache_manager = None
    _ai_cache = None
    _project_cache = None
    
    logger.info("缓存系统已关闭")