# Git忽略文件配置

# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# C扩展
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 安装器日志
pip-log.txt
pip-delete-this-directory.txt

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask
instance/
.webassets-cache

# Scrapy
.scrapy

# Sphinx文档
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre类型检查器
.pyre/

# PyCharm
.idea/

# VS Code
.vscode/

# 项目特定文件
*.db
*.sqlite3
*.log
.DS_Store
Thumbs.db

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 备份文件
*.bak
*.backup

# 配置文件（可能包含敏感信息）
config.json
settings.json
*.cfg
!setup.cfg

# AI API密钥
.openai_key
.anthropic_key
api_keys.txt

# 用户数据
user_data/
projects/
exports/

# 测试输出
test_output/
test_reports/

# 打包输出
*.exe
*.dmg
*.pkg
*.deb
*.rpm
*.appimage

# MacOS
.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# 编辑器临时文件
*.swp
*.swo
*~

# 资源缓存
*.cache