"""
配置管理工具

处理应用程序的配置管理
"""

import os
import json
import toml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import logging
from functools import lru_cache

from ..core.cache_manager import get_cache_manager
from ..core.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """数据库配置"""
    url: str = "sqlite:///bamboofall.db"
    pool_size: int = 5
    max_overflow: int = 10
    echo: bool = False


@dataclass
class AIConfig:
    """AI服务配置"""
    openai_api_key: str = ""
    openai_base_url: str = "https://api.openai.com/v1"
    anthropic_api_key: str = ""
    deepseek_api_key: str = ""
    default_model: str = "gpt-4"
    max_tokens: int = 2000
    temperature: float = 0.7


@dataclass
class UIConfig:
    """界面配置"""
    theme: str = "auto"  # auto, light, dark
    font_family: str = "Source Han Sans CN"
    font_size: int = 12
    editor_font_family: str = "JetBrains Mono"
    editor_font_size: int = 14
    window_width: int = 1200
    window_height: int = 800
    auto_save_interval: int = 300  # 秒


@dataclass
class AppConfig:
    """应用程序总配置"""
    database: DatabaseConfig
    ai: AIConfig
    ui: UIConfig
    debug: bool = False
    log_level: str = "INFO"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AppConfig":
        """从字典创建配置"""
        database_config = DatabaseConfig(**data.get("database", {}))
        ai_config = AIConfig(**data.get("ai", {}))
        ui_config = UIConfig(**data.get("ui", {}))
        
        return cls(
            database=database_config,
            ai=ai_config,
            ui=ui_config,
            debug=data.get("debug", False),
            log_level=data.get("log_level", "INFO")
        )


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[Path] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认使用用户配置目录
        """
        if config_dir is None:
            config_dir = self._get_config_directory()
        
        self.config_dir = config_dir
        self.config_file = config_dir / "config.toml"
        self.secrets_file = config_dir / "secrets.json"
        
        # 确保配置目录存在
        self.config_dir.mkdir(parents=True, exist_ok=True)
        
        self._config: Optional[AppConfig] = None
        self._secrets: Dict[str, str] = {}
        
        # 缓存管理
        self.cache_manager = get_cache_manager()
        self.cache_ttl = 300  # 5分钟缓存
        
        # 加载配置
        self.load_config()
        self.load_secrets()
    
    def _get_config_directory(self) -> Path:
        """获取配置目录"""
        if os.name == 'nt':  # Windows
            config_dir = Path(os.getenv('APPDATA', '')) / 'BambooFall'
        elif os.name == 'posix':
            if os.uname().sysname == 'Darwin':  # macOS
                config_dir = Path.home() / 'Library' / 'Application Support' / 'BambooFall'
            else:  # Linux
                config_dir = Path.home() / '.config' / 'bamboofall'
        else:
            # 其他系统，使用当前用户目录
            config_dir = Path.home() / '.bamboofall'
        
        return config_dir
    
    def load_config(self) -> AppConfig:
        """加载配置"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = toml.load(f)
                self._config = AppConfig.from_dict(config_data)
                logger.info(f"配置已从 {self.config_file} 加载")
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
                self._config = AppConfig(
                    database=DatabaseConfig(),
                    ai=AIConfig(),
                    ui=UIConfig()
                )
        else:
            # 创建默认配置
            self._config = AppConfig(
                database=DatabaseConfig(),
                ai=AIConfig(),
                ui=UIConfig()
            )
            self.save_config()
            logger.info("已创建默认配置文件")
        
        return self._config
    
    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                toml.dump(self._config.to_dict(), f)
            logger.info(f"配置已保存到 {self.config_file}")
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
    
    def load_secrets(self) -> Dict[str, str]:
        """加载密钥配置"""
        if self.secrets_file.exists():
            try:
                with open(self.secrets_file, 'r', encoding='utf-8') as f:
                    self._secrets = json.load(f)
                logger.info("密钥配置已加载")
            except Exception as e:
                logger.error(f"加载密钥配置失败: {e}")
                self._secrets = {}
        else:
            self._secrets = {}
        
        return self._secrets
    
    def save_secrets(self):
        """保存密钥配置"""
        try:
            # 确保密钥文件权限安全
            with open(self.secrets_file, 'w', encoding='utf-8') as f:
                json.dump(self._secrets, f, indent=2)
            
            # 设置文件权限（仅所有者可读写）
            if os.name != 'nt':  # 非Windows系统
                os.chmod(self.secrets_file, 0o600)
            
            logger.info("密钥配置已保存")
        except Exception as e:
            logger.error(f"保存密钥配置失败: {e}")
    
    @property
    @performance_monitor(category="config")
    def config(self) -> AppConfig:
        """获取当前配置"""
        # 尝试从缓存获取
        cached_config = self.cache_manager.get("app_config")
        if cached_config:
            self._config = cached_config
            return self._config
            
        if self._config is None:
            self._config = self.load_config()
            # 缓存配置
            self.cache_manager.set("app_config", self._config, ttl=self.cache_ttl)
        return self._config
    
    @performance_monitor(category="config")
    def get_secret(self, key: str) -> Optional[str]:
        """获取密钥"""
        # 尝试从缓存获取
        cache_key = f"secret_{key}"
        cached_secret = self.cache_manager.get(cache_key)
        if cached_secret:
            return cached_secret
            
        secret = self._secrets.get(key)
        if secret:
            # 缓存密钥（较短的TTL）
            self.cache_manager.set(cache_key, secret, ttl=60)
        return secret
    
    def set_secret(self, key: str, value: str):
        """设置密钥"""
        self._secrets[key] = value
        self.save_secrets()
        
        # 更新缓存
        cache_key = f"secret_{key}"
        self.cache_manager.set(cache_key, value, ttl=60)
    
    def update_config(self, **kwargs):
        """更新配置"""
        config_dict = self._config.to_dict()
        
        # 递归更新配置
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(config_dict, kwargs)
        self._config = AppConfig.from_dict(config_dict)
        self.save_config()
        
        # 清理配置缓存
        self.cache_manager.delete("app_config")
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        db_config = self.config.database
        if db_config.url.startswith("sqlite"):
            # 处理SQLite路径
            if ":///" not in db_config.url:
                # 相对路径，转为绝对路径
                db_path = self.config_dir / "bamboofall.db"
                return f"sqlite:///{db_path}"
        return db_config.url
    
    @lru_cache(maxsize=32)
    @performance_monitor(category="config")
    def get_ai_api_key(self, provider: str) -> Optional[str]:
        """获取AI API密钥"""
        key_mapping = {
            "openai": "openai_api_key",
            "anthropic": "anthropic_api_key", 
            "deepseek": "deepseek_api_key"
        }
        
        key_name = key_mapping.get(provider)
        if key_name:
            # 优先从secrets文件获取
            secret_key = self.get_secret(key_name)
            if secret_key:
                return secret_key
            
            # 其次从配置文件获取
            return getattr(self.config.ai, key_name, None)
        
        return None
    
    def set_ai_api_key(self, provider: str, api_key: str):
        """设置AI API密钥"""
        key_mapping = {
            "openai": "openai_api_key",
            "anthropic": "anthropic_api_key",
            "deepseek": "deepseek_api_key"
        }
        
        key_name = key_mapping.get(provider)
        if key_name:
            self.set_secret(key_name, api_key)
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self._config = AppConfig(
            database=DatabaseConfig(),
            ai=AIConfig(),
            ui=UIConfig()
        )
        self.save_config()
        
        # 清理所有配置相关缓存
        self.clear_cache()
        logger.info("配置已重置为默认值")
    
    def clear_cache(self):
        """清理配置缓存"""
        self.cache_manager.delete("app_config")
        self.cache_manager.invalidate_pattern("secret_*")
        
        # 清理LRU缓存
        if hasattr(self.get_ai_api_key, 'cache_clear'):
            self.get_ai_api_key.cache_clear()
        
        logger.info("配置缓存已清理")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'config_cache': self.cache_manager.get_stats(),
            'lru_cache_info': self.get_ai_api_key.cache_info() if hasattr(self.get_ai_api_key, 'cache_info') else None
        }
    
    def set(self, key: str, value: Any):
        """设置配置项"""
        # 支持点分隔的键路径，如 'ai.temperature'
        keys = key.split('.')
        config_dict = self._config.to_dict()
        
        # 导航到目标位置
        current = config_dict
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # 设置值
        current[keys[-1]] = value
        
        # 更新配置
        self._config = AppConfig.from_dict(config_dict)
        self.save_config()
        
        # 清理配置缓存
        self.cache_manager.delete("app_config")
        logger.info(f"配置项 {key} 已更新")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        # 支持点分隔的键路径，如 'ai.temperature'
        keys = key.split('.')
        current = self.config.to_dict()
        
        try:
            for k in keys:
                current = current[k]
            return current
        except (KeyError, TypeError):
            return default


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def get_config() -> AppConfig:
    """获取当前配置"""
    return get_config_manager().config