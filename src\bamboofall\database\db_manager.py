"""
数据库管理器

处理数据库连接、初始化和基本操作
"""

import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from contextlib import contextmanager

from sqlalchemy import create_engine, event, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from .models import Base, ProjectORM, CharacterORM, ChapterORM, SceneORM

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, database_url: Optional[str] = None):
        """
        初始化数据库管理器
        
        Args:
            database_url: 数据库连接URL，默认使用SQLite
        """
        if database_url is None:
            # 默认使用用户数据目录下的SQLite数据库
            data_dir = self._get_data_directory()
            db_path = data_dir / "bamboofall.db"
            database_url = f"sqlite:///{db_path}"
        
        self.database_url = database_url
        self.engine = None
        self.SessionLocal = None
        self._setup_database()
    
    def _get_data_directory(self) -> Path:
        """获取数据目录"""
        if os.name == 'nt':  # Windows
            data_dir = Path(os.getenv('APPDATA', '')) / 'BambooFall'
        else:  # macOS/Linux
            data_dir = Path.home() / '.bamboofall'
        
        data_dir.mkdir(parents=True, exist_ok=True)
        return data_dir
    
    def _setup_database(self):
        """设置数据库连接"""
        # 创建引擎
        if self.database_url.startswith('sqlite'):
            # SQLite特殊配置
            self.engine = create_engine(
                self.database_url,
                echo=False,  # 生产环境设为False
                poolclass=StaticPool,
                connect_args={
                    "check_same_thread": False,
                    "timeout": 20
                }
            )
            
            # 启用SQLite外键约束
            @event.listens_for(self.engine, "connect")
            def set_sqlite_pragma(dbapi_connection, connection_record):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA foreign_keys=ON")
                cursor.execute("PRAGMA journal_mode=WAL")  # 启用WAL模式提高并发性能
                cursor.close()
        else:
            # 其他数据库
            self.engine = create_engine(self.database_url, echo=False)
        
        # 创建会话工厂
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        # 创建表
        self.create_tables()
    
    def create_tables(self):
        """创建所有表"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库表创建成功")
        except Exception as e:
            logger.error(f"创建数据库表失败: {e}")
            raise
    
    def drop_tables(self):
        """删除所有表（谨慎使用）"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("数据库表删除成功")
        except Exception as e:
            logger.error(f"删除数据库表失败: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """获取数据库会话（上下文管理器）"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def get_session_direct(self) -> Session:
        """直接获取数据库会话（需要手动管理）"""
        return self.SessionLocal()
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_session() as session:
                session.execute(text("SELECT 1"))
            logger.info("数据库连接测试成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        info = {
            "database_url": self.database_url,
            "engine": str(self.engine),
            "tables": []
        }
        
        try:
            with self.get_session() as session:
                # 获取表信息
                if self.database_url.startswith('sqlite'):
                    result = session.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
                    info["tables"] = [row[0] for row in result]
                else:
                    # 其他数据库的表查询方法
                    pass
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
        
        return info
    
    def backup_database(self, backup_path: str) -> bool:
        """备份数据库"""
        try:
            if self.database_url.startswith('sqlite'):
                # SQLite备份
                import shutil
                db_path = self.database_url.replace('sqlite:///', '')
                shutil.copy2(db_path, backup_path)
                logger.info(f"数据库备份到: {backup_path}")
                return True
            else:
                logger.warning("当前数据库类型不支持直接备份")
                return False
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False
    
    def restore_database(self, backup_path: str) -> bool:
        """恢复数据库"""
        try:
            if self.database_url.startswith('sqlite'):
                # SQLite恢复
                import shutil
                db_path = self.database_url.replace('sqlite:///', '')
                shutil.copy2(backup_path, db_path)
                logger.info(f"数据库从 {backup_path} 恢复")
                return True
            else:
                logger.warning("当前数据库类型不支持直接恢复")
                return False
        except Exception as e:
            logger.error(f"数据库恢复失败: {e}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")


# 全局数据库管理器实例
_db_manager: Optional[DatabaseManager] = None


def get_database_manager(database_url: Optional[str] = None) -> DatabaseManager:
    """获取全局数据库管理器实例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager(database_url)
    return _db_manager


def init_database(database_url: Optional[str] = None) -> DatabaseManager:
    """初始化数据库"""
    global _db_manager
    _db_manager = DatabaseManager(database_url)
    return _db_manager


def get_session():
    """获取数据库会话（便捷函数）"""
    db_manager = get_database_manager()
    return db_manager.get_session()


def get_session_direct() -> Session:
    """直接获取数据库会话（便捷函数）"""
    db_manager = get_database_manager()
    return db_manager.get_session_direct()