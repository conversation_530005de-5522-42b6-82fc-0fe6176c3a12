"""
AI服务基类和数据结构

定义AI服务的抽象接口和通用数据结构
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, AsyncGenerator
from enum import Enum
import time
import uuid


class MessageRole(Enum):
    """消息角色枚举"""
    SYSTEM = "system"
    USER = "user"  
    ASSISTANT = "assistant"


class AIModelType(Enum):
    """AI模型类型枚举"""
    CHAT = "chat"           # 对话模型
    COMPLETION = "completion"  # 文本补全模型
    EMBEDDING = "embedding"    # 嵌入模型


@dataclass
class AIMessage:
    """AI消息数据结构"""
    role: MessageRole
    content: str
    timestamp: float = field(default_factory=time.time)
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "role": self.role.value,
            "content": self.content,
            "timestamp": self.timestamp,
            "id": self.id,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AIMessage":
        """从字典创建消息"""
        return cls(
            role=MessageRole(data["role"]),
            content=data["content"],
            timestamp=data.get("timestamp", time.time()),
            id=data.get("id", str(uuid.uuid4())),
            metadata=data.get("metadata", {})
        )


@dataclass 
class AIResponse:
    """AI响应数据结构"""
    content: str
    model: str
    provider: str
    usage: Dict[str, int] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    finish_reason: Optional[str] = None
    response_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "content": self.content,
            "model": self.model,
            "provider": self.provider,
            "usage": self.usage,
            "metadata": self.metadata,
            "finish_reason": self.finish_reason,
            "response_time": self.response_time
        }


@dataclass
class AIConfig:
    """AI服务配置"""
    api_key: str
    base_url: Optional[str] = None
    model: str = "gpt-3.5-turbo"
    max_tokens: int = 1000
    temperature: float = 0.7
    timeout: int = 30
    retry_count: int = 3
    extra_params: Dict[str, Any] = field(default_factory=dict)


class AIServiceBase(ABC):
    """AI服务基类"""
    
    def __init__(self, config: AIConfig):
        self.config = config
        self.provider_name = self.__class__.__name__.replace("Service", "").lower()
        
    @property
    @abstractmethod
    def supported_models(self) -> List[str]:
        """支持的模型列表"""
        pass
        
    @property
    @abstractmethod
    def model_type(self) -> AIModelType:
        """模型类型"""
        pass
    
    @abstractmethod
    async def chat(
        self,
        messages: List[AIMessage],
        model: Optional[str] = None,
        **kwargs
    ) -> AIResponse:
        """聊天接口"""
        pass
    
    @abstractmethod
    async def stream_chat(
        self,
        messages: List[AIMessage], 
        model: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式聊天接口"""
        pass
    
    @abstractmethod
    async def complete(
        self,
        prompt: str,
        model: Optional[str] = None,
        **kwargs
    ) -> AIResponse:
        """文本补全接口"""
        pass
    
    @abstractmethod
    async def get_embedding(
        self,
        text: str,
        model: Optional[str] = None
    ) -> List[float]:
        """获取文本嵌入"""
        pass
    
    @abstractmethod
    def validate_config(self) -> bool:
        """验证配置是否有效"""
        pass
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            test_messages = [
                AIMessage(role=MessageRole.USER, content="Hello")
            ]
            response = await self.chat(test_messages)
            return response is not None
        except Exception:
            return False
    
    def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "name": model_name,
            "provider": self.provider_name,
            "type": self.model_type.value,
            "supported": model_name in self.supported_models
        }


class AIServiceError(Exception):
    """AI服务异常基类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict] = None):
        super().__init__(message)
        self.error_code = error_code
        self.details = details or {}


class APIKeyError(AIServiceError):
    """API密钥错误"""
    pass


class ModelNotSupportedError(AIServiceError):
    """模型不支持错误"""
    pass


class RateLimitError(AIServiceError):
    """速率限制错误"""
    pass


class NetworkError(AIServiceError):
    """网络错误"""
    pass