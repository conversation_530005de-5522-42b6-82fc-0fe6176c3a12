"""
Markdown预览组件

提供实时Markdown预览功能
"""

from PyQt6.QtWidgets import QTextBrowser, QScrollArea, QFrame, QVBoxLayout
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QTextDocument, QTextCursor
from typing import Optional
import markdown
import re

from ..themes.theme_manager import get_theme_manager
from ...utils.logger import LoggerMixin


class MarkdownPreview(QTextBrowser, LoggerMixin):
    """Markdown预览组件"""
    
    link_clicked = pyqtSignal(str)  # 链接点击信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.theme_manager = get_theme_manager()
        self.markdown_converter = markdown.Markdown(
            extensions=['extra', 'codehilite', 'toc'],
            extension_configs={
                'codehilite': {
                    'css_class': 'highlight',
                    'use_pygments': False
                }
            }
        )
        
        # 预览更新定时器
        self.update_timer = QTimer()
        self.update_timer.setSingleShot(True)
        self.update_timer.timeout.connect(self._do_update_preview)
        
        self.setup_ui()
        self.setup_style()
        self.theme_manager.theme_changed.connect(self.setup_style)
        
        # 连接链接点击事件
        self.anchorClicked.connect(self._on_link_clicked)
        self.setOpenLinks(False)  # 禁用默认链接打开行为
        
    def setup_ui(self):
        """设置UI"""
        self.setReadOnly(True)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # 设置字体
        font = QFont("Microsoft YaHei", 11)
        self.setFont(font)
        
    def setup_style(self):
        """设置样式"""
        colors = self.theme_manager.current_colors
        
        # CSS样式
        css = f"""
        body {{
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: {colors['text_primary']};
            background-color: {colors['background']};
            margin: 20px;
            padding: 0;
        }}
        
        h1, h2, h3, h4, h5, h6 {{
            color: {colors['primary']};
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
        }}
        
        h1 {{ font-size: 2em; border-bottom: 2px solid {colors['border']}; padding-bottom: 8px; }}
        h2 {{ font-size: 1.5em; border-bottom: 1px solid {colors['border']}; padding-bottom: 4px; }}
        h3 {{ font-size: 1.25em; }}
        h4 {{ font-size: 1.1em; }}
        h5 {{ font-size: 1em; }}
        h6 {{ font-size: 0.9em; color: {colors['text_secondary']}; }}
        
        p {{
            margin-bottom: 16px;
            text-align: justify;
        }}
        
        code {{
            background-color: {colors['surface_hover']};
            color: {colors['accent']};
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.9em;
        }}
        
        pre {{
            background-color: {colors['surface_hover']};
            color: {colors['text_primary']};
            padding: 16px;
            border-radius: 6px;
            border-left: 4px solid {colors['primary']};
            overflow-x: auto;
            margin: 16px 0;
        }}
        
        pre code {{
            background: transparent;
            padding: 0;
        }}
        
        blockquote {{
            background-color: {colors['surface']};
            border-left: 4px solid {colors['primary']};
            margin: 16px 0;
            padding: 16px;
            font-style: italic;
            color: {colors['text_secondary']};
        }}
        
        ul, ol {{
            margin-bottom: 16px;
            padding-left: 24px;
        }}
        
        li {{
            margin-bottom: 4px;
        }}
        
        a {{
            color: {colors['primary']};
            text-decoration: none;
        }}
        
        a:hover {{
            text-decoration: underline;
        }}
        
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
        }}
        
        th, td {{
            border: 1px solid {colors['border']};
            padding: 8px 12px;
            text-align: left;
        }}
        
        th {{
            background-color: {colors['surface']};
            font-weight: 600;
        }}
        
        tr:nth-child(even) {{
            background-color: {colors['surface_hover']};
        }}
        
        hr {{
            border: none;
            border-top: 2px solid {colors['border']};
            margin: 24px 0;
        }}
        
        img {{
            max-width: 100%;
            height: auto;
            border-radius: 4px;
        }}
        
        .highlight {{
            background-color: {colors['surface_hover']};
            border-radius: 4px;
        }}
        """
        
        self.document().setDefaultStyleSheet(css)
        
        # 设置组件样式
        self.setStyleSheet(f"""
            MarkdownPreview {{
                background-color: {colors['background']};
                border: 1px solid {colors['border']};
                border-radius: 8px;
                padding: 0px;
            }}
            
            QScrollBar:vertical {{
                background-color: {colors['surface']};
                width: 12px;
                border-radius: 6px;
            }}
            
            QScrollBar::handle:vertical {{
                background-color: {colors['text_disabled']};
                border-radius: 6px;
                min-height: 20px;
            }}
            
            QScrollBar::handle:vertical:hover {{
                background-color: {colors['text_secondary']};
            }}
        """)
    
    def update_preview(self, markdown_text: str):
        """更新预览内容（延迟更新）"""
        self.pending_text = markdown_text
        self.update_timer.start(300)  # 300ms延迟
    
    def _do_update_preview(self):
        """执行预览更新"""
        try:
            if hasattr(self, 'pending_text'):
                html = self.markdown_converter.convert(self.pending_text)
                
                # 保存滚动位置
                scrollbar = self.verticalScrollBar()
                scroll_position = scrollbar.value()
                
                self.setHtml(html)
                
                # 恢复滚动位置
                scrollbar.setValue(scroll_position)
                
                self.logger.debug("Markdown预览已更新")
        except Exception as e:
            self.logger.error(f"更新Markdown预览失败: {e}")
            self.setText("预览更新失败，请检查Markdown语法")
    
    def _on_link_clicked(self, url):
        """处理链接点击"""
        self.link_clicked.emit(url.toString())
    
    def scroll_to_line(self, line_number: int):
        """滚动到指定行（用于同步滚动）"""
        try:
            # 简单的同步滚动实现
            cursor = self.textCursor()
            cursor.movePosition(QTextCursor.MoveOperation.Start)
            
            for _ in range(line_number - 1):
                cursor.movePosition(QTextCursor.MoveOperation.NextBlock)
            
            self.setTextCursor(cursor)
            self.ensureCursorVisible()
        except Exception as e:
            self.logger.error(f"滚动到指定行失败: {e}")


class PreviewContainer(QFrame):
    """预览容器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        self.preview = MarkdownPreview()
        layout.addWidget(self.preview)
        
    def update_preview(self, text: str):
        """更新预览"""
        self.preview.update_preview(text)
        
    def scroll_to_line(self, line: int):
        """滚动到指定行"""
        self.preview.scroll_to_line(line)