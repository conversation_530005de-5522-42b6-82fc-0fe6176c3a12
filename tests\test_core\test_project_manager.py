# -*- coding: utf-8 -*-
"""
项目管理器测试
"""

import pytest
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from bamboofall.core.project_manager import ProjectManager
from bamboofall.models.project import Project
from bamboofall.models.character import Character
from bamboofall.models.outline import Outline


class TestProjectManager:
    """ProjectManager测试类"""
    
    @pytest.fixture
    def temp_dir(self):
        """临时目录fixture"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def mock_config_manager(self):
        """Mock配置管理器"""
        config_manager = Mock()
        config_manager.get.side_effect = lambda key, default=None: {
            "projects_directory": "test_projects",
            "default_project_template": "novel",
            "auto_save_interval": 300
        }.get(key, default)
        return config_manager
    
    @pytest.fixture
    def project_manager(self, mock_config_manager, temp_dir):
        """ProjectManager实例fixture"""
        with patch('bamboofall.core.project_manager.get_config_manager', return_value=mock_config_manager):
            with patch('bamboofall.core.project_manager.get_database_manager') as mock_db:
                mock_db_instance = Mock()
                mock_db.return_value = mock_db_instance
                
                manager = ProjectManager()
                manager.projects_directory = temp_dir
                return manager
    
    @pytest.fixture
    def sample_project(self):
        """示例项目fixture"""
        return Project(
            id="test-project-1",
            name="测试项目",
            description="这是一个测试项目",
            genre="科幻",
            target_words=100000,
            created_at="2024-01-01T00:00:00"
        )
    
    def test_init(self, project_manager):
        """测试初始化"""
        assert project_manager.current_project is None
        assert project_manager.projects_directory is not None
        assert hasattr(project_manager, 'db_manager')
    
    def test_create_project(self, project_manager, temp_dir):
        """测试创建项目"""
        project_data = {
            "name": "新项目",
            "description": "项目描述",
            "genre": "奇幻",
            "target_words": 80000
        }
        
        with patch.object(project_manager.db_manager, 'create_project') as mock_create:
            mock_project = Project(id="new-project", **project_data)
            mock_create.return_value = mock_project
            
            project = project_manager.create_project(**project_data)
            
            assert project.name == "新项目"
            assert project.description == "项目描述"
            assert project.genre == "奇幻"
            assert project.target_words == 80000
            
            # 验证数据库调用
            mock_create.assert_called_once()
            
            # 验证项目目录创建
            project_dir = os.path.join(temp_dir, "new-project")
            assert os.path.exists(project_dir)
    
    def test_create_project_with_template(self, project_manager):
        """测试使用模板创建项目"""
        project_data = {
            "name": "模板项目",
            "template": "screenplay"
        }
        
        with patch.object(project_manager.db_manager, 'create_project') as mock_create:
            with patch.object(project_manager, '_apply_project_template') as mock_template:
                mock_project = Project(id="template-project", **project_data)
                mock_create.return_value = mock_project
                
                project = project_manager.create_project(**project_data)
                
                assert project.name == "模板项目"
                mock_template.assert_called_once_with(project, "screenplay")
    
    def test_load_project(self, project_manager, sample_project):
        """测试加载项目"""
        with patch.object(project_manager.db_manager, 'get_project') as mock_get:
            mock_get.return_value = sample_project
            
            project = project_manager.load_project("test-project-1")
            
            assert project == sample_project
            assert project_manager.current_project == sample_project
            mock_get.assert_called_once_with("test-project-1")
    
    def test_load_project_not_found(self, project_manager):
        """测试加载不存在的项目"""
        with patch.object(project_manager.db_manager, 'get_project') as mock_get:
            mock_get.return_value = None
            
            with pytest.raises(ValueError, match="项目不存在"):
                project_manager.load_project("non-existent")
    
    def test_save_project(self, project_manager, sample_project):
        """测试保存项目"""
        project_manager.current_project = sample_project
        
        with patch.object(project_manager.db_manager, 'update_project') as mock_update:
            project_manager.save_project()
            
            mock_update.assert_called_once_with(sample_project)
    
    def test_save_project_no_current(self, project_manager):
        """测试保存项目（无当前项目）"""
        with pytest.raises(ValueError, match="没有当前项目"):
            project_manager.save_project()
    
    def test_delete_project(self, project_manager, temp_dir):
        """测试删除项目"""
        # 创建项目目录
        project_dir = os.path.join(temp_dir, "test-project")
        os.makedirs(project_dir)
        
        with patch.object(project_manager.db_manager, 'delete_project') as mock_delete:
            project_manager.delete_project("test-project")
            
            mock_delete.assert_called_once_with("test-project")
            # 验证目录被删除
            assert not os.path.exists(project_dir)
    
    def test_get_all_projects(self, project_manager):
        """测试获取所有项目"""
        mock_projects = [
            Project(id="p1", name="项目1"),
            Project(id="p2", name="项目2")
        ]
        
        with patch.object(project_manager.db_manager, 'get_all_projects') as mock_get_all:
            mock_get_all.return_value = mock_projects
            
            projects = project_manager.get_all_projects()
            
            assert len(projects) == 2
            assert projects[0].name == "项目1"
            assert projects[1].name == "项目2"
            mock_get_all.assert_called_once()
    
    def test_get_recent_projects(self, project_manager):
        """测试获取最近项目"""
        mock_projects = [
            Project(id="p1", name="最近项目1"),
            Project(id="p2", name="最近项目2")
        ]
        
        with patch.object(project_manager.db_manager, 'get_recent_projects') as mock_get_recent:
            mock_get_recent.return_value = mock_projects
            
            projects = project_manager.get_recent_projects(limit=5)
            
            assert len(projects) == 2
            mock_get_recent.assert_called_once_with(5)
    
    def test_search_projects(self, project_manager):
        """测试搜索项目"""
        mock_projects = [
            Project(id="p1", name="科幻小说", genre="科幻")
        ]
        
        with patch.object(project_manager.db_manager, 'search_projects') as mock_search:
            mock_search.return_value = mock_projects
            
            projects = project_manager.search_projects("科幻")
            
            assert len(projects) == 1
            assert projects[0].genre == "科幻"
            mock_search.assert_called_once_with("科幻")
    
    def test_update_project_metadata(self, project_manager, sample_project):
        """测试更新项目元数据"""
        project_manager.current_project = sample_project
        
        updates = {
            "description": "更新的描述",
            "target_words": 120000
        }
        
        with patch.object(project_manager.db_manager, 'update_project') as mock_update:
            project_manager.update_project_metadata(**updates)
            
            assert sample_project.description == "更新的描述"
            assert sample_project.target_words == 120000
            mock_update.assert_called_once_with(sample_project)
    
    def test_get_project_statistics(self, project_manager, sample_project):
        """测试获取项目统计"""
        project_manager.current_project = sample_project
        
        mock_stats = {
            "total_words": 25000,
            "total_characters": 5,
            "total_chapters": 10,
            "completion_rate": 0.25
        }
        
        with patch.object(project_manager.db_manager, 'get_project_statistics') as mock_stats_db:
            mock_stats_db.return_value = mock_stats
            
            stats = project_manager.get_project_statistics()
            
            assert stats["total_words"] == 25000
            assert stats["completion_rate"] == 0.25
            mock_stats_db.assert_called_once_with(sample_project.id)
    
    def test_export_project(self, project_manager, sample_project, temp_dir):
        """测试导出项目"""
        project_manager.current_project = sample_project
        export_path = os.path.join(temp_dir, "export.json")
        
        with patch.object(project_manager, '_export_project_data') as mock_export:
            mock_export.return_value = {"project": "data"}
            
            result_path = project_manager.export_project(export_path, format="json")
            
            assert result_path == export_path
            mock_export.assert_called_once_with(sample_project, "json")
    
    def test_import_project(self, project_manager, temp_dir):
        """测试导入项目"""
        import_path = os.path.join(temp_dir, "import.json")
        
        # 创建导入文件
        import_data = {"name": "导入项目", "description": "导入的项目"}
        
        with patch.object(project_manager, '_import_project_data') as mock_import:
            mock_project = Project(id="imported", **import_data)
            mock_import.return_value = mock_project
            
            project = project_manager.import_project(import_path)
            
            assert project.name == "导入项目"
            mock_import.assert_called_once_with(import_path)
    
    def test_backup_project(self, project_manager, sample_project, temp_dir):
        """测试备份项目"""
        project_manager.current_project = sample_project
        backup_dir = os.path.join(temp_dir, "backups")
        
        with patch.object(project_manager, '_create_backup') as mock_backup:
            backup_path = os.path.join(backup_dir, "backup.zip")
            mock_backup.return_value = backup_path
            
            result_path = project_manager.backup_project(backup_dir)
            
            assert result_path == backup_path
            mock_backup.assert_called_once_with(sample_project, backup_dir)
    
    def test_restore_project(self, project_manager, temp_dir):
        """测试恢复项目"""
        backup_path = os.path.join(temp_dir, "backup.zip")
        
        with patch.object(project_manager, '_restore_from_backup') as mock_restore:
            mock_project = Project(id="restored", name="恢复的项目")
            mock_restore.return_value = mock_project
            
            project = project_manager.restore_project(backup_path)
            
            assert project.name == "恢复的项目"
            mock_restore.assert_called_once_with(backup_path)
    
    def test_auto_save(self, project_manager, sample_project):
        """测试自动保存"""
        project_manager.current_project = sample_project
        
        with patch.object(project_manager, 'save_project') as mock_save:
            project_manager.auto_save()
            
            mock_save.assert_called_once()
    
    def test_get_project_path(self, project_manager, temp_dir):
        """测试获取项目路径"""
        project_id = "test-project"
        expected_path = os.path.join(temp_dir, project_id)
        
        path = project_manager.get_project_path(project_id)
        
        assert path == expected_path
    
    def test_validate_project_name(self, project_manager):
        """测试验证项目名称"""
        # 有效名称
        assert project_manager.validate_project_name("有效项目名") is True
        
        # 无效名称
        assert project_manager.validate_project_name("") is False
        assert project_manager.validate_project_name("   ") is False
        assert project_manager.validate_project_name("a" * 256) is False  # 太长
    
    def test_project_exists(self, project_manager):
        """测试检查项目是否存在"""
        with patch.object(project_manager.db_manager, 'project_exists') as mock_exists:
            mock_exists.return_value = True
            
            exists = project_manager.project_exists("existing-project")
            
            assert exists is True
            mock_exists.assert_called_once_with("existing-project")
    
    def test_error_handling(self, project_manager):
        """测试错误处理"""
        # 测试数据库错误处理
        with patch.object(project_manager.db_manager, 'get_all_projects') as mock_get:
            mock_get.side_effect = Exception("数据库错误")
            
            with pytest.raises(Exception, match="数据库错误"):
                project_manager.get_all_projects()
    
    def test_project_directory_creation(self, project_manager, temp_dir):
        """测试项目目录创建"""
        project_id = "new-project"
        
        project_manager._create_project_directory(project_id)
        
        project_dir = os.path.join(temp_dir, project_id)
        assert os.path.exists(project_dir)
        
        # 检查子目录
        subdirs = ["chapters", "characters", "outlines", "resources"]
        for subdir in subdirs:
            subdir_path = os.path.join(project_dir, subdir)
            assert os.path.exists(subdir_path)
    
    def test_project_template_application(self, project_manager, sample_project):
        """测试项目模板应用"""
        with patch.object(project_manager, '_load_template') as mock_load:
            template_data = {
                "characters": [{"name": "主角", "role": "protagonist"}],
                "outline": {"title": "默认大纲"}
            }
            mock_load.return_value = template_data
            
            with patch.object(project_manager.db_manager, 'create_character') as mock_char:
                with patch.object(project_manager.db_manager, 'create_outline') as mock_outline:
                    project_manager._apply_project_template(sample_project, "novel")
                    
                    mock_char.assert_called_once()
                    mock_outline.assert_called_once()