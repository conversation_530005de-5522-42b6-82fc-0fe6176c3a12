# -*- coding: utf-8 -*-
"""
OpenAI服务测试
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch

from bamboofall.ai.openai_service import OpenAIService
from bamboofall.ai.ai_service_base import (
    AIConfig, AIMessage, MessageRole, AIResponse, AIModelType,
    AIServiceError, APIKeyError
)


class TestOpenAIService:
    """OpenAIService测试类"""
    
    @pytest.fixture
    def openai_config(self):
        """OpenAI配置fixture"""
        return AIConfig(
            api_key="test-openai-key",
            model="gpt-3.5-turbo",
            max_tokens=1000,
            temperature=0.7
        )
    
    @pytest.fixture
    def openai_service(self, openai_config):
        """OpenAIService实例fixture"""
        return OpenAIService(openai_config)
    
    def test_init(self, openai_service):
        """测试初始化"""
        assert openai_service.provider_name == "openai"
        assert openai_service.model_type == AIModelType.CHAT
        assert "gpt-3.5-turbo" in openai_service.supported_models
        assert "gpt-4" in openai_service.supported_models
        assert "gpt-4-turbo" in openai_service.supported_models
    
    def test_supported_models(self, openai_service):
        """测试支持的模型列表"""
        models = openai_service.supported_models
        expected_models = [
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k",
            "gpt-4",
            "gpt-4-32k",
            "gpt-4-turbo",
            "gpt-4-turbo-preview",
            "text-embedding-ada-002",
            "text-embedding-3-small",
            "text-embedding-3-large"
        ]
        
        for model in expected_models:
            assert model in models
    
    def test_validate_config_valid(self, openai_service):
        """测试有效配置验证"""
        assert openai_service.validate_config() is True
    
    def test_validate_config_invalid_api_key(self):
        """测试无效API密钥"""
        config = AIConfig(api_key="", model="gpt-3.5-turbo")
        service = OpenAIService(config)
        
        with pytest.raises(APIKeyError, match="OpenAI API密钥不能为空"):
            service.validate_config()
    
    def test_validate_config_invalid_model(self):
        """测试无效模型"""
        config = AIConfig(api_key="test-key", model="invalid-model")
        
        with pytest.raises(ValueError, match="不支持的模型"):
            OpenAIService(config)
    
    def test_convert_messages(self, openai_service):
        """测试消息转换"""
        messages = [
            AIMessage(role=MessageRole.SYSTEM, content="你是一个助手"),
            AIMessage(role=MessageRole.USER, content="你好"),
            AIMessage(role=MessageRole.ASSISTANT, content="你好！")
        ]
        
        converted_messages = openai_service._convert_messages(messages)
        
        # 检查转换后的消息
        assert len(converted_messages) == 3
        assert converted_messages[0]["role"] == "system"
        assert converted_messages[0]["content"] == "你是一个助手"
        assert converted_messages[1]["role"] == "user"
        assert converted_messages[1]["content"] == "你好"
        assert converted_messages[2]["role"] == "assistant"
        assert converted_messages[2]["content"] == "你好！"
    
    @patch('openai.OpenAI')
    async def test_chat(self, mock_openai_client, openai_service):
        """测试聊天功能"""
        # 设置mock响应
        mock_response = Mock()
        mock_response.choices = [Mock(message=Mock(content="这是GPT的回复"))]
        mock_response.model = "gpt-3.5-turbo"
        mock_response.usage = Mock(prompt_tokens=10, completion_tokens=20, total_tokens=30)
        mock_response.choices[0].finish_reason = "stop"
        
        mock_client = Mock()
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai_client.return_value = mock_client
        
        # 测试聊天
        messages = [
            AIMessage(role=MessageRole.USER, content="你好")
        ]
        
        response = await openai_service.chat(messages)
        
        # 验证响应
        assert isinstance(response, AIResponse)
        assert response.content == "这是GPT的回复"
        assert response.model == "gpt-3.5-turbo"
        assert response.usage["prompt_tokens"] == 10
        assert response.usage["completion_tokens"] == 20
        assert response.usage["total_tokens"] == 30
        assert response.finish_reason == "stop"
        
        # 验证API调用
        mock_client.chat.completions.create.assert_called_once()
        call_args = mock_client.chat.completions.create.call_args[1]
        assert call_args["model"] == "gpt-3.5-turbo"
        assert call_args["max_tokens"] == 1000
        assert call_args["temperature"] == 0.7
    
    @patch('openai.OpenAI')
    async def test_stream_chat(self, mock_openai_client, openai_service):
        """测试流式聊天"""
        # 设置mock流式响应
        def mock_stream():
            yield Mock(choices=[Mock(delta=Mock(content="你好"))])
            yield Mock(choices=[Mock(delta=Mock(content="，我是"))])
            yield Mock(choices=[Mock(delta=Mock(content="GPT"))])
        
        mock_client = Mock()
        mock_client.chat.completions.create.return_value = mock_stream()
        mock_openai_client.return_value = mock_client
        
        # 测试流式聊天
        messages = [
            AIMessage(role=MessageRole.USER, content="介绍一下你自己")
        ]
        
        chunks = []
        async for chunk in openai_service.stream_chat(messages):
            chunks.append(chunk)
        
        # 验证流式响应
        assert chunks == ["你好", "，我是", "GPT"]
        
        # 验证API调用
        mock_client.chat.completions.create.assert_called_once()
    
    async def test_complete(self, openai_service):
        """测试文本补全"""
        # 文本补全实际上调用chat方法
        with patch.object(openai_service, 'chat') as mock_chat:
            mock_response = AIResponse(
                content="补全的文本内容",
                model="gpt-3.5-turbo"
            )
            mock_chat.return_value = mock_response
            
            result = await openai_service.complete("请补全这段文字：")
            
            assert result == mock_response
            mock_chat.assert_called_once()
            
            # 检查传递给chat的消息
            call_args = mock_chat.call_args[0]
            messages = call_args[0]
            assert len(messages) == 1
            assert messages[0].role == MessageRole.USER
            assert messages[0].content == "请补全这段文字："
    
    @patch('openai.OpenAI')
    async def test_get_embedding(self, mock_openai_client, openai_service):
        """测试获取embedding"""
        # 设置mock响应
        mock_response = Mock()
        mock_response.data = [Mock(embedding=[0.1, 0.2, 0.3, 0.4])]
        
        mock_client = Mock()
        mock_client.embeddings.create.return_value = mock_response
        mock_openai_client.return_value = mock_client
        
        # 测试获取embedding
        result = await openai_service.get_embedding("测试文本")
        
        # 验证结果
        assert result == [0.1, 0.2, 0.3, 0.4]
        
        # 验证API调用
        mock_client.embeddings.create.assert_called_once()
        call_args = mock_client.embeddings.create.call_args[1]
        assert call_args["input"] == "测试文本"
        assert call_args["model"] == "text-embedding-ada-002"  # 默认embedding模型
    
    def test_get_model_info(self, openai_service):
        """测试获取模型信息"""
        # 测试有效模型
        info = openai_service.get_model_info("gpt-3.5-turbo")
        assert info is not None
        assert info["name"] == "GPT-3.5 Turbo"
        assert info["max_tokens"] == 4096
        assert "context_window" in info
        
        # 测试无效模型
        info = openai_service.get_model_info("invalid-model")
        assert info is None
    
    @patch('openai.OpenAI')
    async def test_test_connection_success(self, mock_openai_client, openai_service):
        """测试连接成功"""
        # 设置成功的mock响应
        mock_response = Mock()
        mock_response.choices = [Mock(message=Mock(content="Hello"))]
        
        mock_client = Mock()
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai_client.return_value = mock_client
        
        result = await openai_service.test_connection()
        assert result is True
    
    @patch('openai.OpenAI')
    async def test_test_connection_failure(self, mock_openai_client, openai_service):
        """测试连接失败"""
        # 设置失败的mock响应
        mock_client = Mock()
        mock_client.chat.completions.create.side_effect = Exception("连接失败")
        mock_openai_client.return_value = mock_client
        
        result = await openai_service.test_connection()
        assert result is False
    
    @patch('openai.OpenAI')
    async def test_chat_api_error(self, mock_openai_client, openai_service):
        """测试API错误处理"""
        # 设置API错误
        mock_client = Mock()
        mock_client.chat.completions.create.side_effect = Exception("API错误")
        mock_openai_client.return_value = mock_client
        
        messages = [
            AIMessage(role=MessageRole.USER, content="测试")
        ]
        
        with pytest.raises(AIServiceError, match="OpenAI API调用失败"):
            await openai_service.chat(messages)
    
    @patch('openai.OpenAI')
    async def test_embedding_api_error(self, mock_openai_client, openai_service):
        """测试Embedding API错误处理"""
        # 设置API错误
        mock_client = Mock()
        mock_client.embeddings.create.side_effect = Exception("Embedding API错误")
        mock_openai_client.return_value = mock_client
        
        with pytest.raises(AIServiceError, match="OpenAI Embedding API调用失败"):
            await openai_service.get_embedding("测试文本")
    
    def test_error_handling_invalid_response(self, openai_service):
        """测试无效响应处理"""
        # 测试处理无效响应的错误处理逻辑
        with patch.object(openai_service, '_handle_api_error') as mock_handler:
            mock_handler.return_value = AIServiceError("处理后的错误")
            
            error = openai_service._handle_api_error(Exception("原始错误"))
            assert isinstance(error, AIServiceError)
            assert "处理后的错误" in str(error)
    
    def test_embedding_model_selection(self, openai_service):
        """测试embedding模型选择"""
        # 测试默认embedding模型
        assert openai_service._get_embedding_model() == "text-embedding-ada-002"
        
        # 测试配置中指定embedding模型
        config = AIConfig(
            api_key="test-key",
            model="gpt-3.5-turbo",
            embedding_model="text-embedding-3-large"
        )
        service = OpenAIService(config)
        assert service._get_embedding_model() == "text-embedding-3-large"