"""异常处理工具函数模块

提供异常格式化、清理、分析等工具函数。
"""

import sys
import traceback
import re
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
import logging

from .base import BambooFallError, ErrorSeverity


def format_exception(exc: Exception, include_traceback: bool = True) -> Dict[str, Any]:
    """格式化异常信息
    
    Args:
        exc: 异常对象
        include_traceback: 是否包含堆栈跟踪
        
    Returns:
        格式化后的异常信息字典
    """
    exc_info = {
        'type': type(exc).__name__,
        'message': str(exc),
        'timestamp': datetime.now().isoformat(),
        'module': getattr(exc, '__module__', 'unknown')
    }
    
    # 如果是自定义异常，添加额外信息
    if isinstance(exc, BambooFallError):
        exc_info.update({
            'error_code': exc.error_code.value if exc.error_code else None,
            'severity': exc.severity.value if exc.severity else None,
            'details': exc.details,
            'user_message': exc.user_message,
            'cause': str(exc.cause) if exc.cause else None
        })
    
    # 添加堆栈跟踪
    if include_traceback:
        exc_info['traceback'] = traceback.format_exception(
            type(exc), exc, exc.__traceback__
        )
    
    return exc_info


def sanitize_exception_message(message: str, sensitive_patterns: Optional[List[str]] = None) -> str:
    """清理异常消息中的敏感信息
    
    Args:
        message: 原始异常消息
        sensitive_patterns: 敏感信息的正则表达式模式列表
        
    Returns:
        清理后的异常消息
    """
    if not message:
        return message
    
    # 默认敏感信息模式
    default_patterns = [
        r'password[\s]*[=:][\s]*[\S]+',  # 密码
        r'token[\s]*[=:][\s]*[\S]+',     # 令牌
        r'key[\s]*[=:][\s]*[\S]+',       # 密钥
        r'secret[\s]*[=:][\s]*[\S]+',    # 秘密
        r'api[_-]?key[\s]*[=:][\s]*[\S]+',  # API密钥
        r'\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b',  # 信用卡号
        r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # 邮箱
        r'\b\d{3}-\d{2}-\d{4}\b',  # SSN格式
    ]
    
    patterns = sensitive_patterns or default_patterns
    
    sanitized = message
    for pattern in patterns:
        sanitized = re.sub(pattern, '[REDACTED]', sanitized, flags=re.IGNORECASE)
    
    return sanitized


def get_exception_chain(exc: Exception) -> List[Exception]:
    """获取异常链
    
    Args:
        exc: 异常对象
        
    Returns:
        异常链列表，从根异常到当前异常
    """
    chain = []
    current = exc
    
    while current:
        chain.append(current)
        current = current.__cause__ or current.__context__
    
    return list(reversed(chain))  # 从根异常开始


def analyze_exception_severity(exc: Exception) -> ErrorSeverity:
    """分析异常严重程度
    
    Args:
        exc: 异常对象
        
    Returns:
        异常严重程度
    """
    # 如果是自定义异常且已设置严重程度
    if isinstance(exc, BambooFallError) and exc.severity:
        return exc.severity
    
    # 根据异常类型判断严重程度
    exc_type = type(exc).__name__
    
    # 严重错误
    critical_exceptions = {
        'SystemExit', 'KeyboardInterrupt', 'MemoryError',
        'RecursionError', 'SystemError'
    }
    
    # 高级错误
    high_exceptions = {
        'ImportError', 'ModuleNotFoundError', 'SyntaxError',
        'IndentationError', 'TabError', 'NameError',
        'UnboundLocalError', 'AttributeError'
    }
    
    # 中级错误
    medium_exceptions = {
        'TypeError', 'ValueError', 'KeyError', 'IndexError',
        'FileNotFoundError', 'PermissionError', 'ConnectionError',
        'TimeoutError', 'RuntimeError'
    }
    
    if exc_type in critical_exceptions:
        return ErrorSeverity.CRITICAL
    elif exc_type in high_exceptions:
        return ErrorSeverity.HIGH
    elif exc_type in medium_exceptions:
        return ErrorSeverity.MEDIUM
    else:
        return ErrorSeverity.LOW


def create_error_report(exc: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """创建详细的错误报告
    
    Args:
        exc: 异常对象
        context: 额外的上下文信息
        
    Returns:
        详细的错误报告
    """
    report = {
        'timestamp': datetime.now().isoformat(),
        'exception': format_exception(exc),
        'severity': analyze_exception_severity(exc).value,
        'chain': [format_exception(e, include_traceback=False) 
                 for e in get_exception_chain(exc)],
        'system_info': {
            'python_version': sys.version,
            'platform': sys.platform,
            'executable': sys.executable
        }
    }
    
    if context:
        report['context'] = context
    
    return report


def log_exception_with_context(logger: logging.Logger, exc: Exception, 
                              context: Optional[Dict[str, Any]] = None,
                              level: int = logging.ERROR) -> None:
    """记录带上下文的异常
    
    Args:
        logger: 日志记录器
        exc: 异常对象
        context: 上下文信息
        level: 日志级别
    """
    report = create_error_report(exc, context)
    
    # 清理敏感信息
    sanitized_message = sanitize_exception_message(str(exc))
    
    logger.log(
        level,
        f"Exception occurred: {sanitized_message}",
        extra={
            'exception_report': report,
            'exception_type': type(exc).__name__,
            'exception_severity': report['severity']
        },
        exc_info=True
    )


def is_recoverable_error(exc: Exception) -> bool:
    """判断异常是否可恢复
    
    Args:
        exc: 异常对象
        
    Returns:
        是否可恢复
    """
    # 不可恢复的异常类型
    unrecoverable_exceptions = {
        SystemExit, KeyboardInterrupt, MemoryError,
        RecursionError, SyntaxError, IndentationError,
        TabError, ImportError, ModuleNotFoundError
    }
    
    # 可能可恢复的异常类型
    recoverable_exceptions = {
        ConnectionError, TimeoutError, FileNotFoundError,
        PermissionError, ValueError, KeyError, IndexError
    }
    
    exc_type = type(exc)
    
    if exc_type in unrecoverable_exceptions:
        return False
    elif exc_type in recoverable_exceptions:
        return True
    
    # 对于自定义异常，根据严重程度判断
    if isinstance(exc, BambooFallError):
        return exc.severity in (ErrorSeverity.LOW, ErrorSeverity.MEDIUM)
    
    # 默认认为可恢复
    return True


def get_exception_suggestions(exc: Exception) -> List[str]:
    """获取异常处理建议
    
    Args:
        exc: 异常对象
        
    Returns:
        处理建议列表
    """
    suggestions = []
    exc_type = type(exc).__name__
    
    suggestion_map = {
        'FileNotFoundError': [
            '检查文件路径是否正确',
            '确认文件是否存在',
            '检查文件权限'
        ],
        'PermissionError': [
            '检查文件/目录权限',
            '以管理员身份运行程序',
            '确认用户有足够的访问权限'
        ],
        'ConnectionError': [
            '检查网络连接',
            '确认服务器地址正确',
            '检查防火墙设置',
            '稍后重试'
        ],
        'TimeoutError': [
            '增加超时时间',
            '检查网络延迟',
            '优化请求性能',
            '实现重试机制'
        ],
        'ValueError': [
            '检查输入参数的格式和范围',
            '验证数据类型',
            '添加输入验证'
        ],
        'KeyError': [
            '检查字典键是否存在',
            '使用get()方法提供默认值',
            '验证数据结构'
        ],
        'IndexError': [
            '检查列表/数组索引范围',
            '验证数据长度',
            '添加边界检查'
        ],
        'AttributeError': [
            '检查对象是否有该属性',
            '确认对象类型正确',
            '检查对象初始化'
        ],
        'ImportError': [
            '检查模块是否已安装',
            '确认模块名称正确',
            '检查Python路径设置'
        ]
    }
    
    suggestions = suggestion_map.get(exc_type, ['检查错误详情并相应处理'])
    
    # 如果是自定义异常，添加特定建议
    if isinstance(exc, BambooFallError) and exc.details:
        if 'suggestions' in exc.details:
            suggestions.extend(exc.details['suggestions'])
    
    return suggestions


def format_user_friendly_message(exc: Exception) -> str:
    """格式化用户友好的错误消息
    
    Args:
        exc: 异常对象
        
    Returns:
        用户友好的错误消息
    """
    # 如果是自定义异常且有用户消息
    if isinstance(exc, BambooFallError) and exc.user_message:
        return exc.user_message
    
    # 根据异常类型提供友好消息
    exc_type = type(exc).__name__
    
    friendly_messages = {
        'FileNotFoundError': '找不到指定的文件，请检查文件路径是否正确。',
        'PermissionError': '没有足够的权限访问文件或目录，请检查权限设置。',
        'ConnectionError': '网络连接失败，请检查网络设置或稍后重试。',
        'TimeoutError': '操作超时，请稍后重试或检查网络连接。',
        'ValueError': '输入的数据格式不正确，请检查输入内容。',
        'KeyError': '找不到指定的数据项，请检查数据完整性。',
        'IndexError': '数据索引超出范围，请检查数据长度。',
        'AttributeError': '对象缺少必要的属性或方法，请检查对象状态。',
        'ImportError': '无法导入必要的模块，请检查依赖安装。',
        'ModuleNotFoundError': '找不到指定的模块，请确认模块已正确安装。'
    }
    
    return friendly_messages.get(exc_type, f'发生了一个错误：{str(exc)}')