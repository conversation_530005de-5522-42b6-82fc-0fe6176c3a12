# 笔落App Python版本 代码规范和开发标准

## 文档信息
- **项目名称**：笔落App (Python版本)
- **规范版本**：1.0-Python
- **创建日期**：2025年8月30日
- **适用范围**：所有Python开发人员
- **技术栈**：Python + PyQt6 + SQLite

## 一、Python编码规范

### 1.1 命名规范

#### 1.1.1 文件和目录命名
```python
# ✅ 正确的文件命名
bamboofall/
├── ui/
│   ├── main_window.py        # snake_case
│   ├── dashboard_widget.py   # snake_case
│   └── story_bible_panel.py  # snake_case
├── core/
│   ├── project_manager.py    # snake_case
│   └── ai_service.py         # snake_case
└── models/
    ├── project.py            # 单数形式
    └── character.py          # 单数形式

# ❌ 错误的文件命名
├── MainWindow.py             # 避免PascalCase
├── dashboard-widget.py       # 避免kebab-case
└── storyBiblePanel.py        # 避免camelCase
```

#### 1.1.2 变量和函数命名
```python
# ✅ 正确的变量命名
user_name = "张三"                # snake_case
max_file_size = 1024 * 1024      # snake_case
is_user_logged_in = True         # boolean用is/has前缀

# ✅ 正确的函数命名
def create_project(name: str):    # 动词开头，snake_case
    pass

def get_user_by_id(user_id: str): # 动词开头，snake_case
    pass

def validate_input(input_text: str): # 动词开头，snake_case
    pass

# ❌ 错误的命名
userName = "张三"                 # 避免camelCase
MAX_file_size = 1024             # 混合命名风格
def project(name: str):          # 缺少动词
    pass
```

#### 1.1.3 类和接口命名
```python
# ✅ 正确的类命名
class ProjectManager:             # PascalCase
    def __init__(self):
        pass

class AIServiceProvider:          # PascalCase
    def generate_content(self):   # 方法用snake_case
        pass

class ModernTextEditor(QTextEdit): # 继承时也用PascalCase
    def setup_editor(self):
        pass

# ✅ 正确的数据类命名
@dataclass
class Project:                    # PascalCase
    id: str
    name: str
    created_at: datetime

# ❌ 错误的命名
class projectManager:             # 应该用PascalCase
    pass

class user_interface:             # 应该用PascalCase
    pass
```

#### 1.1.4 常量命名
```python
# ✅ 正确的常量命名
MAX_FILE_SIZE = 10 * 1024 * 1024  # UPPER_SNAKE_CASE
DEFAULT_THEME = "light"           # UPPER_SNAKE_CASE
API_ENDPOINTS = {                 # UPPER_SNAKE_CASE
    "OPENAI": "https://api.openai.com/v1",
    "CLAUDE": "https://api.anthropic.com"
}

# 配置常量
class Config:
    DATABASE_URL = "sqlite:///bamboofall.db"
    DEBUG = False
    LOG_LEVEL = "INFO"

# ❌ 错误的常量命名
maxFileSize = 1024               # 应该用UPPER_SNAKE_CASE
api_endpoints = {}               # 应该用UPPER_SNAKE_CASE
```

### 1.2 代码格式规范

#### 1.2.1 缩进和空格
```python
# ✅ 正确的缩进 (4个空格)
def create_project(options: Dict[str, Any]) -> Optional[Project]:
    if options.get("name"):
        project = Project(
            id=generate_id(),
            name=options["name"],
            type=options.get("type", "fantasy")
        )
        return project
    return None

# ✅ 正确的字典格式
config = {
    "api_url": "https://api.example.com",
    "timeout": 5000,
    "retries": 3,
    "headers": {
        "Content-Type": "application/json",
        "User-Agent": "BambooFall/1.0"
    }
}

# ✅ 正确的列表格式
supported_formats = [
    "txt",
    "pdf", 
    "docx",
    "epub"
]
```

#### 1.2.2 行长度和换行
```python
# ✅ 正确的长行处理 (最大88字符，遵循black标准)
def create_project_with_template(
    name: str,
    project_type: str,
    template_path: Optional[str] = None,
    description: Optional[str] = None
) -> Project:
    pass

# ✅ 正确的链式调用
result = (
    data
    .filter(lambda item: item.is_active)
    .map(lambda item: item.name)
    .sort()
    .join(", ")
)

# ✅ 正确的长字符串
error_message = (
    "创建项目失败：项目名称不能为空，"
    "请输入有效的项目名称后重试。"
)
```

### 1.3 注释规范

#### 1.3.1 文档字符串 (Docstring)
```python
def create_project(options: Dict[str, Any]) -> Optional[Project]:
    """创建新的小说项目
    
    Args:
        options: 项目创建选项字典
            - name (str): 项目名称
            - type (str): 项目类型 ('fantasy', 'urban', 'scifi')
            - template (str, optional): 项目模板路径
            - description (str, optional): 项目描述
    
    Returns:
        Project: 创建的项目对象，创建失败时返回None
    
    Raises:
        ValidationError: 当项目名称无效时抛出
        FileExistsError: 当项目已存在时抛出
    
    Example:
        >>> project = create_project({
        ...     "name": "我的小说",
        ...     "type": "fantasy"
        ... })
        >>> print(project.name)
        我的小说
    """
    pass

class ProjectManager:
    """项目管理器
    
    负责处理项目的创建、打开、保存等核心操作。
    支持多种项目类型和模板系统。
    
    Attributes:
        current_project (Optional[Project]): 当前打开的项目
        recent_projects (List[str]): 最近打开的项目ID列表
    """
    
    def __init__(self):
        self.current_project: Optional[Project] = None
        self.recent_projects: List[str] = []
```

#### 1.3.2 行内注释
```python
# ✅ 好的行内注释
MAX_RETRIES = 3  # API调用最大重试次数

# 检查用户权限
if user.has_permission("write"):
    # 执行写入操作，确保数据一致性
    await save_project(project)

# TODO: 实现自动保存功能
# FIXME: 修复内存泄漏问题  
# NOTE: 这里使用特殊算法优化性能
# WARNING: 此函数可能抛出网络异常

# ❌ 不好的注释
x = 3  # 设置x为3
i += 1  # i加1
```

## 二、PyQt6界面规范

### 2.1 界面组件规范
```python
# ✅ 正确的组件定义
class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_signals()
        self.setup_theme()
    
    def setup_ui(self):
        """设置界面布局"""
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        self.central_widget.setLayout(layout)
        
        # 添加组件
        self.title_label = QLabel("笔落App")
        self.create_button = QPushButton("创建项目")
        
        layout.addWidget(self.title_label)
        layout.addWidget(self.create_button)
    
    def setup_signals(self):
        """设置信号连接"""
        self.create_button.clicked.connect(self.on_create_clicked)
    
    def setup_theme(self):
        """设置主题样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #fafafa;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
            }
        """)
    
    def on_create_clicked(self):
        """创建按钮点击事件处理"""
        pass
```

### 2.2 自定义控件规范
```python
# ✅ 正确的自定义控件
class ProjectCardWidget(QWidget):
    """项目卡片控件"""
    
    # 信号定义
    clicked = pyqtSignal(str)  # 项目ID
    deleted = pyqtSignal(str)  # 项目ID
    
    def __init__(self, project: Project):
        super().__init__()
        self.project = project
        self.setup_ui()
        self.setup_style()
    
    def setup_ui(self):
        """设置控件布局"""
        layout = QVBoxLayout()
        
        # 项目标题
        self.title_label = QLabel(self.project.name)
        self.title_label.setObjectName("projectTitle")
        
        # 项目描述
        self.desc_label = QLabel(self.project.description or "暂无描述")
        self.desc_label.setObjectName("projectDescription")
        self.desc_label.setWordWrap(True)
        
        layout.addWidget(self.title_label)
        layout.addWidget(self.desc_label)
        self.setLayout(layout)
    
    def setup_style(self):
        """设置控件样式"""
        self.setStyleSheet("""
            ProjectCardWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 12px;
                padding: 20px;
                margin: 8px;
            }
            ProjectCardWidget:hover {
                border-color: #2196F3;
            }
            #projectTitle {
                font-size: 18px;
                font-weight: 600;
                color: #333;
                margin-bottom: 8px;
            }
            #projectDescription {
                color: #666;
                font-size: 14px;
                line-height: 1.4;
            }
        """)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.clicked.emit(self.project.id)
        super().mousePressEvent(event)
```

### 2.3 样式表规范
```python
# ✅ 统一的样式管理
class StyleSheet:
    """样式表管理类"""
    
    # 颜色定义
    COLORS = {
        "primary": "#2196F3",
        "secondary": "#757575", 
        "success": "#4CAF50",
        "warning": "#FF9800",
        "error": "#F44336",
        "background": "#fafafa",
        "surface": "#ffffff",
        "text_primary": "#333333",
        "text_secondary": "#666666"
    }
    
    # 字体定义
    FONTS = {
        "ui": "Source Han Sans CN, Microsoft YaHei UI, sans-serif",
        "editor": "JetBrains Mono, Consolas, monospace",
        "code": "Fira Code, Monaco, monospace"
    }
    
    @classmethod
    def get_button_style(cls, button_type: str = "primary") -> str:
        """获取按钮样式"""
        base_style = """
            QPushButton {{
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: 500;
                font-size: 14px;
                font-family: {font};
            }}
            QPushButton:hover {{
                opacity: 0.8;
            }}
        """.format(font=cls.FONTS["ui"])
        
        if button_type == "primary":
            return base_style + f"""
                QPushButton {{
                    background-color: {cls.COLORS["primary"]};
                    color: white;
                }}
            """
        elif button_type == "secondary":
            return base_style + f"""
                QPushButton {{
                    background-color: {cls.COLORS["secondary"]};
                    color: white;
                }}
            """
        return base_style
```

## 三、类型注解规范

### 3.1 函数类型注解
```python
from typing import Optional, List, Dict, Union, Tuple, Callable, Any
from datetime import datetime

# ✅ 正确的函数类型注解
def create_project(
    name: str,
    project_type: str,
    description: Optional[str] = None
) -> Project:
    """创建项目"""
    pass

async def generate_content(
    prompt: str,
    context: Dict[str, Any],
    model: str = "gpt-4"
) -> str:
    """生成内容"""
    pass

def process_chapters(
    chapters: List[Chapter]
) -> Tuple[int, Dict[str, int]]:
    """处理章节，返回总字数和各章节字数"""
    pass

# ✅ 回调函数类型注解
ProgressCallback = Callable[[int, int], None]  # (current, total) -> None

def export_project(
    project: Project,
    format_type: str,
    progress_callback: Optional[ProgressCallback] = None
) -> bool:
    """导出项目"""
    pass
```

### 3.2 类型别名和泛型
```python
from typing import TypeVar, Generic, Protocol

# ✅ 类型别名定义
ProjectID = str
UserID = str
ContentType = Union[str, bytes]
SettingsDict = Dict[str, Union[str, int, bool]]

# ✅ 泛型类型
T = TypeVar('T')

class Repository(Generic[T]):
    """通用仓储类"""
    
    def get_by_id(self, id: str) -> Optional[T]:
        pass
    
    def save(self, entity: T) -> T:
        pass
    
    def delete(self, id: str) -> bool:
        pass

# ✅ 协议定义
class Exportable(Protocol):
    """可导出协议"""
    
    def export(self, format_type: str) -> bytes:
        """导出数据"""
        pass
```

## 四、异常处理规范

### 4.1 自定义异常类
```python
# ✅ 自定义异常类定义
class BambooFallError(Exception):
    """笔落App基础异常类"""
    pass

class ProjectError(BambooFallError):
    """项目相关异常"""
    pass

class ProjectNotFoundError(ProjectError):
    """项目未找到异常"""
    
    def __init__(self, project_id: str):
        self.project_id = project_id
        super().__init__(f"项目未找到: {project_id}")

class AIServiceError(BambooFallError):
    """AI服务异常"""
    
    def __init__(self, message: str, service_name: str, error_code: Optional[str] = None):
        self.service_name = service_name
        self.error_code = error_code
        super().__init__(f"{service_name} 服务错误: {message}")

class ValidationError(BambooFallError):
    """数据验证异常"""
    
    def __init__(self, field: str, message: str):
        self.field = field
        super().__init__(f"验证失败 [{field}]: {message}")
```

### 4.2 异常处理最佳实践
```python
# ✅ 正确的异常处理
async def save_project(project: Project) -> None:
    """保存项目"""
    try:
        # 验证项目数据
        validate_project(project)
        
        # 保存到数据库
        await project_repository.save(project)
        
        # 保存文件
        await file_service.save_project_files(project)
        
        logger.info(f"项目保存成功: {project.name}")
        
    except ValidationError as e:
        logger.error(f"项目验证失败: {e}")
        raise  # 重新抛出，让上层处理
        
    except DatabaseError as e:
        logger.error(f"数据库保存失败: {e}")
        # 回滚操作
        await project_repository.rollback()
        raise ProjectError(f"保存项目失败: {e}") from e
        
    except Exception as e:
        logger.error(f"保存项目时发生未知错误: {e}")
        raise ProjectError("保存项目时发生未知错误") from e

# ✅ UI层异常处理
class ProjectManager:
    def create_project(self, name: str, project_type: str) -> None:
        """创建项目的UI处理"""
        try:
            project = self.project_service.create_project(name, project_type)
            self.show_success_message(f"项目 '{name}' 创建成功")
            self.open_project(project)
            
        except ValidationError as e:
            self.show_error_message(f"输入验证失败: {e}")
            
        except ProjectError as e:
            self.show_error_message(f"创建项目失败: {e}")
            
        except Exception as e:
            logger.error(f"创建项目时发生未知错误: {e}")
            self.show_error_message("创建项目失败，请稍后重试")
```

## 五、测试规范

### 5.1 单元测试
```python
import pytest
from unittest.mock import Mock, patch, AsyncMock
from bamboofall.core.project_manager import ProjectManager
from bamboofall.models.project import Project

class TestProjectManager:
    """项目管理器测试类"""
    
    @pytest.fixture
    def project_manager(self):
        """测试夹具：项目管理器实例"""
        return ProjectManager()
    
    @pytest.fixture
    def sample_project(self):
        """测试夹具：示例项目"""
        return Project(
            id="test-001",
            name="测试项目",
            type="fantasy",
            description="这是一个测试项目"
        )
    
    def test_create_project_success(self, project_manager):
        """测试成功创建项目"""
        # Given
        name = "新项目"
        project_type = "fantasy"
        
        # When
        project = project_manager.create_project(name, project_type)
        
        # Then
        assert project is not None
        assert project.name == name
        assert project.type == project_type
        assert project.id is not None
    
    def test_create_project_with_empty_name_should_raise_error(self, project_manager):
        """测试空名称创建项目应该抛出异常"""
        # Given
        name = ""
        project_type = "fantasy"
        
        # When & Then
        with pytest.raises(ValidationError) as exc_info:
            project_manager.create_project(name, project_type)
        
        assert "项目名称不能为空" in str(exc_info.value)
    
    @patch('bamboofall.core.project_manager.database_service')
    def test_save_project_database_error(self, mock_db, project_manager, sample_project):
        """测试数据库错误时的处理"""
        # Given
        mock_db.save.side_effect = DatabaseError("连接失败")
        
        # When & Then
        with pytest.raises(ProjectError):
            project_manager.save_project(sample_project)
    
    @pytest.mark.asyncio
    async def test_async_load_project(self, project_manager):
        """测试异步加载项目"""
        # Given
        project_id = "test-001"
        
        # When
        with patch.object(project_manager, 'async_service', new_callable=AsyncMock) as mock_service:
            mock_service.load_project.return_value = Mock(id=project_id)
            project = await project_manager.async_load_project(project_id)
        
        # Then
        assert project.id == project_id
        mock_service.load_project.assert_called_once_with(project_id)
```

### 5.2 UI测试
```python
import pytest
from PyQt6.QtCore import Qt
from PyQt6.QtTest import QTest
from PyQt6.QtWidgets import QApplication
from bamboofall.ui.main_window import MainWindow

@pytest.fixture(scope="session")
def qapp():
    """QApplication测试夹具"""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    yield app
    app.quit()

class TestMainWindow:
    """主窗口测试类"""
    
    @pytest.fixture
    def main_window(self, qapp):
        """主窗口测试夹具"""
        window = MainWindow()
        window.show()
        return window
    
    def test_window_title(self, main_window):
        """测试窗口标题"""
        assert "笔落App" in main_window.windowTitle()
    
    def test_create_button_click(self, main_window, qtbot):
        """测试创建按钮点击"""
        # Given
        create_button = main_window.create_button
        
        # When
        qtbot.mouseClick(create_button, Qt.MouseButton.LeftButton)
        
        # Then
        # 验证对话框是否打开等
        pass
    
    def test_keyboard_shortcut(self, main_window, qtbot):
        """测试键盘快捷键"""
        # When
        qtbot.keySequence(main_window, "Ctrl+N")
        
        # Then
        # 验证新建项目功能是否触发
        pass
```

## 六、性能优化规范

### 6.1 内存管理
```python
# ✅ 正确的内存管理
class ProjectManager:
    def __init__(self):
        self._project_cache: Dict[str, Project] = {}
        self._max_cache_size = 10
    
    def get_project(self, project_id: str) -> Optional[Project]:
        """获取项目，带缓存"""
        # 先检查缓存
        if project_id in self._project_cache:
            return self._project_cache[project_id]
        
        # 从数据库加载
        project = self._load_project_from_db(project_id)
        if project:
            self._add_to_cache(project_id, project)
        
        return project
    
    def _add_to_cache(self, project_id: str, project: Project):
        """添加到缓存，实现LRU策略"""
        if len(self._project_cache) >= self._max_cache_size:
            # 移除最旧的项目
            oldest_key = next(iter(self._project_cache))
            del self._project_cache[oldest_key]
        
        self._project_cache[project_id] = project
    
    def clear_cache(self):
        """清理缓存"""
        self._project_cache.clear()
```

### 6.2 异步编程
```python
# ✅ 正确的异步编程模式
import asyncio
from concurrent.futures import ThreadPoolExecutor

class AIService:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    async def generate_content_batch(self, prompts: List[str]) -> List[str]:
        """批量生成内容"""
        tasks = [
            self.generate_content(prompt) 
            for prompt in prompts
        ]
        
        # 并发执行，但限制并发数
        semaphore = asyncio.Semaphore(3)
        
        async def limited_generate(prompt: str) -> str:
            async with semaphore:
                return await self.generate_content(prompt)
        
        results = await asyncio.gather(*[
            limited_generate(prompt) for prompt in prompts
        ])
        
        return results
    
    async def cpu_intensive_task(self, data: Any) -> Any:
        """CPU密集型任务使用线程池"""
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(
            self.executor,
            self._process_data,
            data
        )
        return result
    
    def _process_data(self, data: Any) -> Any:
        """CPU密集型处理函数"""
        # 在线程池中执行的同步代码
        pass
```

## 七、日志和调试规范

### 7.1 日志配置
```python
import logging
import logging.config
from pathlib import Path

# ✅ 日志配置
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s'
        }
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'standard'
        },
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/bamboofall.log',
            'formatter': 'detailed'
        }
    },
    'loggers': {
        'bamboofall': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG',
            'propagate': False
        }
    }
}

def setup_logging():
    """设置日志"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.config.dictConfig(LOGGING_CONFIG)

# ✅ 日志使用示例
logger = logging.getLogger(__name__)

class ProjectService:
    def create_project(self, name: str) -> Project:
        logger.info(f"开始创建项目: {name}")
        
        try:
            project = Project(name=name)
            logger.debug(f"项目对象创建成功: {project.id}")
            
            self.save_project(project)
            logger.info(f"项目创建完成: {name} (ID: {project.id})")
            
            return project
            
        except Exception as e:
            logger.error(f"创建项目失败: {name}, 错误: {e}", exc_info=True)
            raise
```

### 7.2 调试工具
```python
# ✅ 调试装饰器
import functools
import time
from typing import Callable, Any

def debug_timer(func: Callable) -> Callable:
    """性能计时装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logger.debug(f"{func.__name__} 执行时间: {end_time - start_time:.4f}秒")
        return result
    
    return wrapper

def debug_params(func: Callable) -> Callable:
    """参数调试装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs) -> Any:
        logger.debug(f"{func.__name__} 调用参数: args={args}, kwargs={kwargs}")
        result = func(*args, **kwargs)
        logger.debug(f"{func.__name__} 返回值: {result}")
        return result
    
    return wrapper

# 使用示例
@debug_timer
@debug_params
def process_large_text(text: str) -> str:
    """处理大文本"""
    # 处理逻辑
    return processed_text
```

---

**代码规范和开发标准完成**

*本文档为笔落App Python版本提供了全面的代码规范和开发标准，确保代码质量和团队协作效率。*