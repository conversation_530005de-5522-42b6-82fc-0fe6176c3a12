"""
数据仓储模式实现

提供数据访问的抽象层，封装数据库操作
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_

from .models import ProjectORM, CharacterORM, ChapterORM, SceneORM
from .db_manager import get_database_manager
from ..models.project import Project, ProjectStatus, ProjectPriority
from ..models.character import Character, CharacterType
from ..models.chapter import Chapter, ChapterStatus
from ..models.scene import Scene
from ..utils.logger import LoggerMixin
from ..core.database_pool import get_database_pool
from ..core.performance_monitor import performance_monitor, database_monitor
from ..core.cache_manager import get_cache_manager


class BaseRepository(LoggerMixin):
    """基础仓储类"""
    
    def __init__(self):
        self.db_manager = get_database_manager()
        self.db_pool = get_database_pool()
        self.cache_manager = get_cache_manager()
        
        # 缓存配置
        self.cache_ttl = 300  # 5分钟缓存
        self.enable_cache = True
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        # 优先使用连接池
        if self.db_pool:
            return self.db_pool.get_session()
        return self.db_manager.get_session()
    
    def _get_cache_key(self, prefix: str, *args) -> str:
        """生成缓存键"""
        return f"{prefix}:{'_'.join(str(arg) for arg in args)}"
    
    def _cache_get(self, key: str) -> Optional[Any]:
        """从缓存获取数据"""
        if not self.enable_cache:
            return None
        return self.cache_manager.get(key)
    
    def _cache_set(self, key: str, value: Any, ttl: Optional[int] = None):
        """设置缓存数据"""
        if not self.enable_cache:
            return
        self.cache_manager.set(key, value, ttl=ttl or self.cache_ttl)


class ProjectRepository(BaseRepository):
    """项目仓储"""
    
    @performance_monitor(category="database")
    @database_monitor
    def create(self, project: Project) -> bool:
        """创建项目"""
        try:
            with self.get_session() as session:
                project_orm = ProjectORM(
                    id=project.id,
                    name=project.name,
                    description=project.description,
                    author=project.author,
                    genre=project.genre,
                    language=project.language,
                    target_audience=project.target_audience,
                    word_count_goal=project.word_count_goal,
                    current_word_count=project.current_word_count,
                    status=project.status.value,
                    priority=project.priority.value,
                    deadline=project.deadline,
                    created_at=project.created_at,
                    updated_at=project.updated_at,
                    tags=project.tags,
                    world_building=project.world_building,
                    notes=project.notes,
                    cover_image_path=project.cover_image_path
                )
                
                session.add(project_orm)
                session.commit()
                
                # 清理相关缓存
                self.cache_manager.invalidate_pattern("project_*")
                self.cache_manager.invalidate_pattern("all_projects")
                
                self.logger.info(f"项目已创建: {project.name}")
                return True
                
        except Exception as e:
            self.logger.error(f"创建项目失败: {e}")
            return False
    
    @performance_monitor(category="database")
    @database_monitor
    def get_by_id(self, project_id: str) -> Optional[Project]:
        """根据ID获取项目"""
        # 检查缓存
        cache_key = self._get_cache_key("project", project_id)
        cached_project = self._cache_get(cache_key)
        if cached_project:
            return cached_project
            
        try:
            with self.get_session() as session:
                project_orm = session.query(ProjectORM).filter(ProjectORM.id == project_id).first()
                if project_orm:
                    project = self._orm_to_model(project_orm)
                    # 缓存结果
                    self._cache_set(cache_key, project)
                    return project
                return None
                
        except Exception as e:
            self.logger.error(f"获取项目失败: {e}")
            return None
    
    @performance_monitor(category="database")
    @database_monitor
    def get_all(self, limit: int = 100, offset: int = 0) -> List[Project]:
        """获取所有项目"""
        # 检查缓存
        cache_key = self._get_cache_key("all_projects", limit, offset)
        cached_projects = self._cache_get(cache_key)
        if cached_projects:
            return cached_projects
            
        try:
            with self.get_session() as session:
                projects_orm = session.query(ProjectORM)\
                    .order_by(desc(ProjectORM.updated_at))\
                    .limit(limit)\
                    .offset(offset)\
                    .all()
                
                projects = [self._orm_to_model(p) for p in projects_orm]
                
                # 缓存结果
                self._cache_set(cache_key, projects, ttl=60)  # 1分钟缓存
                
                return projects
                
        except Exception as e:
            self.logger.error(f"获取项目列表失败: {e}")
            return []
    
    @performance_monitor(category="database")
    @database_monitor
    def update(self, project: Project) -> bool:
        """更新项目"""
        try:
            with self.get_session() as session:
                project_orm = session.query(ProjectORM).filter(ProjectORM.id == project.id).first()
                if not project_orm:
                    return False
                
                # 更新字段
                project_orm.name = project.name
                project_orm.description = project.description
                project_orm.author = project.author
                project_orm.genre = project.genre
                project_orm.language = project.language
                project_orm.target_audience = project.target_audience
                project_orm.word_count_goal = project.word_count_goal
                project_orm.current_word_count = project.current_word_count
                project_orm.status = project.status.value
                project_orm.priority = project.priority.value
                project_orm.deadline = project.deadline
                project_orm.updated_at = project.updated_at
                project_orm.tags = project.tags
                project_orm.world_building = project.world_building
                project_orm.notes = project.notes
                project_orm.cover_image_path = project.cover_image_path
                
                session.commit()
                
                # 清理相关缓存
                cache_key = self._get_cache_key("project", project.id)
                self.cache_manager.delete(cache_key)
                self.cache_manager.invalidate_pattern("all_projects*")
                
                self.logger.info(f"项目已更新: {project.name}")
                return True
                
        except Exception as e:
            self.logger.error(f"更新项目失败: {e}")
            return False
    
    @performance_monitor(category="database")
    @database_monitor
    def delete(self, project_id: str) -> bool:
        """删除项目"""
        try:
            with self.get_session() as session:
                project_orm = session.query(ProjectORM).filter(ProjectORM.id == project_id).first()
                if project_orm:
                    session.delete(project_orm)
                    session.commit()
                    
                    # 清理相关缓存
                    cache_key = self._get_cache_key("project", project_id)
                    self.cache_manager.delete(cache_key)
                    self.cache_manager.invalidate_pattern("all_projects*")
                    
                    self.logger.info(f"项目已删除: {project_id}")
                    return True
                return False
                
        except Exception as e:
            self.logger.error(f"删除项目失败: {e}")
            return False
    
    @performance_monitor(category="database")
    @database_monitor
    def search(self, keyword: str, limit: int = 50) -> List[Project]:
        """搜索项目"""
        try:
            with self.get_session() as session:
                projects_orm = session.query(ProjectORM)\
                    .filter(or_(
                        ProjectORM.name.contains(keyword),
                        ProjectORM.description.contains(keyword),
                        ProjectORM.author.contains(keyword),
                        ProjectORM.genre.contains(keyword)
                    ))\
                    .order_by(desc(ProjectORM.updated_at))\
                    .limit(limit)\
                    .all()
                
                return [self._orm_to_model(p) for p in projects_orm]
                
        except Exception as e:
            self.logger.error(f"搜索项目失败: {e}")
            return []
    
    @performance_monitor(category="database")
    @database_monitor
    def get_by_status(self, status: ProjectStatus) -> List[Project]:
        """根据状态获取项目"""
        try:
            with self.get_session() as session:
                projects_orm = session.query(ProjectORM)\
                    .filter(ProjectORM.status == status.value)\
                    .order_by(desc(ProjectORM.updated_at))\
                    .all()
                
                return [self._orm_to_model(p) for p in projects_orm]
                
        except Exception as e:
            self.logger.error(f"按状态获取项目失败: {e}")
            return []
    
    @performance_monitor(category="database")
    @database_monitor
    def get_recent(self, limit: int = 10) -> List[Project]:
        """获取最近项目"""
        # 检查缓存
        cache_key = self._get_cache_key("recent_projects", limit)
        cached_projects = self._cache_get(cache_key)
        if cached_projects:
            return cached_projects
            
        try:
            with self.get_session() as session:
                projects_orm = session.query(ProjectORM)\
                    .order_by(desc(ProjectORM.updated_at))\
                    .limit(limit)\
                    .all()
                
                projects = [self._orm_to_model(p) for p in projects_orm]
                
                # 缓存结果
                self._cache_set(cache_key, projects, ttl=60)  # 1分钟缓存
                
                return projects
                
        except Exception as e:
            self.logger.error(f"获取最近项目失败: {e}")
            return []
    
    def _orm_to_model(self, project_orm: ProjectORM) -> Project:
        """ORM转模型"""
        return Project(
            id=project_orm.id,
            name=project_orm.name,
            description=project_orm.description,
            author=project_orm.author,
            genre=project_orm.genre,
            language=project_orm.language,
            target_audience=project_orm.target_audience,
            word_count_goal=project_orm.word_count_goal,
            current_word_count=project_orm.current_word_count,
            status=ProjectStatus(project_orm.status),
            priority=ProjectPriority(project_orm.priority),
            deadline=project_orm.deadline,
            created_at=project_orm.created_at,
            updated_at=project_orm.updated_at,
            tags=project_orm.tags or [],
            world_building=project_orm.world_building,
            notes=project_orm.notes,
            cover_image_path=project_orm.cover_image_path
        )


class CharacterRepository(BaseRepository):
    """角色仓储"""
    
    @performance_monitor(category="database")
    @database_monitor
    def create(self, character: Character) -> bool:
        """创建角色"""
        try:
            with self.get_session() as session:
                character_orm = CharacterORM(
                    id=character.id,
                    project_id=character.project_id,
                    name=character.name,
                    description=character.description,
                    age=character.age,
                    gender=character.gender,
                    occupation=character.occupation,
                    personality_traits=character.personality_traits,
                    background=character.background,
                    abilities=character.abilities,
                    relationships=character.relationships,
                    goals=character.goals,
                    conflicts=character.conflicts,
                    arc_development=character.arc_development,
                    appearance=character.appearance,
                    role=character.role.value,
                    importance=character.importance,
                    created_at=character.created_at,
                    updated_at=character.updated_at,
                    tags=character.tags,
                    notes=character.notes,
                    image_path=character.image_path
                )
                
                session.add(character_orm)
                session.commit()
                
                # 清理相关缓存
                self.cache_manager.invalidate_pattern(f"characters_{character.project_id}*")
                
                self.logger.info(f"角色已创建: {character.name}")
                return True
                
        except Exception as e:
            self.logger.error(f"创建角色失败: {e}")
            return False
    
    @performance_monitor(category="database")
    @database_monitor
    def get_by_project(self, project_id: str) -> List[Character]:
        """获取项目的所有角色"""
        # 检查缓存
        cache_key = self._get_cache_key("characters", project_id)
        cached_characters = self._cache_get(cache_key)
        if cached_characters:
            return cached_characters
            
        try:
            with self.get_session() as session:
                characters_orm = session.query(CharacterORM)\
                    .filter(CharacterORM.project_id == project_id)\
                    .order_by(desc(CharacterORM.importance), asc(CharacterORM.name))\
                    .all()
                
                characters = [self._orm_to_model(c) for c in characters_orm]
                
                # 缓存结果
                self._cache_set(cache_key, characters, ttl=600)  # 10分钟缓存
                
                return characters
                
        except Exception as e:
            self.logger.error(f"获取项目角色失败: {e}")
            return []
    
    @performance_monitor(category="database")
    @database_monitor
    def get_by_id(self, character_id: str) -> Optional[Character]:
        """根据ID获取角色"""
        # 检查缓存
        cache_key = self._get_cache_key("character", character_id)
        cached_character = self._cache_get(cache_key)
        if cached_character:
            return cached_character
            
        try:
            with self.get_session() as session:
                character_orm = session.query(CharacterORM).filter(CharacterORM.id == character_id).first()
                if character_orm:
                    character = self._orm_to_model(character_orm)
                    # 缓存结果
                    self._cache_set(cache_key, character)
                    return character
                return None
                
        except Exception as e:
            self.logger.error(f"获取角色失败: {e}")
            return None
    
    @performance_monitor(category="database")
    @database_monitor
    def update(self, character: Character) -> bool:
        """更新角色"""
        try:
            with self.get_session() as session:
                character_orm = session.query(CharacterORM).filter(CharacterORM.id == character.id).first()
                if not character_orm:
                    return False
                
                # 更新字段
                character_orm.name = character.name
                character_orm.description = character.description
                character_orm.age = character.age
                character_orm.gender = character.gender
                character_orm.occupation = character.occupation
                character_orm.personality_traits = character.personality_traits
                character_orm.background = character.background
                character_orm.abilities = character.abilities
                character_orm.relationships = character.relationships
                character_orm.goals = character.goals
                character_orm.conflicts = character.conflicts
                character_orm.arc_development = character.arc_development
                character_orm.appearance = character.appearance
                character_orm.role = character.role.value
                character_orm.importance = character.importance
                character_orm.updated_at = character.updated_at
                character_orm.tags = character.tags
                character_orm.notes = character.notes
                character_orm.image_path = character.image_path
                
                session.commit()
                
                # 清理相关缓存
                cache_key = self._get_cache_key("character", character.id)
                self.cache_manager.delete(cache_key)
                self.cache_manager.invalidate_pattern(f"characters_{character.project_id}*")
                
                self.logger.info(f"角色已更新: {character.name}")
                return True
                
        except Exception as e:
            self.logger.error(f"更新角色失败: {e}")
            return False
    
    @performance_monitor(category="database")
    @database_monitor
    def delete(self, character_id: str) -> bool:
        """删除角色"""
        try:
            with self.get_session() as session:
                character_orm = session.query(CharacterORM).filter(CharacterORM.id == character_id).first()
                if character_orm:
                    project_id = character_orm.project_id
                    session.delete(character_orm)
                    session.commit()
                    
                    # 清理相关缓存
                    cache_key = self._get_cache_key("character", character_id)
                    self.cache_manager.delete(cache_key)
                    self.cache_manager.invalidate_pattern(f"characters_{project_id}*")
                    
                    self.logger.info(f"角色已删除: {character_id}")
                    return True
                return False
                
        except Exception as e:
            self.logger.error(f"删除角色失败: {e}")
            return False
    
    def _orm_to_model(self, character_orm: CharacterORM) -> Character:
        """ORM转模型"""
        return Character(
            id=character_orm.id,
            project_id=character_orm.project_id,
            name=character_orm.name,
            description=character_orm.description,
            age=character_orm.age,
            gender=character_orm.gender,
            occupation=character_orm.occupation,
            personality_traits=character_orm.personality_traits or [],
            background=character_orm.background,
            abilities=character_orm.abilities or [],
            relationships=character_orm.relationships or {},
            goals=character_orm.goals,
            conflicts=character_orm.conflicts,
            arc_development=character_orm.arc_development,
            appearance=character_orm.appearance,
            type=CharacterType(character_orm.role),
            importance=character_orm.importance,
            created_at=character_orm.created_at,
            updated_at=character_orm.updated_at,
            tags=character_orm.tags or [],
            notes=character_orm.notes,
            image_path=character_orm.image_path
        )


class ChapterRepository(BaseRepository):
    """章节仓储"""
    
    @performance_monitor(category="database")
    @database_monitor
    def create(self, chapter: Chapter) -> bool:
        """创建章节"""
        try:
            with self.get_session() as session:
                chapter_orm = ChapterORM(
                    id=chapter.id,
                    project_id=chapter.project_id,
                    title=chapter.title,
                    content=chapter.content,
                    summary=chapter.summary,
                    status=chapter.status.value,
                    order_index=chapter.order_index,
                    word_count=chapter.word_count,
                    character_count=chapter.character_count,
                    paragraph_count=chapter.paragraph_count,
                    scene_ids=chapter.scene_ids,
                    character_ids=chapter.character_ids,
                    created_at=chapter.created_at,
                    updated_at=chapter.updated_at,
                    target_word_count=chapter.target_word_count,
                    tags=chapter.tags,
                    notes=chapter.notes,
                    outline_node_id=chapter.outline_node_id
                )
                
                session.add(chapter_orm)
                session.commit()
                
                # 清理相关缓存
                self.cache_manager.invalidate_pattern(f"chapters_{chapter.project_id}*")
                
                self.logger.info(f"章节已创建: {chapter.title}")
                return True
                
        except Exception as e:
            self.logger.error(f"创建章节失败: {e}")
            return False
    
    @performance_monitor(category="database")
    @database_monitor
    def get_by_project(self, project_id: str) -> List[Chapter]:
        """获取项目的所有章节"""
        # 检查缓存
        cache_key = self._get_cache_key("chapters", project_id)
        cached_chapters = self._cache_get(cache_key)
        if cached_chapters:
            return cached_chapters
            
        try:
            with self.get_session() as session:
                chapters_orm = session.query(ChapterORM)\
                    .filter(ChapterORM.project_id == project_id)\
                    .order_by(asc(ChapterORM.order_index))\
                    .all()
                
                chapters = [self._orm_to_model(c) for c in chapters_orm]
                
                # 缓存结果
                self._cache_set(cache_key, chapters, ttl=600)  # 10分钟缓存
                
                return chapters
                
        except Exception as e:
            self.logger.error(f"获取项目章节失败: {e}")
            return []
    
    @performance_monitor(category="database")
    @database_monitor
    def get_by_id(self, chapter_id: str) -> Optional[Chapter]:
        """根据ID获取章节"""
        # 检查缓存
        cache_key = self._get_cache_key("chapter", chapter_id)
        cached_chapter = self._cache_get(cache_key)
        if cached_chapter:
            return cached_chapter
            
        try:
            with self.get_session() as session:
                chapter_orm = session.query(ChapterORM).filter(ChapterORM.id == chapter_id).first()
                if chapter_orm:
                    chapter = self._orm_to_model(chapter_orm)
                    # 缓存结果
                    self._cache_set(cache_key, chapter)
                    return chapter
                return None
                
        except Exception as e:
            self.logger.error(f"获取章节失败: {e}")
            return None
    
    @performance_monitor(category="database")
    @database_monitor
    def update(self, chapter: Chapter) -> bool:
        """更新章节"""
        try:
            with self.get_session() as session:
                chapter_orm = session.query(ChapterORM).filter(ChapterORM.id == chapter.id).first()
                if not chapter_orm:
                    return False
                
                # 更新字段
                chapter_orm.title = chapter.title
                chapter_orm.content = chapter.content
                chapter_orm.summary = chapter.summary
                chapter_orm.status = chapter.status.value
                chapter_orm.order_index = chapter.order_index
                chapter_orm.word_count = chapter.word_count
                chapter_orm.character_count = chapter.character_count
                chapter_orm.paragraph_count = chapter.paragraph_count
                chapter_orm.scene_ids = chapter.scene_ids
                chapter_orm.character_ids = chapter.character_ids
                chapter_orm.updated_at = chapter.updated_at
                chapter_orm.target_word_count = chapter.target_word_count
                chapter_orm.tags = chapter.tags
                chapter_orm.notes = chapter.notes
                chapter_orm.outline_node_id = chapter.outline_node_id
                
                session.commit()
                
                # 清理相关缓存
                cache_key = self._get_cache_key("chapter", chapter.id)
                self.cache_manager.delete(cache_key)
                self.cache_manager.invalidate_pattern(f"chapters_{chapter.project_id}*")
                
                self.logger.info(f"章节已更新: {chapter.title}")
                return True
                
        except Exception as e:
            self.logger.error(f"更新章节失败: {e}")
            return False
    
    @performance_monitor(category="database")
    @database_monitor
    def delete(self, chapter_id: str) -> bool:
        """删除章节"""
        try:
            with self.get_session() as session:
                chapter_orm = session.query(ChapterORM).filter(ChapterORM.id == chapter_id).first()
                if chapter_orm:
                    project_id = chapter_orm.project_id
                    session.delete(chapter_orm)
                    session.commit()
                    
                    # 清理相关缓存
                    cache_key = self._get_cache_key("chapter", chapter_id)
                    self.cache_manager.delete(cache_key)
                    self.cache_manager.invalidate_pattern(f"chapters_{project_id}*")
                    
                    self.logger.info(f"章节已删除: {chapter_id}")
                    return True
                return False
                
        except Exception as e:
            self.logger.error(f"删除章节失败: {e}")
            return False
    
    def _orm_to_model(self, chapter_orm: ChapterORM) -> Chapter:
        """ORM转模型"""
        return Chapter(
            id=chapter_orm.id,
            project_id=chapter_orm.project_id,
            title=chapter_orm.title,
            content=chapter_orm.content,
            summary=chapter_orm.summary,
            status=ChapterStatus(chapter_orm.status),
            order_index=chapter_orm.order_index,
            word_count=chapter_orm.word_count,
            character_count=chapter_orm.character_count,
            paragraph_count=chapter_orm.paragraph_count,
            scene_ids=chapter_orm.scene_ids or [],
            character_ids=chapter_orm.character_ids or [],
            created_at=chapter_orm.created_at,
            updated_at=chapter_orm.updated_at,
            target_word_count=chapter_orm.target_word_count,
            tags=chapter_orm.tags or [],
            notes=chapter_orm.notes,
            outline_node_id=chapter_orm.outline_node_id
        )
