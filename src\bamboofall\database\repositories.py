"""
数据仓储模式实现

提供数据访问的抽象层，封装数据库操作
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_

from .models import ProjectORM, CharacterORM, ChapterORM, SceneORM
from .db_manager import get_database_manager
from ..models.project import Project, ProjectStatus, ProjectPriority
from ..models.character import Character, CharacterType
from ..models.chapter import Chapter, ChapterStatus
from ..models.scene import Scene
from ..utils.logger import LoggerMixin


class BaseRepository(LoggerMixin):
    """基础仓储类"""
    
    def __init__(self):
        self.db_manager = get_database_manager()
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.db_manager.get_session()


class ProjectRepository(BaseRepository):
    """项目仓储"""
    
    def create(self, project: Project) -> bool:
        """创建项目"""
        try:
            with self.get_session() as session:
                project_orm = ProjectORM(
                    id=project.id,
                    name=project.name,
                    description=project.description,
                    author=project.author,
                    genre=project.genre,
                    language=project.language,
                    target_audience=project.target_audience,
                    word_count_goal=project.word_count_goal,
                    current_word_count=project.current_word_count,
                    status=project.status.value,
                    priority=project.priority.value,
                    deadline=project.deadline,
                    created_at=project.created_at,
                    updated_at=project.updated_at,
                    tags=project.tags,
                    world_building=project.world_building,
                    notes=project.notes,
                    cover_image_path=project.cover_image_path
                )
                
                session.add(project_orm)
                session.commit()
                self.logger.info(f"项目已创建: {project.name}")
                return True
                
        except Exception as e:
            self.logger.error(f"创建项目失败: {e}")
            return False
    
    def get_by_id(self, project_id: str) -> Optional[Project]:
        """根据ID获取项目"""
        try:
            with self.get_session() as session:
                project_orm = session.query(ProjectORM).filter(ProjectORM.id == project_id).first()
                if project_orm:
                    return self._orm_to_model(project_orm)
                return None
                
        except Exception as e:
            self.logger.error(f"获取项目失败: {e}")
            return None
    
    def get_all(self, limit: int = 100, offset: int = 0) -> List[Project]:
        """获取所有项目"""
        try:
            with self.get_session() as session:
                projects_orm = session.query(ProjectORM)\
                    .order_by(desc(ProjectORM.updated_at))\
                    .limit(limit)\
                    .offset(offset)\
                    .all()
                
                return [self._orm_to_model(p) for p in projects_orm]
                
        except Exception as e:
            self.logger.error(f"获取项目列表失败: {e}")
            return []
    
    def update(self, project: Project) -> bool:
        """更新项目"""
        try:
            with self.get_session() as session:
                project_orm = session.query(ProjectORM).filter(ProjectORM.id == project.id).first()
                if not project_orm:
                    return False
                
                # 更新字段
                project_orm.name = project.name
                project_orm.description = project.description
                project_orm.author = project.author
                project_orm.genre = project.genre
                project_orm.language = project.language
                project_orm.target_audience = project.target_audience
                project_orm.word_count_goal = project.word_count_goal
                project_orm.current_word_count = project.current_word_count
                project_orm.status = project.status.value
                project_orm.priority = project.priority.value
                project_orm.deadline = project.deadline
                project_orm.updated_at = project.updated_at
                project_orm.tags = project.tags
                project_orm.world_building = project.world_building
                project_orm.notes = project.notes
                project_orm.cover_image_path = project.cover_image_path
                
                session.commit()
                self.logger.info(f"项目已更新: {project.name}")
                return True
                
        except Exception as e:
            self.logger.error(f"更新项目失败: {e}")
            return False
    
    def delete(self, project_id: str) -> bool:
        """删除项目"""
        try:
            with self.get_session() as session:
                project_orm = session.query(ProjectORM).filter(ProjectORM.id == project_id).first()
                if project_orm:
                    session.delete(project_orm)
                    session.commit()
                    self.logger.info(f"项目已删除: {project_id}")
                    return True
                return False
                
        except Exception as e:
            self.logger.error(f"删除项目失败: {e}")
            return False
    
    def search(self, keyword: str, limit: int = 50) -> List[Project]:
        """搜索项目"""
        try:
            with self.get_session() as session:
                projects_orm = session.query(ProjectORM)\
                    .filter(or_(
                        ProjectORM.name.contains(keyword),
                        ProjectORM.description.contains(keyword),
                        ProjectORM.author.contains(keyword),
                        ProjectORM.genre.contains(keyword)
                    ))\
                    .order_by(desc(ProjectORM.updated_at))\
                    .limit(limit)\
                    .all()
                
                return [self._orm_to_model(p) for p in projects_orm]
                
        except Exception as e:
            self.logger.error(f"搜索项目失败: {e}")
            return []
    
    def get_by_status(self, status: ProjectStatus) -> List[Project]:
        """根据状态获取项目"""
        try:
            with self.get_session() as session:
                projects_orm = session.query(ProjectORM)\
                    .filter(ProjectORM.status == status.value)\
                    .order_by(desc(ProjectORM.updated_at))\
                    .all()
                
                return [self._orm_to_model(p) for p in projects_orm]
                
        except Exception as e:
            self.logger.error(f"按状态获取项目失败: {e}")
            return []
    
    def get_recent(self, limit: int = 10) -> List[Project]:
        """获取最近项目"""
        try:
            with self.get_session() as session:
                projects_orm = session.query(ProjectORM)\
                    .order_by(desc(ProjectORM.updated_at))\
                    .limit(limit)\
                    .all()
                
                return [self._orm_to_model(p) for p in projects_orm]
                
        except Exception as e:
            self.logger.error(f"获取最近项目失败: {e}")
            return []
    
    def _orm_to_model(self, project_orm: ProjectORM) -> Project:
        """ORM转模型"""
        return Project(
            id=project_orm.id,
            name=project_orm.name,
            description=project_orm.description,
            author=project_orm.author,
            genre=project_orm.genre,
            language=project_orm.language,
            target_audience=project_orm.target_audience,
            word_count_goal=project_orm.word_count_goal,
            current_word_count=project_orm.current_word_count,
            status=ProjectStatus(project_orm.status),
            priority=ProjectPriority(project_orm.priority),
            deadline=project_orm.deadline,
            created_at=project_orm.created_at,
            updated_at=project_orm.updated_at,
            tags=project_orm.tags or [],
            world_building=project_orm.world_building,
            notes=project_orm.notes,
            cover_image_path=project_orm.cover_image_path
        )


class CharacterRepository(BaseRepository):
    """角色仓储"""
    
    def create(self, character: Character) -> bool:
        """创建角色"""
        try:
            with self.get_session() as session:
                character_orm = CharacterORM(
                    id=character.id,
                    project_id=character.project_id,
                    name=character.name,
                    description=character.description,
                    age=character.age,
                    gender=character.gender,
                    occupation=character.occupation,
                    personality_traits=character.personality_traits,
                    background=character.background,
                    abilities=character.abilities,
                    relationships=character.relationships,
                    goals=character.goals,
                    conflicts=character.conflicts,
                    arc_development=character.arc_development,
                    appearance=character.appearance,
                    role=character.role.value,
                    importance=character.importance,
                    created_at=character.created_at,
                    updated_at=character.updated_at,
                    tags=character.tags,
                    notes=character.notes,
                    image_path=character.image_path
                )
                
                session.add(character_orm)
                session.commit()
                self.logger.info(f"角色已创建: {character.name}")
                return True
                
        except Exception as e:
            self.logger.error(f"创建角色失败: {e}")
            return False
    
    def get_by_project(self, project_id: str) -> List[Character]:
        """获取项目的所有角色"""
        try:
            with self.get_session() as session:
                characters_orm = session.query(CharacterORM)\
                    .filter(CharacterORM.project_id == project_id)\
                    .order_by(desc(CharacterORM.importance), asc(CharacterORM.name))\
                    .all()
                
                return [self._orm_to_model(c) for c in characters_orm]
                
        except Exception as e:
            self.logger.error(f"获取项目角色失败: {e}")
            return []
    
    def get_by_id(self, character_id: str) -> Optional[Character]:
        """根据ID获取角色"""
        try:
            with self.get_session() as session:
                character_orm = session.query(CharacterORM).filter(CharacterORM.id == character_id).first()
                if character_orm:
                    return self._orm_to_model(character_orm)
                return None
                
        except Exception as e:
            self.logger.error(f"获取角色失败: {e}")
            return None
    
    def update(self, character: Character) -> bool:
        """更新角色"""
        try:
            with self.get_session() as session:
                character_orm = session.query(CharacterORM).filter(CharacterORM.id == character.id).first()
                if not character_orm:
                    return False
                
                # 更新字段
                character_orm.name = character.name
                character_orm.description = character.description
                character_orm.age = character.age
                character_orm.gender = character.gender
                character_orm.occupation = character.occupation
                character_orm.personality_traits = character.personality_traits
                character_orm.background = character.background
                character_orm.abilities = character.abilities
                character_orm.relationships = character.relationships
                character_orm.goals = character.goals
                character_orm.conflicts = character.conflicts
                character_orm.arc_development = character.arc_development
                character_orm.appearance = character.appearance
                character_orm.role = character.role.value
                character_orm.importance = character.importance
                character_orm.updated_at = character.updated_at
                character_orm.tags = character.tags
                character_orm.notes = character.notes
                character_orm.image_path = character.image_path
                
                session.commit()
                self.logger.info(f"角色已更新: {character.name}")
                return True
                
        except Exception as e:
            self.logger.error(f"更新角色失败: {e}")
            return False
    
    def delete(self, character_id: str) -> bool:
        """删除角色"""
        try:
            with self.get_session() as session:
                character_orm = session.query(CharacterORM).filter(CharacterORM.id == character_id).first()
                if character_orm:
                    session.delete(character_orm)
                    session.commit()
                    self.logger.info(f"角色已删除: {character_id}")
                    return True
                return False
                
        except Exception as e:
            self.logger.error(f"删除角色失败: {e}")
            return False
    
    def _orm_to_model(self, character_orm: CharacterORM) -> Character:
        """ORM转模型"""
        return Character(
            id=character_orm.id,
            project_id=character_orm.project_id,
            name=character_orm.name,
            description=character_orm.description,
            age=character_orm.age,
            gender=character_orm.gender,
            occupation=character_orm.occupation,
            personality_traits=character_orm.personality_traits or [],
            background=character_orm.background,
            abilities=character_orm.abilities or [],
            relationships=character_orm.relationships or {},
            goals=character_orm.goals,
            conflicts=character_orm.conflicts,
            arc_development=character_orm.arc_development,
            appearance=character_orm.appearance,
            type=CharacterType(character_orm.role),
            importance=character_orm.importance,
            created_at=character_orm.created_at,
            updated_at=character_orm.updated_at,
            tags=character_orm.tags or [],
            notes=character_orm.notes,
            image_path=character_orm.image_path
        )


class ChapterRepository(BaseRepository):
    """章节仓储"""
    
    def create(self, chapter: Chapter) -> bool:
        """创建章节"""
        try:
            with self.get_session() as session:
                chapter_orm = ChapterORM(
                    id=chapter.id,
                    project_id=chapter.project_id,
                    title=chapter.title,
                    content=chapter.content,
                    summary=chapter.summary,
                    status=chapter.status.value,
                    order_index=chapter.order_index,
                    word_count=chapter.word_count,
                    character_count=chapter.character_count,
                    paragraph_count=chapter.paragraph_count,
                    scene_ids=chapter.scene_ids,
                    character_ids=chapter.character_ids,
                    created_at=chapter.created_at,
                    updated_at=chapter.updated_at,
                    target_word_count=chapter.target_word_count,
                    tags=chapter.tags,
                    notes=chapter.notes,
                    outline_node_id=chapter.outline_node_id
                )
                
                session.add(chapter_orm)
                session.commit()
                self.logger.info(f"章节已创建: {chapter.title}")
                return True
                
        except Exception as e:
            self.logger.error(f"创建章节失败: {e}")
            return False
    
    def get_by_project(self, project_id: str) -> List[Chapter]:
        """获取项目的所有章节"""
        try:
            with self.get_session() as session:
                chapters_orm = session.query(ChapterORM)\
                    .filter(ChapterORM.project_id == project_id)\
                    .order_by(asc(ChapterORM.order_index))\
                    .all()
                
                return [self._orm_to_model(c) for c in chapters_orm]
                
        except Exception as e:
            self.logger.error(f"获取项目章节失败: {e}")
            return []
    
    def get_by_id(self, chapter_id: str) -> Optional[Chapter]:
        """根据ID获取章节"""
        try:
            with self.get_session() as session:
                chapter_orm = session.query(ChapterORM).filter(ChapterORM.id == chapter_id).first()
                if chapter_orm:
                    return self._orm_to_model(chapter_orm)
                return None
                
        except Exception as e:
            self.logger.error(f"获取章节失败: {e}")
            return None
    
    def update(self, chapter: Chapter) -> bool:
        """更新章节"""
        try:
            with self.get_session() as session:
                chapter_orm = session.query(ChapterORM).filter(ChapterORM.id == chapter.id).first()
                if not chapter_orm:
                    return False
                
                # 更新字段
                chapter_orm.title = chapter.title
                chapter_orm.content = chapter.content
                chapter_orm.summary = chapter.summary
                chapter_orm.status = chapter.status.value
                chapter_orm.order_index = chapter.order_index
                chapter_orm.word_count = chapter.word_count
                chapter_orm.character_count = chapter.character_count
                chapter_orm.paragraph_count = chapter.paragraph_count
                chapter_orm.scene_ids = chapter.scene_ids
                chapter_orm.character_ids = chapter.character_ids
                chapter_orm.updated_at = chapter.updated_at
                chapter_orm.target_word_count = chapter.target_word_count
                chapter_orm.tags = chapter.tags
                chapter_orm.notes = chapter.notes
                chapter_orm.outline_node_id = chapter.outline_node_id
                
                session.commit()
                self.logger.info(f"章节已更新: {chapter.title}")
                return True
                
        except Exception as e:
            self.logger.error(f"更新章节失败: {e}")
            return False
    
    def delete(self, chapter_id: str) -> bool:
        """删除章节"""
        try:
            with self.get_session() as session:
                chapter_orm = session.query(ChapterORM).filter(ChapterORM.id == chapter_id).first()
                if chapter_orm:
                    session.delete(chapter_orm)
                    session.commit()
                    self.logger.info(f"章节已删除: {chapter_id}")
                    return True
                return False
                
        except Exception as e:
            self.logger.error(f"删除章节失败: {e}")
            return False
    
    def _orm_to_model(self, chapter_orm: ChapterORM) -> Chapter:
        """ORM转模型"""
        return Chapter(
            id=chapter_orm.id,
            project_id=chapter_orm.project_id,
            title=chapter_orm.title,
            content=chapter_orm.content,
            summary=chapter_orm.summary,
            status=ChapterStatus(chapter_orm.status),
            order_index=chapter_orm.order_index,
            word_count=chapter_orm.word_count,
            character_count=chapter_orm.character_count,
            paragraph_count=chapter_orm.paragraph_count,
            scene_ids=chapter_orm.scene_ids or [],
            character_ids=chapter_orm.character_ids or [],
            created_at=chapter_orm.created_at,
            updated_at=chapter_orm.updated_at,
            target_word_count=chapter_orm.target_word_count,
            tags=chapter_orm.tags or [],
            notes=chapter_orm.notes,
            outline_node_id=chapter_orm.outline_node_id
        )
