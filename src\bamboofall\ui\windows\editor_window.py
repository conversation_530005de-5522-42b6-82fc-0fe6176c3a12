"""编辑器窗口类

专门用于文本编辑功能的独立窗口。
"""

from typing import Optional, Dict, Any, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter, QTextEdit,
    QLabel, QPushButton, QFrame, QToolButton, QMenu, QMenuBar,
    QStatusBar, QProgressBar, QComboBox, QSpinBox, QCheckBox,
    QGroupBox, QSlider, QTabWidget, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, pyqtSlot
from PyQt6.QtGui import QAction, QIcon, QFont, QTextCursor, QTextCharFormat, QColor
import os
from pathlib import Path

from .base_window import BaseWindow
from ..editor.text_editor_widget import TextEditorWidget
from ..editor.markdown_preview import MarkdownPreview
from ..widgets.modern_widgets import ModernButton
from ...services.ai_service import AIService
from ...utils.config_utils import get_config_manager
from ...exceptions import handle_exceptions, AIServiceError


class EditorWindow(BaseWindow):
    """编辑器窗口类
    
    功能：
    - 专业文本编辑
    - Markdown预览
    - AI辅助写作
    - 写作统计
    - 专注模式
    """
    
    # 信号
    content_changed = pyqtSignal(str)
    word_count_changed = pyqtSignal(int)
    ai_suggestion_requested = pyqtSignal(str, dict)
    focus_mode_toggled = pyqtSignal(bool)
    
    def __init__(self, content: str = "", title: str = "编辑器", parent=None):
        self.initial_content = content
        self.document_title = title
        
        # 服务
        self.ai_service = AIService()
        self.config_manager = get_config_manager()
        
        # UI组件
        self.central_widget = None
        self.main_splitter = None
        self.editor_widget = None
        self.preview_widget = None
        self.sidebar = None
        self.writing_stats_panel = None
        
        # 编辑器设置
        self.font_size = 14
        self.line_spacing = 1.2
        self.focus_mode = False
        self.auto_save_enabled = True
        self.word_wrap = True
        
        # 状态
        self.is_modified = False
        self.word_count = 0
        self.character_count = 0
        self.paragraph_count = 0
        self.reading_time = 0
        
        # 定时器
        self.auto_save_timer = QTimer()
        self.stats_update_timer = QTimer()
        
        super().__init__(parent)
        
        # 设置初始内容
        if self.initial_content:
            self.editor_widget.set_content(self.initial_content)
    
    def setup_window_properties(self):
        """设置窗口属性"""
        self.setWindowTitle(f"笔落编辑器 - {self.document_title}")
        self.setMinimumSize(1000, 700)
        self.resize(1400, 900)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(self.central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建主分割器
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(self.main_splitter)
        
        # 创建编辑器区域
        self.setup_editor_area()
        
        # 创建侧边栏
        self.setup_sidebar()
        
        # 设置分割器比例
        self.main_splitter.setSizes([1100, 300])
        self.main_splitter.setCollapsible(0, False)
        self.main_splitter.setCollapsible(1, True)
    
    def setup_editor_area(self):
        """设置编辑器区域"""
        editor_frame = QFrame()
        editor_layout = QVBoxLayout(editor_frame)
        editor_layout.setContentsMargins(0, 0, 0, 0)
        editor_layout.setSpacing(0)
        
        # 创建编辑器标签页
        self.editor_tabs = QTabWidget()
        self.editor_tabs.setTabPosition(QTabWidget.TabPosition.North)
        
        # 编辑器标签页
        self.editor_widget = TextEditorWidget()
        self.editor_widget.content_changed.connect(self.on_content_changed)
        self.editor_widget.save_requested.connect(self.on_save)
        self.editor_tabs.addTab(self.editor_widget, "编辑")
        
        # 预览标签页
        self.preview_widget = MarkdownPreview()
        self.editor_tabs.addTab(self.preview_widget, "预览")
        
        # 标签页切换事件
        self.editor_tabs.currentChanged.connect(self.on_tab_changed)
        
        editor_layout.addWidget(self.editor_tabs)
        
        self.main_splitter.addWidget(editor_frame)
    
    def setup_sidebar(self):
        """设置侧边栏"""
        self.sidebar = QFrame()
        self.sidebar.setFixedWidth(300)
        self.sidebar.setFrameStyle(QFrame.Shape.StyledPanel)
        
        sidebar_layout = QVBoxLayout(self.sidebar)
        sidebar_layout.setContentsMargins(16, 16, 16, 16)
        sidebar_layout.setSpacing(16)
        
        # 写作统计
        self.setup_writing_stats(sidebar_layout)
        
        # 编辑器设置
        self.setup_editor_settings(sidebar_layout)
        
        # AI助手
        self.setup_ai_assistant(sidebar_layout)
        
        # 添加弹性空间
        sidebar_layout.addStretch()
        
        self.main_splitter.addWidget(self.sidebar)
    
    def setup_writing_stats(self, layout):
        """设置写作统计面板"""
        stats_group = QGroupBox("写作统计")
        stats_layout = QVBoxLayout(stats_group)
        
        # 字数统计
        self.word_count_label = QLabel("字数: 0")
        self.word_count_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        stats_layout.addWidget(self.word_count_label)
        
        # 字符统计
        self.char_count_label = QLabel("字符: 0")
        stats_layout.addWidget(self.char_count_label)
        
        # 段落统计
        self.para_count_label = QLabel("段落: 0")
        stats_layout.addWidget(self.para_count_label)
        
        # 预计阅读时间
        self.reading_time_label = QLabel("阅读时间: 0分钟")
        stats_layout.addWidget(self.reading_time_label)
        
        # 写作目标
        target_layout = QHBoxLayout()
        target_layout.addWidget(QLabel("目标字数:"))
        self.target_words_spin = QSpinBox()
        self.target_words_spin.setRange(0, 100000)
        self.target_words_spin.setValue(1000)
        self.target_words_spin.setSuffix(" 字")
        target_layout.addWidget(self.target_words_spin)
        stats_layout.addLayout(target_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        stats_layout.addWidget(self.progress_bar)
        
        layout.addWidget(stats_group)
    
    def setup_editor_settings(self, layout):
        """设置编辑器设置面板"""
        settings_group = QGroupBox("编辑器设置")
        settings_layout = QVBoxLayout(settings_group)
        
        # 字体大小
        font_layout = QHBoxLayout()
        font_layout.addWidget(QLabel("字体大小:"))
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 72)
        self.font_size_spin.setValue(self.font_size)
        self.font_size_spin.valueChanged.connect(self.on_font_size_changed)
        font_layout.addWidget(self.font_size_spin)
        settings_layout.addLayout(font_layout)
        
        # 行间距
        spacing_layout = QHBoxLayout()
        spacing_layout.addWidget(QLabel("行间距:"))
        self.line_spacing_slider = QSlider(Qt.Orientation.Horizontal)
        self.line_spacing_slider.setRange(100, 300)
        self.line_spacing_slider.setValue(int(self.line_spacing * 100))
        self.line_spacing_slider.valueChanged.connect(self.on_line_spacing_changed)
        spacing_layout.addWidget(self.line_spacing_slider)
        self.spacing_label = QLabel(f"{self.line_spacing:.1f}")
        spacing_layout.addWidget(self.spacing_label)
        settings_layout.addLayout(spacing_layout)
        
        # 自动换行
        self.word_wrap_check = QCheckBox("自动换行")
        self.word_wrap_check.setChecked(self.word_wrap)
        self.word_wrap_check.toggled.connect(self.on_word_wrap_toggled)
        settings_layout.addWidget(self.word_wrap_check)
        
        # 专注模式
        self.focus_mode_check = QCheckBox("专注模式")
        self.focus_mode_check.setChecked(self.focus_mode)
        self.focus_mode_check.toggled.connect(self.on_focus_mode_toggled)
        settings_layout.addWidget(self.focus_mode_check)
        
        # 自动保存
        self.auto_save_check = QCheckBox("自动保存")
        self.auto_save_check.setChecked(self.auto_save_enabled)
        self.auto_save_check.toggled.connect(self.on_auto_save_toggled)
        settings_layout.addWidget(self.auto_save_check)
        
        layout.addWidget(settings_group)
    
    def setup_ai_assistant(self, layout):
        """设置AI助手面板"""
        ai_group = QGroupBox("AI助手")
        ai_layout = QVBoxLayout(ai_group)
        
        # AI续写
        continue_btn = ModernButton("AI续写")
        continue_btn.clicked.connect(self.on_ai_continue)
        ai_layout.addWidget(continue_btn)
        
        # AI改写
        rewrite_btn = ModernButton("AI改写")
        rewrite_btn.clicked.connect(self.on_ai_rewrite)
        ai_layout.addWidget(rewrite_btn)
        
        # AI润色
        polish_btn = ModernButton("AI润色")
        polish_btn.clicked.connect(self.on_ai_polish)
        ai_layout.addWidget(polish_btn)
        
        # AI总结
        summary_btn = ModernButton("AI总结")
        summary_btn.clicked.connect(self.on_ai_summary)
        ai_layout.addWidget(summary_btn)
        
        # AI建议
        self.ai_suggestions_text = QTextEdit()
        self.ai_suggestions_text.setMaximumHeight(150)
        self.ai_suggestions_text.setPlaceholderText("AI建议将显示在这里...")
        self.ai_suggestions_text.setReadOnly(True)
        ai_layout.addWidget(QLabel("AI建议:"))
        ai_layout.addWidget(self.ai_suggestions_text)
        
        layout.addWidget(ai_group)
    
    def setup_menu_bar(self):
        """设置菜单栏"""
        super().setup_menu_bar()
        
        # 编辑菜单扩展
        edit_menu = None
        for action in self.menuBar().actions():
            if action.text() == "编辑(&E)":
                edit_menu = action.menu()
                break
        
        if edit_menu:
            edit_menu.addSeparator()
            
            # 查找替换
            find_action = QAction("查找替换(&F)", self)
            find_action.setShortcut("Ctrl+F")
            find_action.triggered.connect(self.on_find_replace)
            edit_menu.addAction(find_action)
            
            # 转到行
            goto_action = QAction("转到行(&G)", self)
            goto_action.setShortcut("Ctrl+G")
            goto_action.triggered.connect(self.on_goto_line)
            edit_menu.addAction(goto_action)
        
        # 格式菜单
        format_menu = self.menuBar().addMenu("格式(&F)")
        
        # 字体
        font_action = QAction("字体", self)
        font_action.triggered.connect(self.on_font_settings)
        format_menu.addAction(font_action)
        
        format_menu.addSeparator()
        
        # 加粗
        bold_action = QAction("加粗", self)
        bold_action.setShortcut("Ctrl+B")
        bold_action.triggered.connect(self.on_bold)
        format_menu.addAction(bold_action)
        
        # 斜体
        italic_action = QAction("斜体", self)
        italic_action.setShortcut("Ctrl+I")
        italic_action.triggered.connect(self.on_italic)
        format_menu.addAction(italic_action)
        
        # 下划线
        underline_action = QAction("下划线", self)
        underline_action.setShortcut("Ctrl+U")
        underline_action.triggered.connect(self.on_underline)
        format_menu.addAction(underline_action)
        
        # 视图菜单扩展
        view_menu = None
        for action in self.menuBar().actions():
            if action.text() == "视图(&V)":
                view_menu = action.menu()
                break
        
        if view_menu:
            view_menu.addSeparator()
            
            # 专注模式
            focus_action = QAction("专注模式", self)
            focus_action.setShortcut("F11")
            focus_action.setCheckable(True)
            focus_action.toggled.connect(self.on_focus_mode_toggled)
            view_menu.addAction(focus_action)
            
            # 显示/隐藏侧边栏
            sidebar_action = QAction("侧边栏", self)
            sidebar_action.setCheckable(True)
            sidebar_action.setChecked(True)
            sidebar_action.toggled.connect(self.on_sidebar_toggled)
            view_menu.addAction(sidebar_action)
        
        # AI菜单
        ai_menu = self.menuBar().addMenu("AI助手(&A)")
        
        # AI续写
        ai_continue_action = QAction("续写内容", self)
        ai_continue_action.setShortcut("Ctrl+Shift+C")
        ai_continue_action.triggered.connect(self.on_ai_continue)
        ai_menu.addAction(ai_continue_action)
        
        # AI改写
        ai_rewrite_action = QAction("改写内容", self)
        ai_rewrite_action.setShortcut("Ctrl+Shift+R")
        ai_rewrite_action.triggered.connect(self.on_ai_rewrite)
        ai_menu.addAction(ai_rewrite_action)
        
        # AI润色
        ai_polish_action = QAction("润色内容", self)
        ai_polish_action.setShortcut("Ctrl+Shift+P")
        ai_polish_action.triggered.connect(self.on_ai_polish)
        ai_menu.addAction(ai_polish_action)
    
    def setup_tool_bar(self):
        """设置工具栏"""
        toolbar = self.addToolBar("编辑器工具栏")
        toolbar.setMovable(False)
        
        # 保存
        save_action = QAction("保存", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.on_save)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # 撤销/重做
        undo_action = QAction("撤销", self)
        undo_action.setShortcut("Ctrl+Z")
        undo_action.triggered.connect(self.on_undo)
        toolbar.addAction(undo_action)
        
        redo_action = QAction("重做", self)
        redo_action.setShortcut("Ctrl+Y")
        redo_action.triggered.connect(self.on_redo)
        toolbar.addAction(redo_action)
        
        toolbar.addSeparator()
        
        # 格式化
        bold_action = QAction("加粗", self)
        bold_action.setShortcut("Ctrl+B")
        bold_action.triggered.connect(self.on_bold)
        toolbar.addAction(bold_action)
        
        italic_action = QAction("斜体", self)
        italic_action.setShortcut("Ctrl+I")
        italic_action.triggered.connect(self.on_italic)
        toolbar.addAction(italic_action)
        
        toolbar.addSeparator()
        
        # AI功能
        ai_continue_action = QAction("AI续写", self)
        ai_continue_action.triggered.connect(self.on_ai_continue)
        toolbar.addAction(ai_continue_action)
        
        # 专注模式
        focus_action = QAction("专注模式", self)
        focus_action.setCheckable(True)
        focus_action.toggled.connect(self.on_focus_mode_toggled)
        toolbar.addAction(focus_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        super().setup_status_bar()
        
        # 光标位置
        self.cursor_pos_label = QLabel("行: 1, 列: 1")
        self.statusBar().addPermanentWidget(self.cursor_pos_label)
        
        # 选择信息
        self.selection_label = QLabel("")
        self.statusBar().addPermanentWidget(self.selection_label)
        
        # 编码信息
        self.encoding_label = QLabel("UTF-8")
        self.statusBar().addPermanentWidget(self.encoding_label)
    
    def connect_signals(self):
        """连接信号"""
        super().connect_signals()
        
        # 自动保存定时器
        self.auto_save_timer.timeout.connect(self.auto_save)
        if self.auto_save_enabled:
            self.auto_save_timer.start(30000)  # 30秒
        
        # 统计更新定时器
        self.stats_update_timer.timeout.connect(self.update_writing_stats)
        self.stats_update_timer.start(1000)  # 1秒
    
    def on_content_changed(self, content: str):
        """内容变化处理"""
        self.is_modified = True
        self.content_changed.emit(content)
        
        # 更新预览
        if self.preview_widget:
            self.preview_widget.update_content(content)
        
        # 更新窗口标题
        title = f"笔落编辑器 - {self.document_title}"
        if self.is_modified:
            title += " *"
        self.setWindowTitle(title)
    
    def on_tab_changed(self, index: int):
        """标签页切换处理"""
        if index == 1:  # 预览标签页
            content = self.editor_widget.get_content()
            self.preview_widget.update_content(content)
    
    def update_writing_stats(self):
        """更新写作统计"""
        if not self.editor_widget:
            return
        
        content = self.editor_widget.get_content()
        
        # 字数统计（中文按字符计算，英文按单词计算）
        chinese_chars = len([c for c in content if '\u4e00' <= c <= '\u9fff'])
        english_words = len([w for w in content.replace('\n', ' ').split() if w.isalpha()])
        self.word_count = chinese_chars + english_words
        
        # 字符统计
        self.character_count = len(content)
        
        # 段落统计
        self.paragraph_count = len([p for p in content.split('\n\n') if p.strip()])
        
        # 预计阅读时间（按每分钟200字计算）
        self.reading_time = max(1, self.word_count // 200)
        
        # 更新UI
        self.word_count_label.setText(f"字数: {self.word_count}")
        self.char_count_label.setText(f"字符: {self.character_count}")
        self.para_count_label.setText(f"段落: {self.paragraph_count}")
        self.reading_time_label.setText(f"阅读时间: {self.reading_time}分钟")
        
        # 更新进度条
        target = self.target_words_spin.value()
        if target > 0:
            progress = min(100, int((self.word_count / target) * 100))
            self.progress_bar.setValue(progress)
        
        # 发出信号
        self.word_count_changed.emit(self.word_count)
    
    def on_font_size_changed(self, size: int):
        """字体大小变化"""
        self.font_size = size
        if self.editor_widget:
            font = self.editor_widget.font()
            font.setPointSize(size)
            self.editor_widget.setFont(font)
    
    def on_line_spacing_changed(self, value: int):
        """行间距变化"""
        self.line_spacing = value / 100.0
        self.spacing_label.setText(f"{self.line_spacing:.1f}")
        # TODO: 应用行间距设置
    
    def on_word_wrap_toggled(self, enabled: bool):
        """自动换行切换"""
        self.word_wrap = enabled
        if self.editor_widget:
            self.editor_widget.setLineWrapMode(
                QTextEdit.LineWrapMode.WidgetWidth if enabled else QTextEdit.LineWrapMode.NoWrap
            )
    
    def on_focus_mode_toggled(self, enabled: bool):
        """专注模式切换"""
        self.focus_mode = enabled
        
        if enabled:
            # 隐藏侧边栏和工具栏
            self.sidebar.hide()
            self.menuBar().hide()
            for toolbar in self.findChildren(self.toolBar().__class__):
                toolbar.hide()
            self.statusBar().hide()
        else:
            # 显示所有界面元素
            self.sidebar.show()
            self.menuBar().show()
            for toolbar in self.findChildren(self.toolBar().__class__):
                toolbar.show()
            self.statusBar().show()
        
        self.focus_mode_toggled.emit(enabled)
    
    def on_auto_save_toggled(self, enabled: bool):
        """自动保存切换"""
        self.auto_save_enabled = enabled
        
        if enabled:
            self.auto_save_timer.start(30000)
        else:
            self.auto_save_timer.stop()
    
    def on_sidebar_toggled(self, visible: bool):
        """侧边栏显示切换"""
        self.sidebar.setVisible(visible)
    
    # 格式化功能
    def on_bold(self):
        """加粗"""
        if self.editor_widget:
            cursor = self.editor_widget.textCursor()
            format = cursor.charFormat()
            format.setFontWeight(QFont.Weight.Bold if not format.fontWeight() == QFont.Weight.Bold else QFont.Weight.Normal)
            cursor.setCharFormat(format)
    
    def on_italic(self):
        """斜体"""
        if self.editor_widget:
            cursor = self.editor_widget.textCursor()
            format = cursor.charFormat()
            format.setFontItalic(not format.fontItalic())
            cursor.setCharFormat(format)
    
    def on_underline(self):
        """下划线"""
        if self.editor_widget:
            cursor = self.editor_widget.textCursor()
            format = cursor.charFormat()
            format.setFontUnderline(not format.fontUnderline())
            cursor.setCharFormat(format)
    
    # AI功能
    @handle_exceptions()
    def on_ai_continue(self):
        """AI续写"""
        if not self.editor_widget:
            return
        
        content = self.editor_widget.get_content()
        if not content.strip():
            self.show_message("提示", "请先输入一些内容作为续写的基础")
            return
        
        try:
            self.statusBar().showMessage("正在AI续写...")
            # TODO: 实现AI续写功能
            self.show_message("提示", "AI续写功能正在开发中")
        except Exception as e:
            self.show_message("错误", f"AI续写失败: {e}", "error")
        finally:
            self.statusBar().showMessage("就绪")
    
    def on_ai_rewrite(self):
        """AI改写"""
        # TODO: 实现AI改写功能
        self.show_message("提示", "AI改写功能正在开发中")
    
    def on_ai_polish(self):
        """AI润色"""
        # TODO: 实现AI润色功能
        self.show_message("提示", "AI润色功能正在开发中")
    
    def on_ai_summary(self):
        """AI总结"""
        # TODO: 实现AI总结功能
        self.show_message("提示", "AI总结功能正在开发中")
    
    # 其他功能
    def on_find_replace(self):
        """查找替换"""
        # TODO: 实现查找替换对话框
        self.show_message("提示", "查找替换功能正在开发中")
    
    def on_goto_line(self):
        """转到行"""
        # TODO: 实现转到行对话框
        self.show_message("提示", "转到行功能正在开发中")
    
    def on_font_settings(self):
        """字体设置"""
        # TODO: 实现字体设置对话框
        self.show_message("提示", "字体设置功能正在开发中")
    
    def auto_save(self):
        """自动保存"""
        if self.is_modified:
            # TODO: 实现自动保存逻辑
            self.statusBar().showMessage("自动保存完成", 2000)
    
    def get_content(self) -> str:
        """获取编辑器内容"""
        return self.editor_widget.get_content() if self.editor_widget else ""
    
    def set_content(self, content: str):
        """设置编辑器内容"""
        if self.editor_widget:
            self.editor_widget.set_content(content)
            self.is_modified = False
    
    # 重写基类方法
    def on_save(self):
        """保存操作"""
        # TODO: 实现保存逻辑
        self.is_modified = False
        title = f"笔落编辑器 - {self.document_title}"
        self.setWindowTitle(title)
        self.statusBar().showMessage("保存成功", 2000)
    
    def on_undo(self):
        """撤销操作"""
        if self.editor_widget:
            self.editor_widget.undo()
    
    def on_redo(self):
        """重做操作"""
        if self.editor_widget:
            self.editor_widget.redo()
    
    def cleanup(self):
        """清理资源"""
        # 停止定时器
        if self.auto_save_timer.isActive():
            self.auto_save_timer.stop()
        if self.stats_update_timer.isActive():
            self.stats_update_timer.stop()
        
        # 自动保存
        if self.is_modified and self.auto_save_enabled:
            self.auto_save()