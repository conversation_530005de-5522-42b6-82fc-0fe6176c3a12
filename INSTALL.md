# 笔落App 快速安装指南

## 🚀 一键安装 (推荐)

### Windows用户
1. 下载项目文件到本地
2. 双击运行 `run.bat` 文件
3. 按照提示完成安装和启动

### macOS/Linux用户
```bash
# 克隆项目
git clone https://github.com/yourusername/bamboofall_cc.git
cd bamboofall_cc

# 给脚本执行权限
chmod +x run.sh

# 运行安装脚本
./run.sh
```

## 📋 手动安装步骤

### 1. 准备环境
```bash
# 检查Python版本 (需要3.9+)
python --version

# 创建虚拟环境
python -m venv bamboofall_env

# 激活虚拟环境
# Windows:
bamboofall_env\Scripts\activate
# macOS/Linux:
source bamboofall_env/bin/activate
```

### 2. 安装依赖
```bash
# 升级pip
python -m pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt

# 安装项目本身
pip install -e .
```

### 3. 运行应用
```bash
# 方式1: 使用启动脚本
python run.py

# 方式2: 直接运行
python src/bamboofall/main.py

# 方式3: 使用命令行工具
bamboofall
```

## ⚠️ 常见问题解决

### Python版本问题
```bash
# 如果系统Python版本过低，安装新版本
# Windows: 从官网下载安装包
# macOS: brew install python@3.11  
# Ubuntu: sudo apt install python3.11
```

### 依赖安装失败
```bash
# 清理pip缓存
pip cache purge

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 权限问题
```bash
# Linux/macOS确保脚本有执行权限
chmod +x run.sh

# Windows确保在有权限的目录下运行
```

## 🎯 验证安装

安装完成后，应该能看到：
1. 笔落App主窗口打开
2. 界面显示正常，无错误提示
3. 可以切换深色/浅色主题
4. 状态栏显示"就绪"

## 📞 获取帮助

如果安装过程中遇到问题：
1. 查看控制台错误信息
2. 检查 `~/.bamboofall/logs/` 目录下的日志文件
3. 在GitHub上提交Issue
4. 发送邮件到 <EMAIL>

---

*安装成功后，建议阅读用户手册了解详细功能。*