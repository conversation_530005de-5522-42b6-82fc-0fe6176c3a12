"""
Anthropic API服务实现

提供Anthropic Claude API的完整集成，支持聊天、补全和流式响应功能
"""

import asyncio
import time
from typing import List, Optional, Dict, Any, AsyncGenerator
import anthropic
from anthropic import AsyncAnthropic

from .ai_service_base import (
    AIServiceBase, AIMessage, AIResponse, AIConfig, AIModelType,
    MessageRole, AIServiceError, APIKeyError, RateLimitError, NetworkError
)
from ..utils.logger import LoggerMixin


class AnthropicService(AIServiceBase, LoggerMixin):
    """Anthropic服务实现"""
    
    def __init__(self, config: AIConfig):
        super().__init__(config)
        self.client = AsyncAnthropic(
            api_key=config.api_key,
            base_url=config.base_url,
            timeout=config.timeout
        )
        
        # Anthropic支持的模型列表
        self._supported_models = [
            # Claude 3 系列
            "claude-3-opus-20240229",
            "claude-3-sonnet-20240229",
            "claude-3-haiku-20240307",
            
            # Claude 2 系列
            "claude-2.1",
            "claude-2.0",
            
            # <PERSON> Instant
            "claude-instant-1.2",
            "claude-instant-1.1"
        ]
        
    @property
    def supported_models(self) -> List[str]:
        """支持的模型列表"""
        return self._supported_models
    
    @property
    def model_type(self) -> AIModelType:
        """模型类型"""
        return AIModelType.CHAT
    
    def validate_config(self) -> bool:
        """验证配置"""
        if not self.config.api_key:
            self.logger.error("Anthropic API密钥未配置")
            return False
        
        if self.config.model not in self.supported_models:
            self.logger.warning(f"模型 {self.config.model} 可能不受支持")
        
        return True
    
    def _handle_api_error(self, error: Exception) -> AIServiceError:
        """处理API错误"""
        if isinstance(error, anthropic.AuthenticationError):
            return APIKeyError("Anthropic API密钥无效或已过期")
        elif isinstance(error, anthropic.RateLimitError):
            return RateLimitError("Anthropic API请求频率超限，请稍后重试")
        elif isinstance(error, anthropic.APIConnectionError):
            return NetworkError("无法连接到Anthropic API服务")
        elif isinstance(error, anthropic.APIStatusError):
            return AIServiceError(f"Anthropic API错误: {error.message}", str(error.status_code))
        else:
            return AIServiceError(f"Anthropic服务未知错误: {str(error)}")
    
    def _convert_messages(self, messages: List[AIMessage]) -> tuple:
        """转换消息格式为Anthropic API格式"""
        converted = []
        system_message = None
        
        for msg in messages:
            if msg.role == MessageRole.SYSTEM:
                # Anthropic将system消息单独处理
                system_message = msg.content
            else:
                converted.append({
                    "role": msg.role.value,
                    "content": msg.content
                })
        
        return converted, system_message
    
    async def chat(
        self,
        messages: List[AIMessage],
        model: Optional[str] = None,
        **kwargs
    ) -> AIResponse:
        """聊天接口"""
        model = model or self.config.model
        max_tokens = kwargs.get('max_tokens', self.config.max_tokens)
        temperature = kwargs.get('temperature', self.config.temperature)
        
        try:
            # 转换消息格式
            converted_messages, system_message = self._convert_messages(messages)
            
            # 构建请求参数
            request_params = {
                "model": model,
                "messages": converted_messages,
                "max_tokens": max_tokens,
                "temperature": temperature
            }
            
            # 添加system消息（如果存在）
            if system_message:
                request_params["system"] = system_message
            
            # 发送请求
            start_time = time.time()
            response = await self.client.messages.create(**request_params)
            end_time = time.time()
            
            # 构建响应
            content = response.content[0].text if response.content else ""
            
            return AIResponse(
                content=content,
                model=model,
                tokens_used=response.usage.input_tokens + response.usage.output_tokens,
                input_tokens=response.usage.input_tokens,
                output_tokens=response.usage.output_tokens,
                response_time=end_time - start_time,
                provider=self.provider_name,
                raw_response=response.model_dump() if hasattr(response, 'model_dump') else {}
            )
            
        except Exception as e:
            self.logger.error(f"Anthropic聊天请求失败: {e}")
            raise self._handle_api_error(e)
    
    async def stream_chat(
        self,
        messages: List[AIMessage],
        model: Optional[str] = None,
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式聊天接口"""
        model = model or self.config.model
        max_tokens = kwargs.get('max_tokens', self.config.max_tokens)
        temperature = kwargs.get('temperature', self.config.temperature)
        
        try:
            # 转换消息格式
            converted_messages, system_message = self._convert_messages(messages)
            
            # 构建请求参数
            request_params = {
                "model": model,
                "messages": converted_messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": True
            }
            
            # 添加system消息（如果存在）
            if system_message:
                request_params["system"] = system_message
            
            # 发送流式请求
            async with self.client.messages.stream(**request_params) as stream:
                async for chunk in stream:
                    if chunk.type == "content_block_delta":
                        if hasattr(chunk.delta, 'text'):
                            yield chunk.delta.text
                    elif chunk.type == "message_delta":
                        # 处理消息级别的增量更新
                        continue
                        
        except Exception as e:
            self.logger.error(f"Anthropic流式聊天请求失败: {e}")
            raise self._handle_api_error(e)
    
    async def complete(
        self,
        prompt: str,
        model: Optional[str] = None,
        **kwargs
    ) -> AIResponse:
        """文本补全接口（通过聊天接口实现）"""
        # Anthropic主要使用聊天接口，将prompt转换为用户消息
        messages = [AIMessage(role=MessageRole.USER, content=prompt)]
        return await self.chat(messages, model, **kwargs)
    
    async def get_embedding(
        self,
        text: str,
        model: Optional[str] = None
    ) -> List[float]:
        """
        获取文本嵌入
        
        注意：Anthropic目前不提供embedding服务
        """
        raise NotImplementedError(
            "Anthropic不提供embedding服务，请使用OpenAI或其他支持embedding的服务"
        )
    
    async def test_connection(self) -> bool:
        """
        测试连接
        """
        try:
            # 发送一个简单的测试请求
            test_messages = [
                AIMessage(role=MessageRole.USER, content="Hello")
            ]
            await self.chat(test_messages)
            return True
        except Exception as e:
            self.logger.error(f"Anthropic连接测试失败: {e}")
            return False
    
    async def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        # Anthropic API目前不提供动态模型列表接口
        # 返回预定义的模型列表
        return self.supported_models
    
    def get_model_info(self, model: str) -> Dict[str, Any]:
        """获取模型信息"""
        model_info = {
            # Claude 3 系列
            "claude-3-opus-20240229": {
                "name": "Claude 3 Opus",
                "description": "最强大的Claude模型，适合复杂推理任务",
                "max_tokens": 4096,
                "context_window": 200000
            },
            "claude-3-sonnet-20240229": {
                "name": "Claude 3 Sonnet",
                "description": "平衡性能和成本的Claude模型",
                "max_tokens": 4096,
                "context_window": 200000
            },
            "claude-3-haiku-20240307": {
                "name": "Claude 3 Haiku",
                "description": "快速响应的Claude模型",
                "max_tokens": 4096,
                "context_window": 200000
            },
            # Claude 2 系列
            "claude-2.1": {
                "name": "Claude 2.1",
                "description": "Claude 2的改进版本",
                "max_tokens": 4096,
                "context_window": 200000
            },
            "claude-2.0": {
                "name": "Claude 2.0",
                "description": "Claude 2基础版本",
                "max_tokens": 4096,
                "context_window": 100000
            }
        }
        
        return model_info.get(model, {
            "name": model,
            "description": "Anthropic Claude模型",
            "max_tokens": 4096,
            "context_window": 100000
        })