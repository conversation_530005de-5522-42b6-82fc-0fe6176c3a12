"""
章节数据模型

定义小说章节的结构
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from enum import Enum
import uuid
import json


class ChapterStatus(Enum):
    """章节状态枚举"""
    DRAFT = "draft"                  # 草稿
    WRITING = "writing"              # 写作中
    REVIEW = "review"                # 待审阅
    EDITING = "editing"              # 编辑中
    COMPLETE = "complete"            # 已完成
    PUBLISHED = "published"          # 已发布


@dataclass
class Chapter:
    """章节模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    project_id: str = ""
    title: str = ""
    
    # 内容
    content: str = ""
    summary: str = ""
    
    # 状态和顺序
    status: ChapterStatus = ChapterStatus.DRAFT
    order_index: int = 0
    
    # 统计信息
    word_count: int = 0
    character_count: int = 0
    paragraph_count: int = 0
    
    # 相关信息
    scene_ids: List[str] = field(default_factory=list)
    character_ids: List[str] = field(default_factory=list)
    
    # 时间信息
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # 写作目标
    target_word_count: Optional[int] = None
    
    # 标签
    tags: List[str] = field(default_factory=list)
    
    # 备注
    notes: str = ""
    
    # 大纲节点ID
    outline_node_id: Optional[str] = None
    
    def update_timestamp(self):
        """更新时间戳"""
        self.updated_at = datetime.now()
    
    def update_statistics(self):
        """更新统计信息"""
        self.word_count = len(self.content.split())
        self.character_count = len(self.content)
        self.paragraph_count = len([p for p in self.content.split('\n\n') if p.strip()])
        self.update_timestamp()
    
    def add_scene(self, scene_id: str):
        """添加场景"""
        if scene_id not in self.scene_ids:
            self.scene_ids.append(scene_id)
            self.update_timestamp()
    
    def remove_scene(self, scene_id: str):
        """移除场景"""
        if scene_id in self.scene_ids:
            self.scene_ids.remove(scene_id)
            self.update_timestamp()
    
    def add_character(self, character_id: str):
        """添加角色"""
        if character_id not in self.character_ids:
            self.character_ids.append(character_id)
            self.update_timestamp()
    
    def remove_character(self, character_id: str):
        """移除角色"""
        if character_id in self.character_ids:
            self.character_ids.remove(character_id)
            self.update_timestamp()
    
    def get_progress_percentage(self) -> float:
        """获取进度百分比"""
        if not self.target_word_count:
            return 0.0
        return min(100.0, (self.word_count / self.target_word_count) * 100)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "project_id": self.project_id,
            "title": self.title,
            "content": self.content,
            "summary": self.summary,
            "status": self.status.value,
            "order_index": self.order_index,
            "word_count": self.word_count,
            "character_count": self.character_count,
            "paragraph_count": self.paragraph_count,
            "scene_ids": self.scene_ids,
            "character_ids": self.character_ids,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "target_word_count": self.target_word_count,
            "tags": self.tags,
            "notes": self.notes,
            "outline_node_id": self.outline_node_id,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Chapter":
        """从字典创建章节"""
        # 处理枚举类型
        status = ChapterStatus(data.get("status", "draft"))
        
        # 处理时间
        created_at = datetime.fromisoformat(data["created_at"]) if data.get("created_at") else datetime.now()
        updated_at = datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else datetime.now()
        
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            project_id=data.get("project_id", ""),
            title=data.get("title", ""),
            content=data.get("content", ""),
            summary=data.get("summary", ""),
            status=status,
            order_index=data.get("order_index", 0),
            word_count=data.get("word_count", 0),
            character_count=data.get("character_count", 0),
            paragraph_count=data.get("paragraph_count", 0),
            scene_ids=data.get("scene_ids", []),
            character_ids=data.get("character_ids", []),
            created_at=created_at,
            updated_at=updated_at,
            target_word_count=data.get("target_word_count"),
            tags=data.get("tags", []),
            notes=data.get("notes", ""),
            outline_node_id=data.get("outline_node_id"),
        )
    
    def __str__(self) -> str:
        return f"Chapter(id={self.id}, title={self.title}, word_count={self.word_count})"