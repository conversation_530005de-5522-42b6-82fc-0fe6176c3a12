"""
环境安装脚本

提供自动化的环境配置和依赖安装
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description="", ignore_errors=False):
    """运行命令并处理结果"""
    print(f"\n{'='*50}")
    print(f"正在执行: {description or command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=True, 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        print("✅ 执行成功!")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 执行失败: {e}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        
        if not ignore_errors:
            return False
        else:
            print("⚠️ 忽略错误，继续执行...")
            return True


def install_package_with_fallback(package_name, fallback_versions=None):
    """尝试安装包，如果失败则尝试备选版本"""
    print(f"\n正在安装 {package_name}...")
    
    # 首先尝试原版本
    if run_command(f"pip install {package_name}", f"安装 {package_name}", ignore_errors=True):
        return True
    
    # 如果失败，尝试备选版本
    if fallback_versions:
        for version in fallback_versions:
            print(f"尝试备选版本: {version}")
            if run_command(f"pip install {version}", f"安装 {version}", ignore_errors=True):
                return True
    
    print(f"❌ 无法安装 {package_name}")
    return False


def main():
    """主安装流程"""
    print("🚀 开始安装笔落App开发环境...")
    print("项目地址: https://github.com/bamboofall/bamboofall-app")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"\n当前Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8+")
        return False
    
    # 升级pip
    print("\n📦 升级pip...")
    run_command("python -m pip install --upgrade pip", "升级pip")
    
    # 核心依赖安装策略
    core_packages = [
        # GUI框架 - 使用备选方案
        {
            "name": "PyQt6>=6.4.0",
            "fallbacks": ["PyQt6>=6.2.0", "PyQt6>=6.0.0", "PyQt6"]
        },
        {
            "name": "qdarktheme>=2.0.0", 
            "fallbacks": ["qdarktheme>=1.0.0", "qdarktheme", "qdarkstyle>=3.0.0"]
        },
        {
            "name": "qtawesome>=1.2.0",
            "fallbacks": ["qtawesome>=1.0.0", "qtawesome"]
        },
        
        # 数据库
        {
            "name": "SQLAlchemy>=1.4.0",
            "fallbacks": ["SQLAlchemy>=1.3.0", "SQLAlchemy"]
        },
        {
            "name": "alembic>=1.8.0",
            "fallbacks": ["alembic>=1.7.0", "alembic"]
        },
        
        # AI服务
        {
            "name": "openai>=1.0.0",
            "fallbacks": ["openai>=0.27.0", "openai"]
        },
        {
            "name": "aiohttp>=3.8.0",
            "fallbacks": ["aiohttp>=3.7.0", "aiohttp"]
        },
        
        # 文本处理
        {
            "name": "markdown>=3.4.0",
            "fallbacks": ["markdown>=3.3.0", "markdown"]
        },
        {
            "name": "jinja2>=3.0.0",
            "fallbacks": ["jinja2>=2.11.0", "jinja2"]
        }
    ]
    
    # 安装核心依赖
    failed_packages = []
    for package in core_packages:
        if not install_package_with_fallback(package["name"], package["fallbacks"]):
            failed_packages.append(package["name"])
    
    # 安装可选依赖
    optional_packages = [
        "pydantic",
        "dataclasses-json", 
        "httpx",
        "loguru",
        "python-dotenv",
        "click",
        "cryptography",
        "keyring"
    ]
    
    print("\n📦 安装可选依赖...")
    for package in optional_packages:
        run_command(f"pip install {package}", f"安装 {package}", ignore_errors=True)
    
    # 检查关键依赖
    print("\n🔍 检查关键依赖...")
    critical_imports = [
        ("PyQt6.QtWidgets", "PyQt6 GUI框架"),
        ("sqlalchemy", "SQLAlchemy数据库ORM"),
        ("markdown", "Markdown文本处理"),
        ("jinja2", "Jinja2模板引擎")
    ]
    
    import_errors = []
    for module, description in critical_imports:
        try:
            __import__(module)
            print(f"✅ {description} - 导入成功")
        except ImportError as e:
            print(f"❌ {description} - 导入失败: {e}")
            import_errors.append(description)
    
    # 总结安装结果
    print("\n" + "="*60)
    print("📋 安装总结")
    print("="*60)
    
    if failed_packages:
        print(f"❌ 安装失败的包: {', '.join(failed_packages)}")
    
    if import_errors:
        print(f"❌ 导入失败的模块: {', '.join(import_errors)}")
    
    if not failed_packages and not import_errors:
        print("🎉 所有依赖安装成功！")
        print("\n▶️  现在可以运行应用:")
        print("   python run.py")
        return True
    else:
        print("\n⚠️  部分依赖安装失败，但核心功能可能仍可使用")
        print("\n🔧 手动解决方案:")
        if "qdarktheme" in str(failed_packages):
            print("1. 主题问题: pip install qdarkstyle  # 使用备选主题库")
        if "PyQt6" in str(failed_packages):
            print("2. GUI问题: pip install PyQt5  # 或尝试PyQt5")
        print("3. 或尝试: pip install -r requirements.txt --no-deps")
        
        return False


if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎊 环境配置完成！")
        else:
            print("\n⚠️  环境配置遇到问题，请查看上述建议")
    except KeyboardInterrupt:
        print("\n\n用户取消安装")
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        print("\n请尝试手动安装: pip install -r requirements.txt")