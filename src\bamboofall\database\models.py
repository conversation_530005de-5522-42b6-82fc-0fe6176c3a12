"""
数据库ORM模型

定义所有数据模型的SQLAlchemy映射
"""

from sqlalchemy import Column, String, Integer, DateTime, Text, Float, JSON, ForeignKey, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, backref
from datetime import datetime
import uuid

Base = declarative_base()


class ProjectORM(Base):
    """项目ORM模型"""
    __tablename__ = 'projects'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(200), nullable=False)
    description = Column(Text)
    author = Column(String(100))
    genre = Column(String(50))
    language = Column(String(20), default='zh-CN')
    target_audience = Column(String(100))
    word_count_goal = Column(Integer)
    current_word_count = Column(Integer, default=0)
    status = Column(String(20), default='planning')
    priority = Column(String(20), default='medium')
    deadline = Column(DateTime)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    tags = Column(JSON)
    world_building = Column(Text)
    notes = Column(Text)
    cover_image_path = Column(String(500))
    
    # 关系映射
    characters = relationship("CharacterORM", back_populates="project", cascade="all, delete-orphan")
    chapters = relationship("ChapterORM", back_populates="project", cascade="all, delete-orphan")
    scenes = relationship("SceneORM", back_populates="project", cascade="all, delete-orphan")


class CharacterORM(Base):
    """角色ORM模型"""
    __tablename__ = 'characters'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, ForeignKey('projects.id'), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    age = Column(Integer)
    gender = Column(String(20))
    occupation = Column(String(100))
    personality_traits = Column(JSON)
    background = Column(Text)
    abilities = Column(JSON)
    relationships = Column(JSON)
    goals = Column(Text)
    conflicts = Column(Text)
    arc_development = Column(Text)
    appearance = Column(Text)
    role = Column(String(50), default='supporting')
    importance = Column(Integer, default=1)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    tags = Column(JSON)
    notes = Column(Text)
    image_path = Column(String(500))
    
    # 关系映射
    project = relationship("ProjectORM", back_populates="characters")


class ChapterORM(Base):
    """章节ORM模型"""
    __tablename__ = 'chapters'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, ForeignKey('projects.id'), nullable=False)
    title = Column(String(200), nullable=False)
    content = Column(Text)
    summary = Column(Text)
    status = Column(String(20), default='draft')
    order_index = Column(Integer, default=0)
    word_count = Column(Integer, default=0)
    character_count = Column(Integer, default=0)
    paragraph_count = Column(Integer, default=0)
    scene_ids = Column(JSON)
    character_ids = Column(JSON)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    target_word_count = Column(Integer)
    tags = Column(JSON)
    notes = Column(Text)
    outline_node_id = Column(String)
    
    # 关系映射
    project = relationship("ProjectORM", back_populates="chapters")


class SceneORM(Base):
    """场景ORM模型"""
    __tablename__ = 'scenes'
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    project_id = Column(String, ForeignKey('projects.id'), nullable=False)
    name = Column(String(200), nullable=False)
    description = Column(Text)
    location = Column(String(200))
    time_period = Column(String(100))
    weather = Column(String(50))
    mood = Column(String(50))
    purpose = Column(Text)
    conflict = Column(Text)
    characters_present = Column(JSON)
    props_items = Column(JSON)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    tags = Column(JSON)
    notes = Column(Text)
    reference_images = Column(JSON)
    
    # 关系映射
    project = relationship("ProjectORM", back_populates="scenes")
