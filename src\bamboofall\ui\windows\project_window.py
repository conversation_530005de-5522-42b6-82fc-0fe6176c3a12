"""项目窗口类

用于管理和编辑具体的项目内容。
"""

from typing import Optional, List, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QSplitter, QTabWidget,
    QTreeWidget, QTreeWidgetItem, QTextEdit, QLabel, QPushButton,
    QFrame, QStackedWidget, QToolButton, QMenu, QMenuBar, QStatusBar,
    QProgressBar, QComboBox, QSpinBox, QCheckBox, QGroupBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt6.QtGui import QAction, QIcon, QFont, QTextCursor
import os
from pathlib import Path

from .base_window import BaseWindow
from ..editor.text_editor_widget import TextEditorWidget
from ..widgets.modern_widgets import ModernButton
from ...core.project_manager import ProjectManager
from ...core.character_manager import CharacterManager
from ...core.chapter_manager import ChapterManager
from ...core.outline_manager import OutlineManager
from ...services.ai_service import AIService
from ...utils.config_utils import get_config_manager
from ...exceptions import handle_exceptions, ProjectError, AIServiceError


class ProjectWindow(BaseWindow):
    """项目窗口类
    
    功能：
    - 项目结构管理
    - 章节编辑
    - 角色管理
    - 大纲管理
    - AI辅助功能
    """
    
    # 信号
    chapter_selected = pyqtSignal(str)  # 章节ID
    character_selected = pyqtSignal(str)  # 角色ID
    outline_updated = pyqtSignal(dict)  # 大纲数据
    content_changed = pyqtSignal(str, str)  # 内容类型, 内容
    ai_generation_requested = pyqtSignal(str, dict)  # 生成类型, 参数
    
    def __init__(self, project_path: str, parent=None):
        self.project_path = project_path
        
        # 管理器
        self.project_manager = ProjectManager()
        self.character_manager = CharacterManager()
        self.chapter_manager = ChapterManager()
        self.outline_manager = OutlineManager()
        self.ai_service = AIService()
        self.config_manager = get_config_manager()
        
        # UI组件
        self.central_widget = None
        self.main_splitter = None
        self.left_panel = None
        self.right_panel = None
        self.project_tree = None
        self.content_tabs = None
        self.editor_widget = None
        self.character_panel = None
        self.outline_panel = None
        
        # 状态
        self.current_chapter_id = None
        self.current_character_id = None
        self.is_modified = False
        self.auto_save_timer = QTimer()
        
        super().__init__(parent)
        
        # 加载项目
        self.load_project()
    
    def setup_window_properties(self):
        """设置窗口属性"""
        project_name = os.path.basename(self.project_path)
        self.setWindowTitle(f"笔落 - {project_name}")
        self.setMinimumSize(1400, 900)
        self.resize(1600, 1000)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(self.central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建主分割器
        self.main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(self.main_splitter)
        
        # 创建左侧面板
        self.setup_left_panel()
        
        # 创建右侧面板
        self.setup_right_panel()
        
        # 设置分割器比例
        self.main_splitter.setSizes([350, 1250])
        self.main_splitter.setCollapsible(0, False)
        self.main_splitter.setCollapsible(1, False)
    
    def setup_left_panel(self):
        """设置左侧面板"""
        self.left_panel = QFrame()
        self.left_panel.setFixedWidth(350)
        self.left_panel.setFrameStyle(QFrame.Shape.StyledPanel)
        
        left_layout = QVBoxLayout(self.left_panel)
        left_layout.setContentsMargins(8, 8, 8, 8)
        left_layout.setSpacing(8)
        
        # 项目结构树
        self.setup_project_tree(left_layout)
        
        self.main_splitter.addWidget(self.left_panel)
    
    def setup_project_tree(self, layout):
        """设置项目结构树"""
        tree_label = QLabel("项目结构")
        tree_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                padding: 8px 4px;
            }
        """)
        layout.addWidget(tree_label)
        
        self.project_tree = QTreeWidget()
        self.project_tree.setHeaderHidden(True)
        self.project_tree.itemClicked.connect(self.on_tree_item_clicked)
        self.project_tree.itemDoubleClicked.connect(self.on_tree_item_double_clicked)
        
        # 设置树形控件样式
        self.project_tree.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #ddd;
                border-radius: 6px;
                background-color: white;
                selection-background-color: #e3f2fd;
            }
            QTreeWidget::item {
                padding: 6px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTreeWidget::item:hover {
                background-color: #f5f5f5;
            }
            QTreeWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        layout.addWidget(self.project_tree)
    
    def setup_right_panel(self):
        """设置右侧面板"""
        self.right_panel = QFrame()
        
        right_layout = QVBoxLayout(self.right_panel)
        right_layout.setContentsMargins(8, 8, 8, 8)
        right_layout.setSpacing(8)
        
        # 创建标签页
        self.content_tabs = QTabWidget()
        self.content_tabs.setTabPosition(QTabWidget.TabPosition.North)
        self.content_tabs.setMovable(True)
        self.content_tabs.setTabsClosable(True)
        self.content_tabs.tabCloseRequested.connect(self.close_tab)
        
        # 编辑器标签页
        self.setup_editor_tab()
        
        # 角色管理标签页
        self.setup_character_tab()
        
        # 大纲管理标签页
        self.setup_outline_tab()
        
        right_layout.addWidget(self.content_tabs)
        
        self.main_splitter.addWidget(self.right_panel)
    
    def setup_editor_tab(self):
        """设置编辑器标签页"""
        self.editor_widget = TextEditorWidget()
        self.editor_widget.content_changed.connect(self.on_content_changed)
        self.editor_widget.save_requested.connect(self.on_save_requested)
        
        self.content_tabs.addTab(self.editor_widget, "编辑器")
    
    def setup_character_tab(self):
        """设置角色管理标签页"""
        self.character_panel = QWidget()
        character_layout = QVBoxLayout(self.character_panel)
        
        # 角色列表和编辑区域
        character_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 角色列表
        character_list_frame = QFrame()
        character_list_layout = QVBoxLayout(character_list_frame)
        
        character_list_label = QLabel("角色列表")
        character_list_label.setStyleSheet("font-weight: bold; padding: 8px;")
        character_list_layout.addWidget(character_list_label)
        
        # 添加角色按钮
        add_character_btn = ModernButton("添加角色")
        add_character_btn.clicked.connect(self.on_add_character)
        character_list_layout.addWidget(add_character_btn)
        
        # 角色列表控件
        self.character_list = QTreeWidget()
        self.character_list.setHeaderHidden(True)
        self.character_list.itemClicked.connect(self.on_character_selected)
        character_list_layout.addWidget(self.character_list)
        
        character_splitter.addWidget(character_list_frame)
        
        # 角色编辑区域
        character_edit_frame = QFrame()
        character_edit_layout = QVBoxLayout(character_edit_frame)
        
        character_edit_label = QLabel("角色详情")
        character_edit_label.setStyleSheet("font-weight: bold; padding: 8px;")
        character_edit_layout.addWidget(character_edit_label)
        
        # 角色信息编辑控件
        self.character_name_edit = QTextEdit()
        self.character_name_edit.setMaximumHeight(40)
        self.character_name_edit.setPlaceholderText("角色姓名")
        character_edit_layout.addWidget(self.character_name_edit)
        
        self.character_desc_edit = QTextEdit()
        self.character_desc_edit.setPlaceholderText("角色描述")
        character_edit_layout.addWidget(self.character_desc_edit)
        
        # 保存角色按钮
        save_character_btn = ModernButton("保存角色")
        save_character_btn.clicked.connect(self.on_save_character)
        character_edit_layout.addWidget(save_character_btn)
        
        character_splitter.addWidget(character_edit_frame)
        character_splitter.setSizes([200, 400])
        
        character_layout.addWidget(character_splitter)
        
        self.content_tabs.addTab(self.character_panel, "角色管理")
    
    def setup_outline_tab(self):
        """设置大纲管理标签页"""
        self.outline_panel = QWidget()
        outline_layout = QVBoxLayout(self.outline_panel)
        
        # 大纲工具栏
        outline_toolbar = QHBoxLayout()
        
        add_outline_btn = ModernButton("添加章节")
        add_outline_btn.clicked.connect(self.on_add_outline_chapter)
        outline_toolbar.addWidget(add_outline_btn)
        
        generate_outline_btn = ModernButton("AI生成大纲")
        generate_outline_btn.clicked.connect(self.on_generate_outline)
        outline_toolbar.addWidget(generate_outline_btn)
        
        outline_toolbar.addStretch()
        outline_layout.addLayout(outline_toolbar)
        
        # 大纲树形结构
        self.outline_tree = QTreeWidget()
        self.outline_tree.setHeaderLabels(["章节", "字数", "状态"])
        self.outline_tree.itemClicked.connect(self.on_outline_item_clicked)
        outline_layout.addWidget(self.outline_tree)
        
        self.content_tabs.addTab(self.outline_panel, "大纲管理")
    
    def setup_menu_bar(self):
        """设置菜单栏"""
        super().setup_menu_bar()
        
        # 项目菜单
        project_menu = self.menuBar().addMenu("项目(&P)")
        
        # 项目设置
        project_settings_action = QAction("项目设置", self)
        project_settings_action.triggered.connect(self.on_project_settings)
        project_menu.addAction(project_settings_action)
        
        project_menu.addSeparator()
        
        # 导出项目
        export_action = QAction("导出项目", self)
        export_action.triggered.connect(self.on_export_project)
        project_menu.addAction(export_action)
        
        # 编辑菜单扩展
        edit_menu = None
        for action in self.menuBar().actions():
            if action.text() == "编辑(&E)":
                edit_menu = action.menu()
                break
        
        if edit_menu:
            edit_menu.addSeparator()
            
            # 查找替换
            find_action = QAction("查找替换", self)
            find_action.setShortcut("Ctrl+F")
            find_action.triggered.connect(self.on_find_replace)
            edit_menu.addAction(find_action)
        
        # AI菜单
        ai_menu = self.menuBar().addMenu("AI助手(&A)")
        
        # 续写
        continue_writing_action = QAction("续写内容", self)
        continue_writing_action.setShortcut("Ctrl+Shift+C")
        continue_writing_action.triggered.connect(self.on_continue_writing)
        ai_menu.addAction(continue_writing_action)
        
        # 改写
        rewrite_action = QAction("改写内容", self)
        rewrite_action.setShortcut("Ctrl+Shift+R")
        rewrite_action.triggered.connect(self.on_rewrite_content)
        ai_menu.addAction(rewrite_action)
        
        # 生成角色
        generate_character_action = QAction("生成角色", self)
        generate_character_action.triggered.connect(self.on_generate_character)
        ai_menu.addAction(generate_character_action)
    
    def setup_tool_bar(self):
        """设置工具栏"""
        toolbar = self.addToolBar("项目工具栏")
        toolbar.setMovable(False)
        
        # 保存
        save_action = QAction("保存", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.on_save)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # 添加章节
        add_chapter_action = QAction("添加章节", self)
        add_chapter_action.triggered.connect(self.on_add_chapter)
        toolbar.addAction(add_chapter_action)
        
        # 添加角色
        add_character_action = QAction("添加角色", self)
        add_character_action.triggered.connect(self.on_add_character)
        toolbar.addAction(add_character_action)
        
        toolbar.addSeparator()
        
        # AI续写
        ai_continue_action = QAction("AI续写", self)
        ai_continue_action.triggered.connect(self.on_continue_writing)
        toolbar.addAction(ai_continue_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        super().setup_status_bar()
        
        # 字数统计
        self.word_count_label = QLabel("字数: 0")
        self.statusBar().addPermanentWidget(self.word_count_label)
        
        # 章节信息
        self.chapter_info_label = QLabel("")
        self.statusBar().addPermanentWidget(self.chapter_info_label)
        
        # 进度条（用于AI生成等操作）
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)
    
    def connect_signals(self):
        """连接信号"""
        super().connect_signals()
        
        # 自动保存定时器
        self.auto_save_timer.timeout.connect(self.auto_save)
        self.auto_save_timer.start(30000)  # 30秒自动保存
    
    @handle_exceptions()
    def load_project(self):
        """加载项目"""
        try:
            if self.project_manager.load_project(self.project_path):
                self.load_project_tree()
                self.load_characters()
                self.load_outline()
                self.statusBar().showMessage(f"项目已加载: {self.project_path}")
            else:
                raise ProjectError(f"无法加载项目: {self.project_path}")
        except Exception as e:
            self.show_message("错误", f"加载项目失败: {e}", "error")
    
    def load_project_tree(self):
        """加载项目结构树"""
        self.project_tree.clear()
        
        # 根节点
        project_name = os.path.basename(self.project_path)
        root_item = QTreeWidgetItem([project_name])
        root_item.setData(0, Qt.ItemDataRole.UserRole, {"type": "project", "path": self.project_path})
        self.project_tree.addTopLevelItem(root_item)
        
        # 章节节点
        chapters_item = QTreeWidgetItem(["章节"])
        chapters_item.setData(0, Qt.ItemDataRole.UserRole, {"type": "chapters"})
        root_item.addChild(chapters_item)
        
        # 加载章节
        chapters = self.chapter_manager.get_all_chapters()
        for chapter in chapters:
            chapter_item = QTreeWidgetItem([chapter.get('title', '未命名章节')])
            chapter_item.setData(0, Qt.ItemDataRole.UserRole, {
                "type": "chapter", 
                "id": chapter.get('id'),
                "data": chapter
            })
            chapters_item.addChild(chapter_item)
        
        # 角色节点
        characters_item = QTreeWidgetItem(["角色"])
        characters_item.setData(0, Qt.ItemDataRole.UserRole, {"type": "characters"})
        root_item.addChild(characters_item)
        
        # 大纲节点
        outline_item = QTreeWidgetItem(["大纲"])
        outline_item.setData(0, Qt.ItemDataRole.UserRole, {"type": "outline"})
        root_item.addChild(outline_item)
        
        # 展开根节点
        root_item.setExpanded(True)
        chapters_item.setExpanded(True)
    
    def load_characters(self):
        """加载角色列表"""
        if hasattr(self, 'character_list'):
            self.character_list.clear()
            characters = self.character_manager.get_all_characters()
            for character in characters:
                item = QTreeWidgetItem([character.get('name', '未命名角色')])
                item.setData(0, Qt.ItemDataRole.UserRole, character)
                self.character_list.addTopLevelItem(item)
    
    def load_outline(self):
        """加载大纲"""
        if hasattr(self, 'outline_tree'):
            self.outline_tree.clear()
            outline = self.outline_manager.get_outline()
            for chapter in outline.get('chapters', []):
                item = QTreeWidgetItem([
                    chapter.get('title', '未命名章节'),
                    str(chapter.get('word_count', 0)),
                    chapter.get('status', '未开始')
                ])
                item.setData(0, Qt.ItemDataRole.UserRole, chapter)
                self.outline_tree.addTopLevelItem(item)
    
    def on_tree_item_clicked(self, item: QTreeWidgetItem, column: int):
        """树形控件项目点击"""
        data = item.data(0, Qt.ItemDataRole.UserRole)
        if data and data.get('type') == 'chapter':
            self.load_chapter(data.get('id'))
    
    def on_tree_item_double_clicked(self, item: QTreeWidgetItem, column: int):
        """树形控件项目双击"""
        data = item.data(0, Qt.ItemDataRole.UserRole)
        if data:
            item_type = data.get('type')
            if item_type == 'chapter':
                self.content_tabs.setCurrentIndex(0)  # 切换到编辑器
            elif item_type == 'characters':
                self.content_tabs.setCurrentIndex(1)  # 切换到角色管理
            elif item_type == 'outline':
                self.content_tabs.setCurrentIndex(2)  # 切换到大纲管理
    
    @handle_exceptions()
    def load_chapter(self, chapter_id: str):
        """加载章节内容"""
        try:
            chapter = self.chapter_manager.get_chapter(chapter_id)
            if chapter:
                self.current_chapter_id = chapter_id
                self.editor_widget.set_content(chapter.get('content', ''))
                self.chapter_info_label.setText(f"章节: {chapter.get('title', '未命名')}")
                self.update_word_count()
        except Exception as e:
            self.show_message("错误", f"加载章节失败: {e}", "error")
    
    def on_content_changed(self, content: str):
        """内容变化处理"""
        self.is_modified = True
        self.update_word_count()
        self.content_changed.emit("chapter", content)
    
    def on_save_requested(self):
        """保存请求处理"""
        self.on_save()
    
    def update_word_count(self):
        """更新字数统计"""
        if self.editor_widget:
            content = self.editor_widget.get_content()
            word_count = len(content.replace(' ', '').replace('\n', ''))
            self.word_count_label.setText(f"字数: {word_count}")
    
    def close_tab(self, index: int):
        """关闭标签页"""
        if index > 0:  # 不允许关闭编辑器标签页
            self.content_tabs.removeTab(index)
    
    # 菜单和工具栏事件处理
    def on_project_settings(self):
        """项目设置"""
        # TODO: 实现项目设置对话框
        self.show_message("提示", "项目设置功能正在开发中")
    
    def on_export_project(self):
        """导出项目"""
        # TODO: 实现项目导出功能
        self.show_message("提示", "项目导出功能正在开发中")
    
    def on_find_replace(self):
        """查找替换"""
        # TODO: 实现查找替换对话框
        self.show_message("提示", "查找替换功能正在开发中")
    
    @handle_exceptions()
    def on_add_chapter(self):
        """添加章节"""
        try:
            chapter_data = {
                'title': f'第{len(self.chapter_manager.get_all_chapters()) + 1}章',
                'content': '',
                'order': len(self.chapter_manager.get_all_chapters())
            }
            chapter_id = self.chapter_manager.create_chapter(chapter_data)
            self.load_project_tree()
            self.load_chapter(chapter_id)
        except Exception as e:
            self.show_message("错误", f"添加章节失败: {e}", "error")
    
    def on_add_character(self):
        """添加角色"""
        # 切换到角色管理标签页
        self.content_tabs.setCurrentIndex(1)
        
        # 清空编辑区域
        self.character_name_edit.clear()
        self.character_desc_edit.clear()
        self.current_character_id = None
    
    def on_character_selected(self, item: QTreeWidgetItem, column: int):
        """选择角色"""
        character_data = item.data(0, Qt.ItemDataRole.UserRole)
        if character_data:
            self.current_character_id = character_data.get('id')
            self.character_name_edit.setPlainText(character_data.get('name', ''))
            self.character_desc_edit.setPlainText(character_data.get('description', ''))
    
    @handle_exceptions()
    def on_save_character(self):
        """保存角色"""
        try:
            character_data = {
                'name': self.character_name_edit.toPlainText(),
                'description': self.character_desc_edit.toPlainText()
            }
            
            if self.current_character_id:
                self.character_manager.update_character(self.current_character_id, character_data)
            else:
                self.character_manager.create_character(character_data)
            
            self.load_characters()
            self.show_message("成功", "角色已保存")
        except Exception as e:
            self.show_message("错误", f"保存角色失败: {e}", "error")
    
    def on_add_outline_chapter(self):
        """添加大纲章节"""
        # TODO: 实现大纲章节添加
        self.show_message("提示", "大纲章节添加功能正在开发中")
    
    def on_outline_item_clicked(self, item: QTreeWidgetItem, column: int):
        """大纲项目点击"""
        # TODO: 实现大纲项目选择处理
        pass
    
    @handle_exceptions()
    def on_generate_outline(self):
        """AI生成大纲"""
        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度
            self.statusBar().showMessage("正在生成大纲...")
            
            # TODO: 实现AI大纲生成
            self.show_message("提示", "AI大纲生成功能正在开发中")
            
        except Exception as e:
            self.show_message("错误", f"生成大纲失败: {e}", "error")
        finally:
            self.progress_bar.setVisible(False)
            self.statusBar().showMessage("就绪")
    
    @handle_exceptions()
    def on_continue_writing(self):
        """AI续写"""
        try:
            if not self.current_chapter_id:
                self.show_message("提示", "请先选择一个章节")
                return
            
            current_content = self.editor_widget.get_content()
            if not current_content.strip():
                self.show_message("提示", "请先输入一些内容作为续写的基础")
                return
            
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            self.statusBar().showMessage("正在AI续写...")
            
            # TODO: 实现AI续写功能
            self.show_message("提示", "AI续写功能正在开发中")
            
        except Exception as e:
            self.show_message("错误", f"AI续写失败: {e}", "error")
        finally:
            self.progress_bar.setVisible(False)
            self.statusBar().showMessage("就绪")
    
    def on_rewrite_content(self):
        """AI改写"""
        # TODO: 实现AI改写功能
        self.show_message("提示", "AI改写功能正在开发中")
    
    def on_generate_character(self):
        """AI生成角色"""
        # TODO: 实现AI角色生成功能
        self.show_message("提示", "AI角色生成功能正在开发中")
    
    @handle_exceptions()
    def auto_save(self):
        """自动保存"""
        if self.is_modified and self.current_chapter_id:
            try:
                content = self.editor_widget.get_content()
                self.chapter_manager.update_chapter(self.current_chapter_id, {'content': content})
                self.is_modified = False
                self.statusBar().showMessage("自动保存完成", 2000)
            except Exception as e:
                self.logger.error(f"自动保存失败: {e}")
    
    # 重写基类方法
    def on_save(self):
        """保存操作"""
        if self.current_chapter_id:
            try:
                content = self.editor_widget.get_content()
                self.chapter_manager.update_chapter(self.current_chapter_id, {'content': content})
                self.is_modified = False
                self.statusBar().showMessage("保存成功", 2000)
            except Exception as e:
                self.show_message("错误", f"保存失败: {e}", "error")
    
    def cleanup(self):
        """清理资源"""
        # 停止自动保存定时器
        if self.auto_save_timer.isActive():
            self.auto_save_timer.stop()
        
        # 保存当前状态
        if self.is_modified:
            self.auto_save()