# -*- coding: utf-8 -*-
"""
数据库管理器测试
"""

import pytest
import sqlite3
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from bamboofall.database.database_manager import DatabaseManager
from bamboofall.models.project import Project
from bamboofall.models.character import Character
from bamboofall.models.chapter import Chapter
from bamboofall.models.outline import Outline


class TestDatabaseManager:
    """DatabaseManager测试类"""
    
    @pytest.fixture
    def temp_db_path(self):
        """临时数据库路径fixture"""
        fd, path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        yield path
        try:
            os.unlink(path)
        except OSError:
            pass
    
    @pytest.fixture
    def db_manager(self, temp_db_path):
        """DatabaseManager实例fixture"""
        manager = DatabaseManager(temp_db_path)
        return manager
    
    @pytest.fixture
    def sample_project(self):
        """示例项目fixture"""
        return Project(
            id="test-project-1",
            name="测试项目",
            description="这是一个测试项目",
            genre="科幻",
            target_words=100000,
            created_at="2024-01-01T00:00:00"
        )
    
    @pytest.fixture
    def sample_character(self):
        """示例角色fixture"""
        return Character(
            id="char-1",
            project_id="test-project-1",
            name="主角",
            description="故事的主人公",
            role="protagonist",
            age=25,
            gender="male"
        )
    
    def test_init(self, temp_db_path):
        """测试初始化"""
        manager = DatabaseManager(temp_db_path)
        
        assert manager.db_path == temp_db_path
        assert os.path.exists(temp_db_path)
        
        # 验证数据库连接
        with manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            # 检查基本表是否存在
            expected_tables = ['projects', 'characters', 'chapters', 'outlines']
            for table in expected_tables:
                assert table in tables
    
    def test_get_connection(self, db_manager):
        """测试获取数据库连接"""
        with db_manager.get_connection() as conn:
            assert isinstance(conn, sqlite3.Connection)
            
            # 测试连接可用性
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            assert result[0] == 1
    
    def test_create_project(self, db_manager, sample_project):
        """测试创建项目"""
        created_project = db_manager.create_project(sample_project)
        
        assert created_project.id == sample_project.id
        assert created_project.name == sample_project.name
        assert created_project.description == sample_project.description
        
        # 验证数据库中的数据
        retrieved_project = db_manager.get_project(sample_project.id)
        assert retrieved_project is not None
        assert retrieved_project.name == sample_project.name
    
    def test_get_project(self, db_manager, sample_project):
        """测试获取项目"""
        # 先创建项目
        db_manager.create_project(sample_project)
        
        # 获取项目
        project = db_manager.get_project(sample_project.id)
        
        assert project is not None
        assert project.id == sample_project.id
        assert project.name == sample_project.name
        assert project.description == sample_project.description
    
    def test_get_project_not_found(self, db_manager):
        """测试获取不存在的项目"""
        project = db_manager.get_project("non-existent")
        assert project is None
    
    def test_update_project(self, db_manager, sample_project):
        """测试更新项目"""
        # 先创建项目
        db_manager.create_project(sample_project)
        
        # 更新项目
        sample_project.name = "更新的项目名"
        sample_project.description = "更新的描述"
        sample_project.target_words = 150000
        
        updated_project = db_manager.update_project(sample_project)
        
        assert updated_project.name == "更新的项目名"
        assert updated_project.description == "更新的描述"
        assert updated_project.target_words == 150000
        
        # 验证数据库中的更新
        retrieved_project = db_manager.get_project(sample_project.id)
        assert retrieved_project.name == "更新的项目名"
    
    def test_delete_project(self, db_manager, sample_project):
        """测试删除项目"""
        # 先创建项目
        db_manager.create_project(sample_project)
        
        # 验证项目存在
        assert db_manager.get_project(sample_project.id) is not None
        
        # 删除项目
        db_manager.delete_project(sample_project.id)
        
        # 验证项目已删除
        assert db_manager.get_project(sample_project.id) is None
    
    def test_get_all_projects(self, db_manager):
        """测试获取所有项目"""
        # 创建多个项目
        projects = [
            Project(id="p1", name="项目1", description="描述1"),
            Project(id="p2", name="项目2", description="描述2"),
            Project(id="p3", name="项目3", description="描述3")
        ]
        
        for project in projects:
            db_manager.create_project(project)
        
        # 获取所有项目
        all_projects = db_manager.get_all_projects()
        
        assert len(all_projects) == 3
        project_names = [p.name for p in all_projects]
        assert "项目1" in project_names
        assert "项目2" in project_names
        assert "项目3" in project_names
    
    def test_get_recent_projects(self, db_manager):
        """测试获取最近项目"""
        # 创建多个项目（模拟不同的创建时间）
        projects = [
            Project(id="p1", name="项目1", created_at="2024-01-01T00:00:00"),
            Project(id="p2", name="项目2", created_at="2024-01-02T00:00:00"),
            Project(id="p3", name="项目3", created_at="2024-01-03T00:00:00")
        ]
        
        for project in projects:
            db_manager.create_project(project)
        
        # 获取最近的2个项目
        recent_projects = db_manager.get_recent_projects(limit=2)
        
        assert len(recent_projects) == 2
        # 应该按创建时间倒序排列
        assert recent_projects[0].name == "项目3"
        assert recent_projects[1].name == "项目2"
    
    def test_search_projects(self, db_manager):
        """测试搜索项目"""
        # 创建多个项目
        projects = [
            Project(id="p1", name="科幻小说", genre="科幻", description="关于未来的故事"),
            Project(id="p2", name="奇幻冒险", genre="奇幻", description="魔法世界的冒险"),
            Project(id="p3", name="现实主义", genre="现实", description="现代都市故事")
        ]
        
        for project in projects:
            db_manager.create_project(project)
        
        # 搜索包含"科幻"的项目
        results = db_manager.search_projects("科幻")
        assert len(results) == 1
        assert results[0].name == "科幻小说"
        
        # 搜索包含"故事"的项目
        results = db_manager.search_projects("故事")
        assert len(results) == 2  # "科幻小说"和"现实主义"都包含"故事"
    
    def test_project_exists(self, db_manager, sample_project):
        """测试检查项目是否存在"""
        # 项目不存在
        assert db_manager.project_exists(sample_project.id) is False
        
        # 创建项目
        db_manager.create_project(sample_project)
        
        # 项目存在
        assert db_manager.project_exists(sample_project.id) is True
    
    def test_create_character(self, db_manager, sample_project, sample_character):
        """测试创建角色"""
        # 先创建项目
        db_manager.create_project(sample_project)
        
        # 创建角色
        created_character = db_manager.create_character(sample_character)
        
        assert created_character.id == sample_character.id
        assert created_character.name == sample_character.name
        assert created_character.project_id == sample_project.id
        
        # 验证数据库中的数据
        retrieved_character = db_manager.get_character(sample_character.id)
        assert retrieved_character is not None
        assert retrieved_character.name == sample_character.name
    
    def test_get_project_characters(self, db_manager, sample_project):
        """测试获取项目角色"""
        # 先创建项目
        db_manager.create_project(sample_project)
        
        # 创建多个角色
        characters = [
            Character(id="c1", project_id=sample_project.id, name="主角", role="protagonist"),
            Character(id="c2", project_id=sample_project.id, name="反派", role="antagonist"),
            Character(id="c3", project_id=sample_project.id, name="配角", role="supporting")
        ]
        
        for character in characters:
            db_manager.create_character(character)
        
        # 获取项目角色
        project_characters = db_manager.get_project_characters(sample_project.id)
        
        assert len(project_characters) == 3
        character_names = [c.name for c in project_characters]
        assert "主角" in character_names
        assert "反派" in character_names
        assert "配角" in character_names
    
    def test_create_chapter(self, db_manager, sample_project):
        """测试创建章节"""
        # 先创建项目
        db_manager.create_project(sample_project)
        
        # 创建章节
        chapter = Chapter(
            id="ch1",
            project_id=sample_project.id,
            title="第一章",
            content="章节内容",
            order=1,
            word_count=100
        )
        
        created_chapter = db_manager.create_chapter(chapter)
        
        assert created_chapter.id == chapter.id
        assert created_chapter.title == chapter.title
        assert created_chapter.project_id == sample_project.id
        
        # 验证数据库中的数据
        retrieved_chapter = db_manager.get_chapter(chapter.id)
        assert retrieved_chapter is not None
        assert retrieved_chapter.title == chapter.title
    
    def test_get_project_chapters(self, db_manager, sample_project):
        """测试获取项目章节"""
        # 先创建项目
        db_manager.create_project(sample_project)
        
        # 创建多个章节
        chapters = [
            Chapter(id="ch1", project_id=sample_project.id, title="第一章", order=1),
            Chapter(id="ch2", project_id=sample_project.id, title="第二章", order=2),
            Chapter(id="ch3", project_id=sample_project.id, title="第三章", order=3)
        ]
        
        for chapter in chapters:
            db_manager.create_chapter(chapter)
        
        # 获取项目章节
        project_chapters = db_manager.get_project_chapters(sample_project.id)
        
        assert len(project_chapters) == 3
        # 应该按order排序
        assert project_chapters[0].title == "第一章"
        assert project_chapters[1].title == "第二章"
        assert project_chapters[2].title == "第三章"
    
    def test_create_outline(self, db_manager, sample_project):
        """测试创建大纲"""
        # 先创建项目
        db_manager.create_project(sample_project)
        
        # 创建大纲
        outline = Outline(
            id="outline1",
            project_id=sample_project.id,
            title="主要大纲",
            content="大纲内容",
            type="main"
        )
        
        created_outline = db_manager.create_outline(outline)
        
        assert created_outline.id == outline.id
        assert created_outline.title == outline.title
        assert created_outline.project_id == sample_project.id
        
        # 验证数据库中的数据
        retrieved_outline = db_manager.get_outline(outline.id)
        assert retrieved_outline is not None
        assert retrieved_outline.title == outline.title
    
    def test_get_project_statistics(self, db_manager, sample_project):
        """测试获取项目统计"""
        # 先创建项目
        db_manager.create_project(sample_project)
        
        # 创建一些数据
        characters = [
            Character(id="c1", project_id=sample_project.id, name="角色1"),
            Character(id="c2", project_id=sample_project.id, name="角色2")
        ]
        
        chapters = [
            Chapter(id="ch1", project_id=sample_project.id, title="章节1", word_count=1000),
            Chapter(id="ch2", project_id=sample_project.id, title="章节2", word_count=1500)
        ]
        
        for character in characters:
            db_manager.create_character(character)
        
        for chapter in chapters:
            db_manager.create_chapter(chapter)
        
        # 获取统计信息
        stats = db_manager.get_project_statistics(sample_project.id)
        
        assert stats["total_characters"] == 2
        assert stats["total_chapters"] == 2
        assert stats["total_words"] == 2500
        assert stats["completion_rate"] == 0.025  # 2500 / 100000
    
    def test_backup_database(self, db_manager, temp_db_path):
        """测试数据库备份"""
        # 创建一些数据
        project = Project(id="backup-test", name="备份测试项目")
        db_manager.create_project(project)
        
        # 备份数据库
        backup_path = temp_db_path + ".backup"
        db_manager.backup_database(backup_path)
        
        # 验证备份文件存在
        assert os.path.exists(backup_path)
        
        # 验证备份文件内容
        backup_manager = DatabaseManager(backup_path)
        backup_project = backup_manager.get_project("backup-test")
        assert backup_project is not None
        assert backup_project.name == "备份测试项目"
        
        # 清理
        os.unlink(backup_path)
    
    def test_restore_database(self, db_manager, temp_db_path):
        """测试数据库恢复"""
        # 创建原始数据
        original_project = Project(id="original", name="原始项目")
        db_manager.create_project(original_project)
        
        # 创建备份
        backup_path = temp_db_path + ".backup"
        db_manager.backup_database(backup_path)
        
        # 修改原始数据库
        new_project = Project(id="new", name="新项目")
        db_manager.create_project(new_project)
        
        # 恢复数据库
        db_manager.restore_database(backup_path)
        
        # 验证恢复结果
        restored_original = db_manager.get_project("original")
        restored_new = db_manager.get_project("new")
        
        assert restored_original is not None
        assert restored_original.name == "原始项目"
        assert restored_new is None  # 新项目应该不存在
        
        # 清理
        os.unlink(backup_path)
    
    def test_transaction_rollback(self, db_manager):
        """测试事务回滚"""
        project = Project(id="transaction-test", name="事务测试")
        
        try:
            with db_manager.get_connection() as conn:
                # 开始事务
                db_manager.create_project(project)
                
                # 验证项目已创建
                assert db_manager.get_project("transaction-test") is not None
                
                # 模拟错误
                raise Exception("模拟错误")
        except Exception:
            pass
        
        # 由于异常，事务应该被回滚
        # 注意：这个测试的行为取决于具体的事务实现
        # 如果使用了适当的事务管理，项目应该不存在
    
    def test_database_migration(self, temp_db_path):
        """测试数据库迁移"""
        # 创建旧版本数据库（模拟）
        with sqlite3.connect(temp_db_path) as conn:
            conn.execute("CREATE TABLE old_table (id TEXT PRIMARY KEY, name TEXT)")
            conn.execute("INSERT INTO old_table VALUES ('1', 'test')")
        
        # 初始化数据库管理器（应该触发迁移）
        manager = DatabaseManager(temp_db_path)
        
        # 验证新表存在
        with manager.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            assert 'projects' in tables
            assert 'characters' in tables
    
    def test_error_handling(self, db_manager):
        """测试错误处理"""
        # 测试创建重复ID的项目
        project1 = Project(id="duplicate", name="项目1")
        project2 = Project(id="duplicate", name="项目2")
        
        db_manager.create_project(project1)
        
        with pytest.raises(Exception):  # 应该抛出唯一约束错误
            db_manager.create_project(project2)
    
    def test_connection_pool(self, db_manager):
        """测试连接池功能"""
        # 测试多个并发连接
        connections = []
        
        for i in range(5):
            conn = db_manager.get_connection()
            connections.append(conn)
        
        # 所有连接都应该可用
        for conn in connections:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            assert result[0] == 1
        
        # 关闭连接
        for conn in connections:
            conn.close()