# -*- coding: utf-8 -*-
"""
大纲导出器

支持将大纲导出为多种格式：Markdown、Word、PDF、JSON等
"""

import logging
import json
import os
from typing import List, Optional, Dict, Any
from datetime import datetime
from pathlib import Path

from ..models.outline import OutlineNode, OutlineNodeType, OutlineNodeStatus
from ..exceptions.exceptions import ExportError, ValidationError

logger = logging.getLogger(__name__)


class OutlineExporter:
    """大纲导出器"""
    
    def __init__(self):
        self.supported_formats = [
            'markdown', 'md',
            'json',
            'txt', 'text',
            'html',
            'xml',
            'csv'
        ]
        
    def export_outline(self, nodes: List[OutlineNode], file_path: str, format_type: str = None) -> bool:
        """导出大纲
        
        Args:
            nodes: 大纲节点列表
            file_path: 导出文件路径
            format_type: 导出格式，如果为None则根据文件扩展名判断
            
        Returns:
            bool: 导出是否成功
        """
        try:
            # 确定导出格式
            if not format_type:
                format_type = self._get_format_from_extension(file_path)
                
            if format_type not in self.supported_formats:
                raise ValidationError(f"不支持的导出格式: {format_type}")
                
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 根据格式导出
            if format_type in ['markdown', 'md']:
                return self._export_markdown(nodes, file_path)
            elif format_type == 'json':
                return self._export_json(nodes, file_path)
            elif format_type in ['txt', 'text']:
                return self._export_text(nodes, file_path)
            elif format_type == 'html':
                return self._export_html(nodes, file_path)
            elif format_type == 'xml':
                return self._export_xml(nodes, file_path)
            elif format_type == 'csv':
                return self._export_csv(nodes, file_path)
            else:
                raise ValidationError(f"未实现的导出格式: {format_type}")
                
        except Exception as e:
            logger.error(f"导出大纲失败: {e}")
            raise ExportError(f"导出失败: {e}")
            
    def _get_format_from_extension(self, file_path: str) -> str:
        """从文件扩展名获取格式"""
        ext = Path(file_path).suffix.lower().lstrip('.')
        return ext if ext in self.supported_formats else 'txt'
        
    def _build_tree_structure(self, nodes: List[OutlineNode]) -> Dict[str, Any]:
        """构建树形结构"""
        node_dict = {node.id: node for node in nodes}
        root_nodes = []
        
        def build_node_tree(node: OutlineNode) -> Dict[str, Any]:
            node_data = {
                'node': node,
                'children': []
            }
            
            # 获取子节点
            children = [node_dict[child_id] for child_id in node.children_ids if child_id in node_dict]
            children.sort(key=lambda n: n.order_index or 0)
            
            for child in children:
                node_data['children'].append(build_node_tree(child))
                
            return node_data
            
        # 找到根节点
        for node in nodes:
            if not node.parent_id or node.parent_id not in node_dict:
                root_nodes.append(build_node_tree(node))
                
        return {'roots': root_nodes}
        
    def _export_markdown(self, nodes: List[OutlineNode], file_path: str) -> bool:
        """导出为Markdown格式"""
        try:
            tree = self._build_tree_structure(nodes)
            
            lines = []
            lines.append("# 大纲")
            lines.append("")
            lines.append(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            lines.append("")
            
            def write_node(node_data: Dict[str, Any], level: int = 1):
                node = node_data['node']
                
                # 标题
                prefix = '#' * min(level + 1, 6)
                lines.append(f"{prefix} {node.title or '未命名'}")
                lines.append("")
                
                # 基本信息
                lines.append(f"**类型**: {node.type.value}")
                lines.append(f"**状态**: {node.status.value}")
                
                if node.estimated_word_count:
                    lines.append(f"**预计字数**: {node.estimated_word_count}")
                if node.actual_word_count:
                    lines.append(f"**实际字数**: {node.actual_word_count}")
                    
                progress = node.get_progress()
                lines.append(f"**完成进度**: {progress:.1%}")
                lines.append("")
                
                # 内容
                if node.content:
                    lines.append("**内容描述**:")
                    lines.append("")
                    lines.append(node.content)
                    lines.append("")
                    
                # 关联信息
                if node.chapter_id or node.character_ids or node.scene_ids:
                    lines.append("**关联信息**:")
                    if node.chapter_id:
                        lines.append(f"- 关联章节: {node.chapter_id}")
                    if node.character_ids:
                        lines.append(f"- 相关角色: {', '.join(node.character_ids)}")
                    if node.scene_ids:
                        lines.append(f"- 相关场景: {', '.join(node.scene_ids)}")
                    lines.append("")
                    
                # 标签
                if node.tags:
                    lines.append(f"**标签**: {', '.join(node.tags)}")
                    lines.append("")
                    
                # 备注
                if node.notes:
                    lines.append("**备注**:")
                    lines.append("")
                    lines.append(node.notes)
                    lines.append("")
                    
                lines.append("---")
                lines.append("")
                
                # 递归处理子节点
                for child_data in node_data['children']:
                    write_node(child_data, level + 1)
                    
            # 写入所有根节点
            for root_data in tree['roots']:
                write_node(root_data)
                
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
                
            return True
            
        except Exception as e:
            logger.error(f"导出Markdown失败: {e}")
            return False
            
    def _export_json(self, nodes: List[OutlineNode], file_path: str) -> bool:
        """导出为JSON格式"""
        try:
            # 构建导出数据
            export_data = {
                'export_info': {
                    'format': 'json',
                    'version': '1.0',
                    'export_time': datetime.now().isoformat(),
                    'node_count': len(nodes)
                },
                'nodes': []
            }
            
            # 转换节点数据
            for node in nodes:
                node_data = {
                    'id': node.id,
                    'project_id': node.project_id,
                    'title': node.title,
                    'content': node.content,
                    'type': node.type.value,
                    'status': node.status.value,
                    'parent_id': node.parent_id,
                    'children_ids': node.children_ids,
                    'order_index': node.order_index,
                    'level': node.level,
                    'chapter_id': node.chapter_id,
                    'scene_ids': node.scene_ids,
                    'character_ids': node.character_ids,
                    'estimated_word_count': node.estimated_word_count,
                    'actual_word_count': node.actual_word_count,
                    'tags': node.tags,
                    'metadata': node.metadata,
                    'notes': node.notes,
                    'created_at': node.created_at.isoformat() if node.created_at else None,
                    'updated_at': node.updated_at.isoformat() if node.updated_at else None
                }
                export_data['nodes'].append(node_data)
                
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            logger.error(f"导出JSON失败: {e}")
            return False
            
    def _export_text(self, nodes: List[OutlineNode], file_path: str) -> bool:
        """导出为纯文本格式"""
        try:
            tree = self._build_tree_structure(nodes)
            
            lines = []
            lines.append("大纲")
            lines.append("=" * 50)
            lines.append("")
            lines.append(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            lines.append(f"节点总数: {len(nodes)}")
            lines.append("")
            
            def write_node(node_data: Dict[str, Any], level: int = 0):
                node = node_data['node']
                indent = "  " * level
                
                # 标题
                lines.append(f"{indent}{level + 1}. {node.title or '未命名'}")
                
                # 基本信息
                lines.append(f"{indent}   类型: {node.type.value}")
                lines.append(f"{indent}   状态: {node.status.value}")
                
                if node.estimated_word_count or node.actual_word_count:
                    word_info = []
                    if node.estimated_word_count:
                        word_info.append(f"预计{node.estimated_word_count}字")
                    if node.actual_word_count:
                        word_info.append(f"实际{node.actual_word_count}字")
                    lines.append(f"{indent}   字数: {', '.join(word_info)}")
                    
                progress = node.get_progress()
                lines.append(f"{indent}   进度: {progress:.1%}")
                
                # 内容
                if node.content:
                    content_lines = node.content.split('\n')
                    lines.append(f"{indent}   内容:")
                    for content_line in content_lines:
                        lines.append(f"{indent}     {content_line}")
                        
                # 关联信息
                relations = []
                if node.chapter_id:
                    relations.append(f"章节:{node.chapter_id}")
                if node.character_ids:
                    relations.append(f"角色:{','.join(node.character_ids)}")
                if relations:
                    lines.append(f"{indent}   关联: {'; '.join(relations)}")
                    
                # 标签
                if node.tags:
                    lines.append(f"{indent}   标签: {', '.join(node.tags)}")
                    
                # 备注
                if node.notes:
                    lines.append(f"{indent}   备注: {node.notes}")
                    
                lines.append("")
                
                # 递归处理子节点
                for child_data in node_data['children']:
                    write_node(child_data, level + 1)
                    
            # 写入所有根节点
            for root_data in tree['roots']:
                write_node(root_data)
                
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
                
            return True
            
        except Exception as e:
            logger.error(f"导出文本失败: {e}")
            return False
            
    def _export_html(self, nodes: List[OutlineNode], file_path: str) -> bool:
        """导出为HTML格式"""
        try:
            tree = self._build_tree_structure(nodes)
            
            html_lines = []
            html_lines.append("<!DOCTYPE html>")
            html_lines.append("<html lang='zh-CN'>")
            html_lines.append("<head>")
            html_lines.append("    <meta charset='UTF-8'>")
            html_lines.append("    <meta name='viewport' content='width=device-width, initial-scale=1.0'>")
            html_lines.append("    <title>大纲</title>")
            html_lines.append("    <style>")
            html_lines.append("        body { font-family: Arial, sans-serif; margin: 20px; }")
            html_lines.append("        .node { margin: 10px 0; padding: 10px; border-left: 3px solid #ccc; }")
            html_lines.append("        .node-title { font-weight: bold; font-size: 1.2em; }")
            html_lines.append("        .node-info { color: #666; font-size: 0.9em; margin: 5px 0; }")
            html_lines.append("        .node-content { margin: 10px 0; }")
            html_lines.append("        .level-0 { margin-left: 0; }")
            html_lines.append("        .level-1 { margin-left: 20px; }")
            html_lines.append("        .level-2 { margin-left: 40px; }")
            html_lines.append("        .level-3 { margin-left: 60px; }")
            html_lines.append("        .level-4 { margin-left: 80px; }")
            html_lines.append("        .progress-bar { width: 100px; height: 10px; background: #eee; border-radius: 5px; overflow: hidden; }")
            html_lines.append("        .progress-fill { height: 100%; background: #4CAF50; }")
            html_lines.append("    </style>")
            html_lines.append("</head>")
            html_lines.append("<body>")
            html_lines.append("    <h1>大纲</h1>")
            html_lines.append(f"    <p>导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>")
            html_lines.append(f"    <p>节点总数: {len(nodes)}</p>")
            
            def write_node(node_data: Dict[str, Any], level: int = 0):
                node = node_data['node']
                
                html_lines.append(f"    <div class='node level-{level}'>")
                html_lines.append(f"        <div class='node-title'>{node.title or '未命名'}</div>")
                
                # 基本信息
                info_parts = [
                    f"类型: {node.type.value}",
                    f"状态: {node.status.value}"
                ]
                
                if node.estimated_word_count:
                    info_parts.append(f"预计字数: {node.estimated_word_count}")
                if node.actual_word_count:
                    info_parts.append(f"实际字数: {node.actual_word_count}")
                    
                html_lines.append(f"        <div class='node-info'>{' | '.join(info_parts)}</div>")
                
                # 进度条
                progress = node.get_progress()
                html_lines.append("        <div class='node-info'>")
                html_lines.append(f"            进度: {progress:.1%}")
                html_lines.append("            <div class='progress-bar'>")
                html_lines.append(f"                <div class='progress-fill' style='width: {progress * 100}%'></div>")
                html_lines.append("            </div>")
                html_lines.append("        </div>")
                
                # 内容
                if node.content:
                    content_html = node.content.replace('\n', '<br>')
                    html_lines.append(f"        <div class='node-content'>{content_html}</div>")
                    
                # 关联信息
                if node.chapter_id or node.character_ids:
                    relations = []
                    if node.chapter_id:
                        relations.append(f"章节: {node.chapter_id}")
                    if node.character_ids:
                        relations.append(f"角色: {', '.join(node.character_ids)}")
                    html_lines.append(f"        <div class='node-info'>关联: {'; '.join(relations)}</div>")
                    
                # 标签
                if node.tags:
                    html_lines.append(f"        <div class='node-info'>标签: {', '.join(node.tags)}</div>")
                    
                # 备注
                if node.notes:
                    notes_html = node.notes.replace('\n', '<br>')
                    html_lines.append(f"        <div class='node-info'>备注: {notes_html}</div>")
                    
                html_lines.append("    </div>")
                
                # 递归处理子节点
                for child_data in node_data['children']:
                    write_node(child_data, level + 1)
                    
            # 写入所有根节点
            for root_data in tree['roots']:
                write_node(root_data)
                
            html_lines.append("</body>")
            html_lines.append("</html>")
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(html_lines))
                
            return True
            
        except Exception as e:
            logger.error(f"导出HTML失败: {e}")
            return False
            
    def _export_xml(self, nodes: List[OutlineNode], file_path: str) -> bool:
        """导出为XML格式"""
        try:
            tree = self._build_tree_structure(nodes)
            
            xml_lines = []
            xml_lines.append("<?xml version='1.0' encoding='UTF-8'?>")
            xml_lines.append("<outline>")
            xml_lines.append(f"    <export_info>")
            xml_lines.append(f"        <export_time>{datetime.now().isoformat()}</export_time>")
            xml_lines.append(f"        <node_count>{len(nodes)}</node_count>")
            xml_lines.append(f"    </export_info>")
            xml_lines.append("    <nodes>")
            
            def write_node(node_data: Dict[str, Any], level: int = 2):
                node = node_data['node']
                indent = "    " * level
                
                xml_lines.append(f"{indent}<node id='{node.id}'>")
                xml_lines.append(f"{indent}    <title><![CDATA[{node.title or ''}]]></title>")
                xml_lines.append(f"{indent}    <type>{node.type.value}</type>")
                xml_lines.append(f"{indent}    <status>{node.status.value}</status>")
                xml_lines.append(f"{indent}    <level>{node.level or 0}</level>")
                xml_lines.append(f"{indent}    <order_index>{node.order_index or 0}</order_index>")
                
                if node.content:
                    xml_lines.append(f"{indent}    <content><![CDATA[{node.content}]]></content>")
                    
                if node.estimated_word_count:
                    xml_lines.append(f"{indent}    <estimated_word_count>{node.estimated_word_count}</estimated_word_count>")
                if node.actual_word_count:
                    xml_lines.append(f"{indent}    <actual_word_count>{node.actual_word_count}</actual_word_count>")
                    
                progress = node.get_progress()
                xml_lines.append(f"{indent}    <progress>{progress}</progress>")
                
                if node.chapter_id:
                    xml_lines.append(f"{indent}    <chapter_id>{node.chapter_id}</chapter_id>")
                    
                if node.character_ids:
                    xml_lines.append(f"{indent}    <character_ids>")
                    for char_id in node.character_ids:
                        xml_lines.append(f"{indent}        <character_id>{char_id}</character_id>")
                    xml_lines.append(f"{indent}    </character_ids>")
                    
                if node.tags:
                    xml_lines.append(f"{indent}    <tags>")
                    for tag in node.tags:
                        xml_lines.append(f"{indent}        <tag>{tag}</tag>")
                    xml_lines.append(f"{indent}    </tags>")
                    
                if node.notes:
                    xml_lines.append(f"{indent}    <notes><![CDATA[{node.notes}]]></notes>")
                    
                if node.created_at:
                    xml_lines.append(f"{indent}    <created_at>{node.created_at.isoformat()}</created_at>")
                if node.updated_at:
                    xml_lines.append(f"{indent}    <updated_at>{node.updated_at.isoformat()}</updated_at>")
                    
                # 子节点
                if node_data['children']:
                    xml_lines.append(f"{indent}    <children>")
                    for child_data in node_data['children']:
                        write_node(child_data, level + 2)
                    xml_lines.append(f"{indent}    </children>")
                    
                xml_lines.append(f"{indent}</node>")
                
            # 写入所有根节点
            for root_data in tree['roots']:
                write_node(root_data)
                
            xml_lines.append("    </nodes>")
            xml_lines.append("</outline>")
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(xml_lines))
                
            return True
            
        except Exception as e:
            logger.error(f"导出XML失败: {e}")
            return False
            
    def _export_csv(self, nodes: List[OutlineNode], file_path: str) -> bool:
        """导出为CSV格式"""
        try:
            import csv
            
            # CSV列标题
            headers = [
                'ID', '标题', '类型', '状态', '层级', '排序', '父节点ID',
                '预计字数', '实际字数', '进度', '关联章节', '相关角色',
                '标签', '内容', '备注', '创建时间', '更新时间'
            ]
            
            # 准备数据
            rows = []
            for node in nodes:
                progress = node.get_progress()
                row = [
                    node.id,
                    node.title or '',
                    node.type.value,
                    node.status.value,
                    node.level or 0,
                    node.order_index or 0,
                    node.parent_id or '',
                    node.estimated_word_count or 0,
                    node.actual_word_count or 0,
                    f"{progress:.1%}",
                    node.chapter_id or '',
                    ','.join(node.character_ids) if node.character_ids else '',
                    ','.join(node.tags) if node.tags else '',
                    (node.content or '').replace('\n', ' '),
                    (node.notes or '').replace('\n', ' '),
                    node.created_at.strftime('%Y-%m-%d %H:%M:%S') if node.created_at else '',
                    node.updated_at.strftime('%Y-%m-%d %H:%M:%S') if node.updated_at else ''
                ]
                rows.append(row)
                
            # 保存CSV文件
            with open(file_path, 'w', encoding='utf-8-sig', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(headers)
                writer.writerows(rows)
                
            return True
            
        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            return False
            
    def get_supported_formats(self) -> List[str]:
        """获取支持的导出格式"""
        return self.supported_formats.copy()
        
    def get_format_description(self, format_type: str) -> str:
        """获取格式描述"""
        descriptions = {
            'markdown': 'Markdown文档 (*.md)',
            'md': 'Markdown文档 (*.md)',
            'json': 'JSON数据文件 (*.json)',
            'txt': '纯文本文件 (*.txt)',
            'text': '纯文本文件 (*.txt)',
            'html': 'HTML网页文件 (*.html)',
            'xml': 'XML数据文件 (*.xml)',
            'csv': 'CSV表格文件 (*.csv)'
        }
        return descriptions.get(format_type, f'{format_type}文件')


# 全局导出器实例
_outline_exporter = None


def get_outline_exporter() -> OutlineExporter:
    """获取大纲导出器实例"""
    global _outline_exporter
    if _outline_exporter is None:
        _outline_exporter = OutlineExporter()
    return _outline_exporter