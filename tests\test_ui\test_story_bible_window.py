"""故事圣经窗口测试

测试故事圣经窗口的各项功能。
"""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from PyQt6.QtTest import QTest

from src.bamboofall.ui.windows.story_bible_window import StoryBibleWindow
from src.bamboofall.core.project_manager import ProjectManager
from src.bamboofall.core.project import Project


class TestStoryBibleWindow:
    """故事圣经窗口测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            return QApplication([])
        return QApplication.instance()
    
    @pytest.fixture
    def temp_project_dir(self):
        """创建临时项目目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            project_path = Path(temp_dir) / "test_project"
            project_path.mkdir()
            yield project_path
    
    @pytest.fixture
    def mock_project(self, temp_project_dir):
        """创建模拟项目"""
        project = Mock(spec=Project)
        project.project_path = temp_project_dir
        project.name = "测试项目"
        project.description = "测试项目描述"
        return project
    
    @pytest.fixture
    def mock_project_manager(self, mock_project):
        """创建模拟项目管理器"""
        manager = Mock(spec=ProjectManager)
        manager.current_project = mock_project
        return manager
    
    @pytest.fixture
    def story_bible_window(self, app, mock_project_manager):
        """创建故事圣经窗口实例"""
        with patch('src.bamboofall.ui.windows.story_bible_window.get_theme_manager'), \
             patch('src.bamboofall.ui.windows.story_bible_window.get_config_manager'), \
             patch('src.bamboofall.ui.windows.story_bible_window.AIService'):
            window = StoryBibleWindow(mock_project_manager)
            yield window
            window.close()
    
    def test_window_initialization(self, story_bible_window):
        """测试窗口初始化"""
        assert story_bible_window.windowTitle() == "笔落 - 故事圣经"
        assert story_bible_window.isVisible() == False
        assert story_bible_window.minimumSize().width() == 1000
        assert story_bible_window.minimumSize().height() == 700
    
    def test_ui_components_creation(self, story_bible_window):
        """测试UI组件创建"""
        # 检查主要组件是否创建
        assert story_bible_window.central_widget is not None
        assert story_bible_window.main_splitter is not None
        assert story_bible_window.left_panel is not None
        assert story_bible_window.right_panel is not None
        
        # 检查左侧面板组件
        assert story_bible_window.category_tree is not None
        assert story_bible_window.search_edit is not None
        assert story_bible_window.add_btn is not None
        assert story_bible_window.delete_btn is not None
        
        # 检查右侧面板组件
        assert story_bible_window.content_tabs is not None
        assert story_bible_window.character_tab is not None
        assert story_bible_window.world_tab is not None
        assert story_bible_window.timeline_tab is not None
        assert story_bible_window.location_tab is not None
        assert story_bible_window.relationship_tab is not None
    
    def test_category_tree_setup(self, story_bible_window):
        """测试分类树设置"""
        tree = story_bible_window.category_tree
        
        # 检查顶级项目
        assert tree.topLevelItemCount() == 5
        
        # 检查各个分类
        categories = []
        for i in range(tree.topLevelItemCount()):
            item = tree.topLevelItem(i)
            categories.append(item.text(0))
        
        expected_categories = ["角色", "世界观", "时间线", "地点", "关系图谱"]
        assert all(cat in categories for cat in expected_categories)
        
        # 检查世界观子分类
        world_item = None
        for i in range(tree.topLevelItemCount()):
            item = tree.topLevelItem(i)
            if item.text(0) == "世界观":
                world_item = item
                break
        
        assert world_item is not None
        assert world_item.childCount() == 8
    
    def test_character_tab_components(self, story_bible_window):
        """测试角色标签页组件"""
        # 检查角色表单字段
        assert story_bible_window.char_name_edit is not None
        assert story_bible_window.char_alias_edit is not None
        assert story_bible_window.char_age_spin is not None
        assert story_bible_window.char_gender_combo is not None
        assert story_bible_window.char_occupation_edit is not None
        assert story_bible_window.char_birthplace_edit is not None
        
        # 检查文本编辑器
        assert story_bible_window.char_appearance_edit is not None
        assert story_bible_window.char_personality_edit is not None
        assert story_bible_window.char_background_edit is not None
        
        # 检查关系表格
        assert story_bible_window.char_relations_table is not None
        assert story_bible_window.char_relations_table.columnCount() == 3
    
    def test_world_tab_components(self, story_bible_window):
        """测试世界观标签页组件"""
        assert story_bible_window.world_categories is not None
        assert story_bible_window.world_content is not None
        assert story_bible_window.world_title_label is not None
        
        # 检查世界观分类
        categories_widget = story_bible_window.world_categories
        assert categories_widget.count() == 8
        
        expected_categories = [
            "设定总览", "历史背景", "地理环境", "社会制度",
            "文化习俗", "科技水平", "魔法体系", "种族设定"
        ]
        
        for i, expected in enumerate(expected_categories):
            item = categories_widget.item(i)
            assert item.text() == expected
    
    def test_timeline_tab_components(self, story_bible_window):
        """测试时间线标签页组件"""
        assert story_bible_window.timeline_table is not None
        assert story_bible_window.timeline_details is not None
        
        # 检查时间线表格列
        table = story_bible_window.timeline_table
        assert table.columnCount() == 4
        
        expected_headers = ["日期", "事件标题", "重要性", "相关角色"]
        for i, expected in enumerate(expected_headers):
            assert table.horizontalHeaderItem(i).text() == expected
    
    def test_location_tab_components(self, story_bible_window):
        """测试地点标签页组件"""
        assert story_bible_window.location_tree is not None
        assert story_bible_window.location_name_edit is not None
        assert story_bible_window.location_type_combo is not None
        assert story_bible_window.location_parent_combo is not None
        assert story_bible_window.location_description is not None
        assert story_bible_window.location_events_list is not None
    
    def test_relationship_tab_components(self, story_bible_window):
        """测试关系图谱标签页组件"""
        assert story_bible_window.relationship_view is not None
    
    def test_default_story_bible_data_creation(self, story_bible_window):
        """测试默认故事圣经数据创建"""
        data = story_bible_window.create_default_story_bible_data()
        
        # 检查数据结构
        assert "characters" in data
        assert "world" in data
        assert "timeline" in data
        assert "locations" in data
        assert "relationships" in data
        assert "metadata" in data
        
        # 检查世界观默认分类
        world_data = data["world"]
        expected_world_categories = [
            "设定总览", "历史背景", "地理环境", "社会制度",
            "文化习俗", "科技水平", "魔法体系", "种族设定"
        ]
        
        for category in expected_world_categories:
            assert category in world_data
            assert world_data[category] == ""
        
        # 检查元数据
        metadata = data["metadata"]
        assert "created_at" in metadata
        assert "updated_at" in metadata
        assert "version" in metadata
        assert metadata["version"] == "1.0"
    
    def test_story_bible_data_loading(self, story_bible_window, temp_project_dir):
        """测试故事圣经数据加载"""
        # 创建测试数据文件
        test_data = {
            "characters": {
                "char1": {"name": "测试角色", "age": 25}
            },
            "world": {
                "设定总览": "测试世界观"
            },
            "timeline": [],
            "locations": {},
            "relationships": {},
            "metadata": {
                "version": "1.0"
            }
        }
        
        story_bible_file = temp_project_dir / "story_bible.json"
        with open(story_bible_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False)
        
        # 重新加载数据
        story_bible_window.load_project_data()
        
        # 检查数据是否正确加载
        assert story_bible_window.story_bible_data == test_data
    
    def test_story_bible_data_saving(self, story_bible_window, temp_project_dir):
        """测试故事圣经数据保存"""
        # 修改数据
        story_bible_window.story_bible_data["world"]["设定总览"] = "修改后的世界观"
        
        # 保存数据
        story_bible_window.save_story_bible_data()
        
        # 检查文件是否创建
        story_bible_file = temp_project_dir / "story_bible.json"
        assert story_bible_file.exists()
        
        # 检查文件内容
        with open(story_bible_file, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        assert saved_data["world"]["设定总览"] == "修改后的世界观"
        assert "updated_at" in saved_data["metadata"]
    
    def test_category_selection(self, story_bible_window):
        """测试分类选择"""
        tree = story_bible_window.category_tree
        tabs = story_bible_window.content_tabs
        
        # 选择角色分类
        characters_item = tree.topLevelItem(0)  # 角色
        story_bible_window.on_category_selected(characters_item, 0)
        assert tabs.currentWidget() == story_bible_window.character_tab
        
        # 选择世界观分类
        world_item = tree.topLevelItem(1)  # 世界观
        story_bible_window.on_category_selected(world_item, 0)
        assert tabs.currentWidget() == story_bible_window.world_tab
        
        # 选择时间线分类
        timeline_item = tree.topLevelItem(2)  # 时间线
        story_bible_window.on_category_selected(timeline_item, 0)
        assert tabs.currentWidget() == story_bible_window.timeline_tab
    
    def test_world_category_selection(self, story_bible_window):
        """测试世界观分类选择"""
        categories_widget = story_bible_window.world_categories
        title_label = story_bible_window.world_title_label
        content_edit = story_bible_window.world_content
        
        # 选择第一个分类
        first_item = categories_widget.item(0)
        story_bible_window.on_world_category_selected(first_item)
        
        assert title_label.text() == "设定总览"
        # 内容应该为空（默认数据）
        assert content_edit.toPlainText() == ""
    
    def test_character_form_validation(self, story_bible_window):
        """测试角色表单验证"""
        # 测试年龄范围
        age_spin = story_bible_window.char_age_spin
        assert age_spin.minimum() == 0
        assert age_spin.maximum() == 1000
        
        # 测试性别选项
        gender_combo = story_bible_window.char_gender_combo
        expected_genders = ["男", "女", "其他", "未知"]
        actual_genders = [gender_combo.itemText(i) for i in range(gender_combo.count())]
        assert actual_genders == expected_genders
    
    def test_location_type_options(self, story_bible_window):
        """测试地点类型选项"""
        type_combo = story_bible_window.location_type_combo
        expected_types = ["城市", "村庄", "建筑", "自然景观", "其他"]
        actual_types = [type_combo.itemText(i) for i in range(type_combo.count())]
        assert actual_types == expected_types
    
    @patch('src.bamboofall.ui.windows.story_bible_window.StoryBibleWindow.show_message')
    def test_ai_generation_requests(self, mock_show_message, story_bible_window):
        """测试AI生成请求"""
        # 测试角色外貌生成（无角色名）
        story_bible_window.generate_character_appearance()
        mock_show_message.assert_called_with("提示", "请先输入角色姓名")
        
        # 测试角色外貌生成（有角色名）
        story_bible_window.char_name_edit.setText("测试角色")
        
        with patch.object(story_bible_window, 'ai_generation_requested') as mock_signal:
            story_bible_window.generate_character_appearance()
            mock_signal.emit.assert_called_with("character_appearance", {"name": "测试角色"})
    
    @patch('src.bamboofall.ui.windows.story_bible_window.StoryBibleWindow.show_message')
    def test_location_description_generation(self, mock_show_message, story_bible_window):
        """测试地点描述生成"""
        # 测试无地点名
        story_bible_window.generate_location_description()
        mock_show_message.assert_called_with("提示", "请先输入地点名称")
        
        # 测试有地点名
        story_bible_window.location_name_edit.setText("测试地点")
        
        with patch.object(story_bible_window, 'ai_generation_requested') as mock_signal:
            story_bible_window.generate_location_description()
            mock_signal.emit.assert_called_with("location_description", {"name": "测试地点"})
    
    @patch('src.bamboofall.ui.windows.story_bible_window.StoryBibleWindow.show_message')
    def test_world_content_generation(self, mock_show_message, story_bible_window):
        """测试世界观内容生成"""
        # 测试无选中分类
        story_bible_window.generate_world_content()
        mock_show_message.assert_called_with("提示", "请先选择一个世界观分类")
        
        # 测试有选中分类
        categories_widget = story_bible_window.world_categories
        categories_widget.setCurrentRow(0)
        
        with patch.object(story_bible_window, 'ai_generation_requested') as mock_signal:
            story_bible_window.generate_world_content()
            mock_signal.emit.assert_called_with("world_content", {"category": "设定总览"})
    
    def test_search_functionality(self, story_bible_window):
        """测试搜索功能"""
        search_edit = story_bible_window.search_edit
        
        # 测试搜索框占位符
        assert search_edit.placeholderText() == "搜索..."
        
        # 测试搜索文本变化
        search_edit.setText("测试搜索")
        # 由于搜索功能还在开发中，这里只测试UI响应
        assert search_edit.text() == "测试搜索"
    
    def test_window_cleanup(self, story_bible_window, temp_project_dir):
        """测试窗口清理"""
        # 修改数据
        story_bible_window.story_bible_data["world"]["设定总览"] = "清理测试"
        
        # 执行清理
        story_bible_window.cleanup()
        
        # 检查数据是否保存
        story_bible_file = temp_project_dir / "story_bible.json"
        if story_bible_file.exists():
            with open(story_bible_file, 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
            assert saved_data["world"]["设定总览"] == "清理测试"
    
    def test_signals_definition(self, story_bible_window):
        """测试信号定义"""
        # 检查信号是否正确定义
        assert hasattr(story_bible_window, 'character_selected')
        assert hasattr(story_bible_window, 'world_setting_changed')
        assert hasattr(story_bible_window, 'timeline_updated')
        assert hasattr(story_bible_window, 'relationship_changed')
        assert hasattr(story_bible_window, 'ai_generation_requested')
    
    @patch('src.bamboofall.ui.windows.story_bible_window.StoryBibleWindow.show_message')
    def test_placeholder_functions(self, mock_show_message, story_bible_window):
        """测试占位符功能"""
        # 测试各种占位符功能
        placeholder_methods = [
            'add_character_relation',
            'add_timeline_event',
            'delete_timeline_event',
            'sort_timeline_by_date',
            'refresh_relationship_view',
            'export_relationship_diagram',
            'add_item',
            'delete_item'
        ]
        
        for method_name in placeholder_methods:
            if hasattr(story_bible_window, method_name):
                method = getattr(story_bible_window, method_name)
                method()
                # 检查是否显示了开发中的消息
                assert mock_show_message.called
                mock_show_message.reset_mock()


if __name__ == '__main__':
    pytest.main([__file__])