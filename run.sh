#!/bin/bash

# 笔落App 启动脚本 (Linux/macOS)

set -e

echo "🌸 笔落App - AI辅助小说创作平台"
echo "================================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.9或更高版本"
    exit 1
fi

# 显示Python版本
echo "Python版本: $(python3 --version)"

# 检查是否在虚拟环境中
if [[ -n "$VIRTUAL_ENV" ]]; then
    echo "检测到虚拟环境: $VIRTUAL_ENV"
else
    echo "提示: 建议使用虚拟环境运行应用"
    echo "创建虚拟环境: python3 -m venv bamboofall_env"
    echo "激活虚拟环境: source bamboofall_env/bin/activate"
    echo
fi

# 切换到脚本目录
cd "$(dirname "$0")"

# 运行应用
echo "正在启动应用..."
python3 run.py

echo "应用已退出"