# -*- coding: utf-8 -*-
"""
配置工具测试
"""

import pytest
import os
import tempfile
import json
import configparser
from unittest.mock import patch, mock_open
from bamboofall.utils.config_utils import ConfigUtils


class TestConfigUtils:
    """ConfigUtils测试类"""
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        import shutil
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def sample_config_dict(self):
        """示例配置字典"""
        return {
            "app": {
                "name": "BambooFall",
                "version": "1.0.0",
                "debug": True
            },
            "database": {
                "host": "localhost",
                "port": 5432,
                "name": "bamboofall_db"
            },
            "ai": {
                "openai_api_key": "sk-test-key",
                "anthropic_api_key": "claude-test-key",
                "default_model": "gpt-3.5-turbo"
            }
        }
    
    @pytest.fixture
    def json_config_file(self, temp_dir, sample_config_dict):
        """创建JSON配置文件"""
        config_file = os.path.join(temp_dir, "config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(sample_config_dict, f, indent=2, ensure_ascii=False)
        return config_file
    
    @pytest.fixture
    def ini_config_file(self, temp_dir):
        """创建INI配置文件"""
        config_file = os.path.join(temp_dir, "config.ini")
        config = configparser.ConfigParser()
        config['app'] = {
            'name': 'BambooFall',
            'version': '1.0.0',
            'debug': 'True'
        }
        config['database'] = {
            'host': 'localhost',
            'port': '5432',
            'name': 'bamboofall_db'
        }
        
        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)
        return config_file
    
    def test_load_json_config(self, json_config_file, sample_config_dict):
        """测试加载JSON配置"""
        config = ConfigUtils.load_config(json_config_file)
        
        assert config == sample_config_dict
        assert config['app']['name'] == 'BambooFall'
        assert config['database']['port'] == 5432
        assert config['ai']['default_model'] == 'gpt-3.5-turbo'
    
    def test_load_ini_config(self, ini_config_file):
        """测试加载INI配置"""
        config = ConfigUtils.load_config(ini_config_file)
        
        assert isinstance(config, dict)
        assert 'app' in config
        assert 'database' in config
        assert config['app']['name'] == 'BambooFall'
        assert config['database']['host'] == 'localhost'
    
    def test_load_config_file_not_found(self):
        """测试加载不存在的配置文件"""
        with pytest.raises(FileNotFoundError):
            ConfigUtils.load_config('/path/to/nonexistent/config.json')
    
    def test_load_config_invalid_json(self, temp_dir):
        """测试加载无效JSON配置"""
        invalid_json_file = os.path.join(temp_dir, 'invalid.json')
        with open(invalid_json_file, 'w') as f:
            f.write('{"invalid": json}')
        
        with pytest.raises(json.JSONDecodeError):
            ConfigUtils.load_config(invalid_json_file)
    
    def test_save_json_config(self, temp_dir, sample_config_dict):
        """测试保存JSON配置"""
        config_file = os.path.join(temp_dir, 'new_config.json')
        
        ConfigUtils.save_config(sample_config_dict, config_file)
        
        # 验证文件被创建
        assert os.path.exists(config_file)
        
        # 验证内容正确
        with open(config_file, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        assert loaded_config == sample_config_dict
    
    def test_save_ini_config(self, temp_dir, sample_config_dict):
        """测试保存INI配置"""
        config_file = os.path.join(temp_dir, 'new_config.ini')
        
        ConfigUtils.save_config(sample_config_dict, config_file)
        
        # 验证文件被创建
        assert os.path.exists(config_file)
        
        # 验证可以重新加载
        loaded_config = ConfigUtils.load_config(config_file)
        assert 'app' in loaded_config
        assert loaded_config['app']['name'] == 'BambooFall'
    
    def test_get_config_value(self, sample_config_dict):
        """测试获取配置值"""
        # 测试简单键
        value = ConfigUtils.get_config_value(sample_config_dict, 'app.name')
        assert value == 'BambooFall'
        
        # 测试嵌套键
        value = ConfigUtils.get_config_value(sample_config_dict, 'database.port')
        assert value == 5432
        
        # 测试深层嵌套
        value = ConfigUtils.get_config_value(sample_config_dict, 'ai.openai_api_key')
        assert value == 'sk-test-key'
    
    def test_get_config_value_with_default(self, sample_config_dict):
        """测试获取配置值（带默认值）"""
        # 测试存在的键
        value = ConfigUtils.get_config_value(
            sample_config_dict, 'app.name', default='DefaultApp'
        )
        assert value == 'BambooFall'
        
        # 测试不存在的键
        value = ConfigUtils.get_config_value(
            sample_config_dict, 'app.nonexistent', default='DefaultValue'
        )
        assert value == 'DefaultValue'
        
        # 测试不存在的嵌套键
        value = ConfigUtils.get_config_value(
            sample_config_dict, 'nonexistent.key', default='DefaultValue'
        )
        assert value == 'DefaultValue'
    
    def test_get_config_value_key_not_found(self, sample_config_dict):
        """测试获取不存在的配置值"""
        with pytest.raises(KeyError):
            ConfigUtils.get_config_value(sample_config_dict, 'nonexistent.key')
    
    def test_set_config_value(self, sample_config_dict):
        """测试设置配置值"""
        # 测试设置现有键
        ConfigUtils.set_config_value(sample_config_dict, 'app.name', 'NewAppName')
        assert sample_config_dict['app']['name'] == 'NewAppName'
        
        # 测试设置新键
        ConfigUtils.set_config_value(sample_config_dict, 'app.new_key', 'NewValue')
        assert sample_config_dict['app']['new_key'] == 'NewValue'
        
        # 测试设置深层嵌套键
        ConfigUtils.set_config_value(sample_config_dict, 'new.nested.key', 'NestedValue')
        assert sample_config_dict['new']['nested']['key'] == 'NestedValue'
    
    def test_delete_config_value(self, sample_config_dict):
        """测试删除配置值"""
        # 删除存在的键
        ConfigUtils.delete_config_value(sample_config_dict, 'app.debug')
        assert 'debug' not in sample_config_dict['app']
        
        # 删除不存在的键应该不报错
        ConfigUtils.delete_config_value(sample_config_dict, 'app.nonexistent')
    
    def test_merge_configs(self):
        """测试合并配置"""
        config1 = {
            'app': {
                'name': 'App1',
                'version': '1.0.0'
            },
            'database': {
                'host': 'localhost'
            }
        }
        
        config2 = {
            'app': {
                'name': 'App2',  # 这个会覆盖config1中的值
                'debug': True    # 这个是新增的
            },
            'ai': {
                'model': 'gpt-4'
            }
        }
        
        merged = ConfigUtils.merge_configs(config1, config2)
        
        # 验证合并结果
        assert merged['app']['name'] == 'App2'  # 被覆盖
        assert merged['app']['version'] == '1.0.0'  # 保留原值
        assert merged['app']['debug'] is True  # 新增值
        assert merged['database']['host'] == 'localhost'  # 保留原值
        assert merged['ai']['model'] == 'gpt-4'  # 新增部分
    
    def test_validate_config_schema(self):
        """测试配置模式验证"""
        schema = {
            'app': {
                'name': str,
                'version': str,
                'debug': bool
            },
            'database': {
                'host': str,
                'port': int
            }
        }
        
        valid_config = {
            'app': {
                'name': 'BambooFall',
                'version': '1.0.0',
                'debug': True
            },
            'database': {
                'host': 'localhost',
                'port': 5432
            }
        }
        
        invalid_config = {
            'app': {
                'name': 'BambooFall',
                'version': '1.0.0',
                'debug': 'not_a_boolean'  # 类型错误
            },
            'database': {
                'host': 'localhost',
                'port': 'not_an_integer'  # 类型错误
            }
        }
        
        # 测试有效配置
        assert ConfigUtils.validate_config(valid_config, schema) is True
        
        # 测试无效配置
        assert ConfigUtils.validate_config(invalid_config, schema) is False
    
    def test_get_config_section(self, sample_config_dict):
        """测试获取配置部分"""
        app_section = ConfigUtils.get_config_section(sample_config_dict, 'app')
        
        assert app_section == sample_config_dict['app']
        assert app_section['name'] == 'BambooFall'
        assert app_section['version'] == '1.0.0'
        
        # 测试不存在的部分
        with pytest.raises(KeyError):
            ConfigUtils.get_config_section(sample_config_dict, 'nonexistent')
    
    def test_has_config_key(self, sample_config_dict):
        """测试检查配置键是否存在"""
        # 测试存在的键
        assert ConfigUtils.has_config_key(sample_config_dict, 'app.name') is True
        assert ConfigUtils.has_config_key(sample_config_dict, 'database.port') is True
        
        # 测试不存在的键
        assert ConfigUtils.has_config_key(sample_config_dict, 'app.nonexistent') is False
        assert ConfigUtils.has_config_key(sample_config_dict, 'nonexistent.key') is False
    
    def test_list_config_keys(self, sample_config_dict):
        """测试列出配置键"""
        keys = ConfigUtils.list_config_keys(sample_config_dict)
        
        expected_keys = [
            'app.name',
            'app.version', 
            'app.debug',
            'database.host',
            'database.port',
            'database.name',
            'ai.openai_api_key',
            'ai.anthropic_api_key',
            'ai.default_model'
        ]
        
        for key in expected_keys:
            assert key in keys
    
    def test_filter_config_by_prefix(self, sample_config_dict):
        """测试按前缀过滤配置"""
        app_config = ConfigUtils.filter_config_by_prefix(sample_config_dict, 'app')
        
        assert 'name' in app_config
        assert 'version' in app_config
        assert 'debug' in app_config
        assert app_config['name'] == 'BambooFall'
        
        # 测试不存在的前缀
        empty_config = ConfigUtils.filter_config_by_prefix(sample_config_dict, 'nonexistent')
        assert empty_config == {}
    
    def test_convert_config_types(self):
        """测试配置类型转换"""
        string_config = {
            'app': {
                'debug': 'true',
                'port': '8080',
                'timeout': '30.5',
                'name': 'BambooFall'
            }
        }
        
        type_map = {
            'app.debug': bool,
            'app.port': int,
            'app.timeout': float,
            'app.name': str
        }
        
        converted = ConfigUtils.convert_config_types(string_config, type_map)
        
        assert converted['app']['debug'] is True
        assert converted['app']['port'] == 8080
        assert converted['app']['timeout'] == 30.5
        assert converted['app']['name'] == 'BambooFall'
    
    def test_backup_config(self, json_config_file, temp_dir):
        """测试备份配置"""
        backup_path = ConfigUtils.backup_config(json_config_file)
        
        # 验证备份文件存在
        assert os.path.exists(backup_path)
        assert backup_path != json_config_file
        
        # 验证备份内容正确
        original_config = ConfigUtils.load_config(json_config_file)
        backup_config = ConfigUtils.load_config(backup_path)
        
        assert original_config == backup_config
    
    def test_restore_config(self, json_config_file, sample_config_dict, temp_dir):
        """测试恢复配置"""
        # 创建备份
        backup_path = ConfigUtils.backup_config(json_config_file)
        
        # 修改原配置
        modified_config = sample_config_dict.copy()
        modified_config['app']['name'] = 'ModifiedApp'
        ConfigUtils.save_config(modified_config, json_config_file)
        
        # 恢复配置
        ConfigUtils.restore_config(backup_path, json_config_file)
        
        # 验证配置被恢复
        restored_config = ConfigUtils.load_config(json_config_file)
        assert restored_config['app']['name'] == 'BambooFall'
    
    def test_get_default_config_path(self):
        """测试获取默认配置路径"""
        default_path = ConfigUtils.get_default_config_path()
        
        assert isinstance(default_path, str)
        assert len(default_path) > 0
        # 应该包含用户目录或应用目录
        assert 'bamboofall' in default_path.lower() or 'config' in default_path.lower()
    
    def test_create_default_config(self, temp_dir):
        """测试创建默认配置"""
        config_file = os.path.join(temp_dir, 'default_config.json')
        
        ConfigUtils.create_default_config(config_file)
        
        # 验证文件被创建
        assert os.path.exists(config_file)
        
        # 验证配置结构
        config = ConfigUtils.load_config(config_file)
        assert isinstance(config, dict)
        # 应该包含基本的配置部分
        expected_sections = ['app', 'database', 'ai']
        for section in expected_sections:
            if section in config:
                assert isinstance(config[section], dict)
    
    def test_config_encryption(self, temp_dir, sample_config_dict):
        """测试配置加密（如果支持）"""
        if hasattr(ConfigUtils, 'encrypt_config'):
            encrypted_file = os.path.join(temp_dir, 'encrypted_config.enc')
            password = 'test_password'
            
            # 加密配置
            ConfigUtils.encrypt_config(sample_config_dict, encrypted_file, password)
            
            # 验证加密文件存在
            assert os.path.exists(encrypted_file)
            
            # 解密配置
            decrypted_config = ConfigUtils.decrypt_config(encrypted_file, password)
            
            # 验证解密后的配置正确
            assert decrypted_config == sample_config_dict
    
    def test_config_validation_with_custom_rules(self):
        """测试自定义规则配置验证"""
        config = {
            'app': {
                'name': 'BambooFall',
                'port': 8080
            }
        }
        
        # 自定义验证规则
        def validate_port(value):
            return isinstance(value, int) and 1024 <= value <= 65535
        
        def validate_name(value):
            return isinstance(value, str) and len(value) > 0
        
        validation_rules = {
            'app.port': validate_port,
            'app.name': validate_name
        }
        
        # 测试有效配置
        assert ConfigUtils.validate_config_with_rules(config, validation_rules) is True
        
        # 测试无效配置
        invalid_config = {
            'app': {
                'name': '',  # 空名称
                'port': 80   # 端口号太小
            }
        }
        
        assert ConfigUtils.validate_config_with_rules(invalid_config, validation_rules) is False
    
    def test_config_environment_variables(self):
        """测试环境变量配置"""
        with patch.dict(os.environ, {
            'BAMBOOFALL_APP_NAME': 'EnvApp',
            'BAMBOOFALL_DATABASE_PORT': '5433',
            'BAMBOOFALL_AI_DEBUG': 'true'
        }):
            env_config = ConfigUtils.load_config_from_env('BAMBOOFALL')
            
            assert env_config['app']['name'] == 'EnvApp'
            assert env_config['database']['port'] == '5433'
            assert env_config['ai']['debug'] == 'true'
    
    def test_config_template_substitution(self):
        """测试配置模板替换"""
        template_config = {
            'app': {
                'name': '${APP_NAME}',
                'version': '${APP_VERSION}'
            },
            'database': {
                'url': 'postgresql://${DB_USER}:${DB_PASS}@${DB_HOST}:${DB_PORT}/${DB_NAME}'
            }
        }
        
        variables = {
            'APP_NAME': 'BambooFall',
            'APP_VERSION': '1.0.0',
            'DB_USER': 'admin',
            'DB_PASS': 'password',
            'DB_HOST': 'localhost',
            'DB_PORT': '5432',
            'DB_NAME': 'bamboofall_db'
        }
        
        resolved_config = ConfigUtils.substitute_template_variables(template_config, variables)
        
        assert resolved_config['app']['name'] == 'BambooFall'
        assert resolved_config['app']['version'] == '1.0.0'
        assert 'postgresql://admin:password@localhost:5432/bamboofall_db' in resolved_config['database']['url']
    
    def test_config_hot_reload(self, json_config_file, sample_config_dict):
        """测试配置热重载"""
        if hasattr(ConfigUtils, 'watch_config_changes'):
            # 创建配置监视器
            watcher = ConfigUtils.watch_config_changes(json_config_file)
            
            # 修改配置文件
            modified_config = sample_config_dict.copy()
            modified_config['app']['name'] = 'HotReloadedApp'
            ConfigUtils.save_config(modified_config, json_config_file)
            
            # 检查是否检测到变化
            import time
            time.sleep(0.1)  # 等待文件系统事件
            
            if hasattr(watcher, 'has_changes'):
                assert watcher.has_changes() is True
    
    def test_config_diff(self, sample_config_dict):
        """测试配置差异比较"""
        config1 = sample_config_dict.copy()
        config2 = sample_config_dict.copy()
        config2['app']['name'] = 'ModifiedApp'
        config2['app']['new_key'] = 'new_value'
        del config2['database']['port']
        
        diff = ConfigUtils.compare_configs(config1, config2)
        
        # 验证差异检测
        assert 'modified' in diff
        assert 'added' in diff
        assert 'removed' in diff
        
        assert 'app.name' in diff['modified']
        assert 'app.new_key' in diff['added']
        assert 'database.port' in diff['removed']
    
    def test_config_performance(self, temp_dir):
        """测试配置性能"""
        import time
        
        # 创建大型配置
        large_config = {}
        for i in range(1000):
            large_config[f'section_{i}'] = {
                f'key_{j}': f'value_{i}_{j}' for j in range(100)
            }
        
        config_file = os.path.join(temp_dir, 'large_config.json')
        
        # 测试保存性能
        start_time = time.time()
        ConfigUtils.save_config(large_config, config_file)
        save_time = time.time() - start_time
        
        # 测试加载性能
        start_time = time.time()
        loaded_config = ConfigUtils.load_config(config_file)
        load_time = time.time() - start_time
        
        # 验证性能在合理范围内（根据实际情况调整）
        assert save_time < 5.0  # 保存应该在5秒内完成
        assert load_time < 5.0  # 加载应该在5秒内完成
        assert loaded_config == large_config
    
    def test_config_thread_safety(self, json_config_file, sample_config_dict):
        """测试配置线程安全"""
        import threading
        import time
        
        results = []
        errors = []
        
        def config_worker(worker_id):
            try:
                for i in range(10):
                    # 读取配置
                    config = ConfigUtils.load_config(json_config_file)
                    
                    # 修改配置
                    config['app']['worker_id'] = worker_id
                    config['app']['iteration'] = i
                    
                    # 保存配置
                    ConfigUtils.save_config(config, json_config_file)
                    
                    time.sleep(0.01)  # 短暂延迟
                
                results.append(f'worker_{worker_id}_completed')
            except Exception as e:
                errors.append(f'worker_{worker_id}_error: {e}')
        
        # 创建多个线程
        threads = []
        for i in range(5):
            t = threading.Thread(target=config_worker, args=(i,))
            threads.append(t)
        
        # 启动所有线程
        for t in threads:
            t.start()
        
        # 等待所有线程完成
        for t in threads:
            t.join()
        
        # 验证没有严重错误
        assert len(errors) == 0 or len(errors) < len(threads)  # 允许少量并发错误
        assert len(results) > 0  # 至少有一些成功的操作