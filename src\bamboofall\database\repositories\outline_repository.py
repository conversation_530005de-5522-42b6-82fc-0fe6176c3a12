# -*- coding: utf-8 -*-
"""
大纲数据仓储

处理大纲相关的数据库操作
"""

import logging
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from .base_repository import BaseRepository
from ..models import OutlineORM, OutlineNodeORM
from ...models.outline import Outline, OutlineNode, OutlineNodeType, OutlineNodeStatus
from ...exceptions.exceptions import DatabaseError, ValidationError
from ...core.performance_monitor import performance_monitor, database_monitor
from ...core.cache_manager import get_cache_manager

logger = logging.getLogger(__name__)


class OutlineRepository(BaseRepository):
    """大纲仓储类"""
    
    def __init__(self):
        super().__init__()
        self.cache_manager = get_cache_manager()
        self.cache_ttl = 600  # 10分钟缓存
        self.enable_cache = True
    
    @performance_monitor(category="database")
    @database_monitor
    def create_outline(self, outline: Outline) -> Outline:
        """创建大纲"""
        try:
            with self.get_session() as session:
                outline_orm = OutlineORM(
                    id=outline.id,
                    project_id=outline.project_id,
                    name=outline.name,
                    description=outline.description,
                    root_node_id=outline.root_node_id,
                    version=outline.version,
                    notes=outline.notes,
                    created_at=outline.created_at,
                    updated_at=outline.updated_at
                )
                
                session.add(outline_orm)
                session.commit()
                session.refresh(outline_orm)
                
                # 清理相关缓存
                self.cache_manager.invalidate_pattern(f"outlines_{outline.project_id}*")
                
                return self._orm_to_model(outline_orm)
                
        except Exception as e:
            logger.error(f"创建大纲失败: {e}")
            raise DatabaseError(f"创建大纲失败: {e}")
    
    @performance_monitor(category="database")
    @database_monitor
    def get_outline(self, outline_id: str) -> Optional[Outline]:
        """获取大纲"""
        # 检查缓存
        cache_key = self._get_cache_key("outline", outline_id)
        cached_outline = self._cache_get(cache_key)
        if cached_outline:
            return cached_outline
            
        try:
            with self.get_session() as session:
                outline_orm = session.query(OutlineORM).filter(
                    OutlineORM.id == outline_id
                ).first()
                
                if outline_orm:
                    outline = self._orm_to_model(outline_orm)
                    # 缓存结果
                    self._cache_set(cache_key, outline)
                    return outline
                return None
                
        except Exception as e:
            logger.error(f"获取大纲失败: {e}")
            raise DatabaseError(f"获取大纲失败: {e}")
    
    @performance_monitor(category="database")
    @database_monitor
    def update_outline(self, outline: Outline) -> Outline:
        """更新大纲"""
        try:
            with self.get_session() as session:
                outline_orm = session.query(OutlineORM).filter(
                    OutlineORM.id == outline.id
                ).first()
                
                if not outline_orm:
                    raise ValidationError(f"大纲不存在: {outline.id}")
                
                # 更新字段
                outline_orm.name = outline.name
                outline_orm.description = outline.description
                outline_orm.root_node_id = outline.root_node_id
                outline_orm.version = outline.version
                outline_orm.notes = outline.notes
                outline_orm.updated_at = outline.updated_at
                
                session.commit()
                session.refresh(outline_orm)
                
                # 清理相关缓存
                cache_key = self._get_cache_key("outline", outline.id)
                self.cache_manager.delete(cache_key)
                self.cache_manager.invalidate_pattern(f"outlines_{outline.project_id}*")
                self.cache_manager.invalidate_pattern(f"outline_nodes_{outline.project_id}*")
                
                return self._orm_to_model(outline_orm)
                
        except Exception as e:
            logger.error(f"更新大纲失败: {e}")
            raise DatabaseError(f"更新大纲失败: {e}")
    
    @performance_monitor(category="database")
    @database_monitor
    def delete_outline(self, outline_id: str) -> bool:
        """删除大纲"""
        try:
            with self.get_session() as session:
                # 获取大纲信息用于清理缓存
                outline_orm = session.query(OutlineORM).filter(
                    OutlineORM.id == outline_id
                ).first()
                
                if not outline_orm:
                    return False
                    
                project_id = outline_orm.project_id
                
                # 先删除所有相关节点
                session.query(OutlineNodeORM).filter(
                    OutlineNodeORM.outline_id == outline_id
                ).delete()
                
                # 删除大纲
                result = session.query(OutlineORM).filter(
                    OutlineORM.id == outline_id
                ).delete()
                
                session.commit()
                
                # 清理相关缓存
                cache_key = self._get_cache_key("outline", outline_id)
                self.cache_manager.delete(cache_key)
                self.cache_manager.invalidate_pattern(f"outlines_{project_id}*")
                self.cache_manager.invalidate_pattern(f"outline_nodes_{project_id}*")
                
                return result > 0
                
        except Exception as e:
            logger.error(f"删除大纲失败: {e}")
            raise DatabaseError(f"删除大纲失败: {e}")
    
    @performance_monitor(category="database")
    @database_monitor
    def get_project_outlines(self, project_id: str) -> List[Outline]:
        """获取项目的所有大纲"""
        # 检查缓存
        cache_key = self._get_cache_key("outlines", project_id)
        cached_outlines = self._cache_get(cache_key)
        if cached_outlines:
            return cached_outlines
            
        try:
            with self.get_session() as session:
                outline_orms = session.query(OutlineORM).filter(
                    OutlineORM.project_id == project_id
                ).order_by(OutlineORM.created_at).all()
                
                outlines = [self._orm_to_model(orm) for orm in outline_orms]
                
                # 缓存结果
                self._cache_set(cache_key, outlines)
                
                return outlines
                
        except Exception as e:
            logger.error(f"获取项目大纲失败: {e}")
            raise DatabaseError(f"获取项目大纲失败: {e}")
    
    @performance_monitor(category="database")
    @database_monitor
    def create_outline_node(self, node: OutlineNode) -> OutlineNode:
        """创建大纲节点"""
        try:
            with self.get_session() as session:
                node_orm = OutlineNodeORM(
                    id=node.id,
                    project_id=node.project_id,
                    title=node.title,
                    content=node.content,
                    type=node.type.value,
                    status=node.status.value,
                    parent_id=node.parent_id,
                    children_ids=','.join(node.children_ids) if node.children_ids else '',
                    order_index=node.order_index,
                    level=node.level,
                    chapter_id=node.chapter_id,
                    scene_ids=','.join(node.scene_ids) if node.scene_ids else '',
                    character_ids=','.join(node.character_ids) if node.character_ids else '',
                    estimated_word_count=node.estimated_word_count,
                    actual_word_count=node.actual_word_count,
                    tags=','.join(node.tags) if node.tags else '',
                    metadata=str(node.metadata) if node.metadata else '',
                    notes=node.notes,
                    created_at=node.created_at,
                    updated_at=node.updated_at
                )
                
                session.add(node_orm)
                session.commit()
                session.refresh(node_orm)
                
                # 清理相关缓存
                self.cache_manager.invalidate_pattern(f"outline_nodes_{node.project_id}*")
                
                return self._node_orm_to_model(node_orm)
                
        except Exception as e:
            logger.error(f"创建大纲节点失败: {e}")
            raise DatabaseError(f"创建大纲节点失败: {e}")
    
    @performance_monitor(category="database")
    @database_monitor
    def get_outline_node(self, node_id: str) -> Optional[OutlineNode]:
        """获取大纲节点"""
        # 检查缓存
        cache_key = self._get_cache_key("outline_node", node_id)
        cached_node = self._cache_get(cache_key)
        if cached_node:
            return cached_node
            
        try:
            with self.get_session() as session:
                node_orm = session.query(OutlineNodeORM).filter(
                    OutlineNodeORM.id == node_id
                ).first()
                
                if node_orm:
                    node = self._node_orm_to_model(node_orm)
                    # 缓存结果
                    self._cache_set(cache_key, node)
                    return node
                return None
                
        except Exception as e:
            logger.error(f"获取大纲节点失败: {e}")
            raise DatabaseError(f"获取大纲节点失败: {e}")
    
    @performance_monitor(category="database")
    @database_monitor
    def update_outline_node(self, node: OutlineNode) -> OutlineNode:
        """更新大纲节点"""
        try:
            with self.get_session() as session:
                node_orm = session.query(OutlineNodeORM).filter(
                    OutlineNodeORM.id == node.id
                ).first()
                
                if not node_orm:
                    raise ValidationError(f"大纲节点不存在: {node.id}")
                
                # 更新字段
                node_orm.title = node.title
                node_orm.content = node.content
                node_orm.type = node.type.value
                node_orm.status = node.status.value
                node_orm.parent_id = node.parent_id
                node_orm.children_ids = ','.join(node.children_ids) if node.children_ids else ''
                node_orm.order_index = node.order_index
                node_orm.level = node.level
                node_orm.chapter_id = node.chapter_id
                node_orm.scene_ids = ','.join(node.scene_ids) if node.scene_ids else ''
                node_orm.character_ids = ','.join(node.character_ids) if node.character_ids else ''
                node_orm.estimated_word_count = node.estimated_word_count
                node_orm.actual_word_count = node.actual_word_count
                node_orm.tags = ','.join(node.tags) if node.tags else ''
                node_orm.metadata = str(node.metadata) if node.metadata else ''
                node_orm.notes = node.notes
                node_orm.updated_at = node.updated_at
                
                session.commit()
                session.refresh(node_orm)
                
                # 清理相关缓存
                cache_key = self._get_cache_key("outline_node", node.id)
                self.cache_manager.delete(cache_key)
                self.cache_manager.invalidate_pattern(f"outline_nodes_{node.project_id}*")
                
                return self._node_orm_to_model(node_orm)
                
        except Exception as e:
            logger.error(f"更新大纲节点失败: {e}")
            raise DatabaseError(f"更新大纲节点失败: {e}")
    
    @performance_monitor(category="database")
    @database_monitor
    def delete_outline_node(self, node_id: str) -> bool:
        """删除大纲节点"""
        try:
            with self.get_session() as session:
                # 获取节点信息用于清理缓存
                node_orm = session.query(OutlineNodeORM).filter(
                    OutlineNodeORM.id == node_id
                ).first()
                
                if not node_orm:
                    return False
                    
                project_id = node_orm.project_id
                
                result = session.query(OutlineNodeORM).filter(
                    OutlineNodeORM.id == node_id
                ).delete()
                
                session.commit()
                
                # 清理相关缓存
                cache_key = self._get_cache_key("outline_node", node_id)
                self.cache_manager.delete(cache_key)
                self.cache_manager.invalidate_pattern(f"outline_nodes_{project_id}*")
                
                return result > 0
                
        except Exception as e:
            logger.error(f"删除大纲节点失败: {e}")
            raise DatabaseError(f"删除大纲节点失败: {e}")
    
    @performance_monitor(category="database")
    @database_monitor
    def get_outline_nodes(self, outline_id: str) -> List[OutlineNode]:
        """获取大纲的所有节点"""
        # 检查缓存
        cache_key = self._get_cache_key("outline_nodes", outline_id)
        cached_nodes = self._cache_get(cache_key)
        if cached_nodes:
            return cached_nodes
            
        try:
            with self.get_session() as session:
                # 通过大纲获取项目ID，然后获取所有节点
                outline_orm = session.query(OutlineORM).filter(
                    OutlineORM.id == outline_id
                ).first()
                
                if not outline_orm:
                    return []
                
                node_orms = session.query(OutlineNodeORM).filter(
                    OutlineNodeORM.project_id == outline_orm.project_id
                ).order_by(OutlineNodeORM.level, OutlineNodeORM.order_index).all()
                
                nodes = [self._node_orm_to_model(orm) for orm in node_orms]
                
                # 缓存结果
                self._cache_set(cache_key, nodes)
                
                return nodes
                
        except Exception as e:
            logger.error(f"获取大纲节点失败: {e}")
            raise DatabaseError(f"获取大纲节点失败: {e}")
    
    @performance_monitor(category="database")
    @database_monitor
    def get_project_outline_nodes(self, project_id: str) -> List[OutlineNode]:
        """获取项目的所有大纲节点"""
        # 检查缓存
        cache_key = self._get_cache_key("outline_nodes", project_id)
        cached_nodes = self._cache_get(cache_key)
        if cached_nodes:
            return cached_nodes
            
        try:
            with self.get_session() as session:
                node_orms = session.query(OutlineNodeORM).filter(
                    OutlineNodeORM.project_id == project_id
                ).order_by(OutlineNodeORM.level, OutlineNodeORM.order_index).all()
                
                nodes = [self._node_orm_to_model(orm) for orm in node_orms]
                
                # 缓存结果
                self._cache_set(cache_key, nodes)
                
                return nodes
                
        except Exception as e:
            logger.error(f"获取项目大纲节点失败: {e}")
            raise DatabaseError(f"获取项目大纲节点失败: {e}")
    
    def search_outline_nodes(self, outline_id: str, keyword: str) -> List[OutlineNode]:
        """搜索大纲节点"""
        try:
            with self.get_session() as session:
                # 获取大纲的项目ID
                outline_orm = session.query(OutlineORM).filter(
                    OutlineORM.id == outline_id
                ).first()
                
                if not outline_orm:
                    return []
                
                # 搜索节点
                node_orms = session.query(OutlineNodeORM).filter(
                    and_(
                        OutlineNodeORM.project_id == outline_orm.project_id,
                        or_(
                            OutlineNodeORM.title.contains(keyword),
                            OutlineNodeORM.content.contains(keyword),
                            OutlineNodeORM.notes.contains(keyword)
                        )
                    )
                ).order_by(OutlineNodeORM.level, OutlineNodeORM.order_index).all()
                
                return [self._node_orm_to_model(orm) for orm in node_orms]
                
        except Exception as e:
            logger.error(f"搜索大纲节点失败: {e}")
            raise DatabaseError(f"搜索大纲节点失败: {e}")
    
    def get_nodes_by_type(self, project_id: str, node_type: OutlineNodeType) -> List[OutlineNode]:
        """按类型获取节点"""
        try:
            with self.get_session() as session:
                node_orms = session.query(OutlineNodeORM).filter(
                    and_(
                        OutlineNodeORM.project_id == project_id,
                        OutlineNodeORM.type == node_type.value
                    )
                ).order_by(OutlineNodeORM.order_index).all()
                
                return [self._node_orm_to_model(orm) for orm in node_orms]
                
        except Exception as e:
            logger.error(f"按类型获取节点失败: {e}")
            raise DatabaseError(f"按类型获取节点失败: {e}")
    
    def get_nodes_by_status(self, project_id: str, status: OutlineNodeStatus) -> List[OutlineNode]:
        """按状态获取节点"""
        try:
            with self.get_session() as session:
                node_orms = session.query(OutlineNodeORM).filter(
                    and_(
                        OutlineNodeORM.project_id == project_id,
                        OutlineNodeORM.status == status.value
                    )
                ).order_by(OutlineNodeORM.order_index).all()
                
                return [self._node_orm_to_model(orm) for orm in node_orms]
                
        except Exception as e:
            logger.error(f"按状态获取节点失败: {e}")
            raise DatabaseError(f"按状态获取节点失败: {e}")
    
    def _orm_to_model(self, outline_orm: OutlineORM) -> Outline:
        """将ORM对象转换为模型对象"""
        return Outline(
            id=outline_orm.id,
            project_id=outline_orm.project_id,
            name=outline_orm.name,
            description=outline_orm.description,
            root_node_id=outline_orm.root_node_id,
            version=outline_orm.version,
            notes=outline_orm.notes,
            created_at=outline_orm.created_at,
            updated_at=outline_orm.updated_at
        )
    
    def _node_orm_to_model(self, node_orm: OutlineNodeORM) -> OutlineNode:
        """将节点ORM对象转换为模型对象"""
        # 处理列表字段
        children_ids = [id.strip() for id in node_orm.children_ids.split(',') if id.strip()] if node_orm.children_ids else []
        scene_ids = [id.strip() for id in node_orm.scene_ids.split(',') if id.strip()] if node_orm.scene_ids else []
        character_ids = [id.strip() for id in node_orm.character_ids.split(',') if id.strip()] if node_orm.character_ids else []
        tags = [tag.strip() for tag in node_orm.tags.split(',') if tag.strip()] if node_orm.tags else []
        
        # 处理元数据
        metadata = {}
        if node_orm.metadata:
            try:
                import json
                metadata = json.loads(node_orm.metadata)
            except:
                metadata = {}
        
        return OutlineNode(
            id=node_orm.id,
            project_id=node_orm.project_id,
            title=node_orm.title,
            content=node_orm.content,
            type=OutlineNodeType(node_orm.type),
            status=OutlineNodeStatus(node_orm.status),
            parent_id=node_orm.parent_id,
            children_ids=children_ids,
            order_index=node_orm.order_index,
            level=node_orm.level,
            chapter_id=node_orm.chapter_id,
            scene_ids=scene_ids,
            character_ids=character_ids,
            estimated_word_count=node_orm.estimated_word_count,
            actual_word_count=node_orm.actual_word_count,
            tags=tags,
            metadata=metadata,
            notes=node_orm.notes,
            created_at=node_orm.created_at,
            updated_at=node_orm.updated_at
        )