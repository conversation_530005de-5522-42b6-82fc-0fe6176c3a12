# -*- coding: utf-8 -*-
"""
日志工具测试
"""

import pytest
import os
import tempfile
import logging
import json
from unittest.mock import patch, MagicMock
from bamboofall.utils.logger_utils import LoggerUtils


class TestLoggerUtils:
    """LoggerUtils测试类"""
    
    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        import shutil
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def log_file(self, temp_dir):
        """创建临时日志文件路径"""
        return os.path.join(temp_dir, "test.log")
    
    def test_create_logger_basic(self):
        """测试创建基本日志器"""
        logger = LoggerUtils.create_logger("test_logger")
        
        assert isinstance(logger, logging.Logger)
        assert logger.name == "test_logger"
        assert logger.level == logging.INFO  # 默认级别
    
    def test_create_logger_with_level(self):
        """测试创建指定级别的日志器"""
        logger = LoggerUtils.create_logger("debug_logger", level=logging.DEBUG)
        
        assert logger.level == logging.DEBUG
    
    def test_create_logger_with_file_handler(self, log_file):
        """测试创建带文件处理器的日志器"""
        logger = LoggerUtils.create_logger(
            "file_logger", 
            log_file=log_file,
            level=logging.INFO
        )
        
        # 测试日志记录
        logger.info("测试日志消息")
        
        # 验证日志文件被创建
        assert os.path.exists(log_file)
        
        # 验证日志内容
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert "测试日志消息" in content
    
    def test_create_logger_with_console_handler(self):
        """测试创建带控制台处理器的日志器"""
        logger = LoggerUtils.create_logger(
            "console_logger",
            console_output=True,
            level=logging.INFO
        )
        
        # 检查是否有控制台处理器
        console_handlers = [
            h for h in logger.handlers 
            if isinstance(h, logging.StreamHandler) and h.stream.name == '<stderr>'
        ]
        assert len(console_handlers) > 0
    
    def test_create_logger_with_custom_format(self, log_file):
        """测试创建自定义格式的日志器"""
        custom_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        
        logger = LoggerUtils.create_logger(
            "custom_format_logger",
            log_file=log_file,
            log_format=custom_format
        )
        
        logger.info("自定义格式测试")
        
        # 验证日志格式
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert "custom_format_logger" in content
            assert "INFO" in content
            assert "自定义格式测试" in content
    
    def test_get_logger_existing(self):
        """测试获取已存在的日志器"""
        # 先创建一个日志器
        original_logger = LoggerUtils.create_logger("existing_logger")
        
        # 获取同名日志器
        retrieved_logger = LoggerUtils.get_logger("existing_logger")
        
        # 应该是同一个实例
        assert retrieved_logger is original_logger
    
    def test_get_logger_non_existing(self):
        """测试获取不存在的日志器"""
        logger = LoggerUtils.get_logger("non_existing_logger")
        
        # 应该创建新的日志器
        assert isinstance(logger, logging.Logger)
        assert logger.name == "non_existing_logger"
    
    def test_set_log_level(self):
        """测试设置日志级别"""
        logger = LoggerUtils.create_logger("level_test_logger")
        
        # 设置为DEBUG级别
        LoggerUtils.set_log_level("level_test_logger", logging.DEBUG)
        assert logger.level == logging.DEBUG
        
        # 设置为ERROR级别
        LoggerUtils.set_log_level("level_test_logger", logging.ERROR)
        assert logger.level == logging.ERROR
    
    def test_add_file_handler(self, log_file):
        """测试添加文件处理器"""
        logger = LoggerUtils.create_logger("file_handler_test")
        
        # 添加文件处理器
        LoggerUtils.add_file_handler("file_handler_test", log_file)
        
        # 测试日志记录
        logger.info("文件处理器测试")
        
        # 验证文件被创建和写入
        assert os.path.exists(log_file)
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert "文件处理器测试" in content
    
    def test_add_console_handler(self):
        """测试添加控制台处理器"""
        logger = LoggerUtils.create_logger("console_handler_test")
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 添加控制台处理器
        LoggerUtils.add_console_handler("console_handler_test")
        
        # 验证控制台处理器被添加
        console_handlers = [
            h for h in logger.handlers 
            if isinstance(h, logging.StreamHandler)
        ]
        assert len(console_handlers) > 0
    
    def test_remove_handler(self, log_file):
        """测试移除处理器"""
        logger = LoggerUtils.create_logger(
            "remove_handler_test",
            log_file=log_file,
            console_output=True
        )
        
        initial_handler_count = len(logger.handlers)
        assert initial_handler_count > 0
        
        # 移除文件处理器
        LoggerUtils.remove_handler("remove_handler_test", "file")
        
        # 验证处理器数量减少
        assert len(logger.handlers) < initial_handler_count
    
    def test_log_rotation(self, temp_dir):
        """测试日志轮转"""
        log_file = os.path.join(temp_dir, "rotation_test.log")
        
        logger = LoggerUtils.create_logger(
            "rotation_test",
            log_file=log_file,
            max_bytes=1024,  # 1KB
            backup_count=3
        )
        
        # 写入大量日志以触发轮转
        for i in range(100):
            logger.info(f"这是一条很长的日志消息用于测试日志轮转功能 - 消息编号: {i}")
        
        # 检查是否生成了轮转文件
        log_files = [f for f in os.listdir(temp_dir) if f.startswith("rotation_test.log")]
        assert len(log_files) > 1  # 应该有原文件和至少一个轮转文件
    
    def test_structured_logging(self, log_file):
        """测试结构化日志"""
        logger = LoggerUtils.create_logger(
            "structured_test",
            log_file=log_file,
            structured=True
        )
        
        # 记录结构化日志
        LoggerUtils.log_structured(
            "structured_test",
            level="info",
            message="用户登录",
            user_id=12345,
            ip_address="***********",
            timestamp="2024-01-01T10:00:00Z"
        )
        
        # 验证结构化日志格式
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            # 应该包含JSON格式的日志
            assert "user_id" in content
            assert "ip_address" in content
            assert "12345" in content
    
    def test_log_filtering(self, log_file):
        """测试日志过滤"""
        # 创建自定义过滤器
        def sensitive_filter(record):
            # 过滤包含敏感信息的日志
            return "password" not in record.getMessage().lower()
        
        logger = LoggerUtils.create_logger(
            "filter_test",
            log_file=log_file
        )
        
        # 添加过滤器
        LoggerUtils.add_filter("filter_test", sensitive_filter)
        
        # 记录不同类型的日志
        logger.info("正常日志消息")
        logger.info("用户输入了password: secret123")  # 应该被过滤
        logger.info("另一条正常消息")
        
        # 验证过滤效果
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert "正常日志消息" in content
            assert "另一条正常消息" in content
            assert "password" not in content  # 敏感信息被过滤
    
    def test_log_context_manager(self, log_file):
        """测试日志上下文管理器"""
        logger = LoggerUtils.create_logger("context_test", log_file=log_file)
        
        # 使用上下文管理器
        with LoggerUtils.log_context("context_test", user_id=123, session_id="abc"):
            logger.info("上下文中的日志消息")
        
        # 验证上下文信息被包含
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert "user_id=123" in content or "123" in content
            assert "session_id=abc" in content or "abc" in content
    
    def test_log_performance_monitoring(self, log_file):
        """测试日志性能监控"""
        logger = LoggerUtils.create_logger("perf_test", log_file=log_file)
        
        # 使用性能监控装饰器
        @LoggerUtils.log_performance("perf_test")
        def slow_function():
            import time
            time.sleep(0.1)
            return "完成"
        
        result = slow_function()
        
        assert result == "完成"
        
        # 验证性能日志
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert "slow_function" in content
            assert "执行时间" in content or "duration" in content
    
    def test_log_exception_handling(self, log_file):
        """测试异常日志处理"""
        logger = LoggerUtils.create_logger("exception_test", log_file=log_file)
        
        try:
            raise ValueError("测试异常")
        except Exception as e:
            LoggerUtils.log_exception("exception_test", e, "处理用户请求时发生错误")
        
        # 验证异常日志
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert "ValueError" in content
            assert "测试异常" in content
            assert "处理用户请求时发生错误" in content
    
    def test_log_sanitization(self, log_file):
        """测试日志清理"""
        logger = LoggerUtils.create_logger(
            "sanitize_test",
            log_file=log_file,
            sanitize_sensitive=True
        )
        
        # 记录包含敏感信息的日志
        sensitive_data = {
            "username": "testuser",
            "password": "secret123",
            "credit_card": "1234-5678-9012-3456",
            "email": "<EMAIL>"
        }
        
        LoggerUtils.log_sanitized("sanitize_test", "用户数据", sensitive_data)
        
        # 验证敏感信息被清理
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert "testuser" in content  # 用户名可以保留
            assert "secret123" not in content  # 密码应该被清理
            assert "1234-5678-9012-3456" not in content  # 信用卡号应该被清理
    
    def test_log_aggregation(self, temp_dir):
        """测试日志聚合"""
        # 创建多个日志文件
        log_files = []
        for i in range(3):
            log_file = os.path.join(temp_dir, f"app_{i}.log")
            log_files.append(log_file)
            
            logger = LoggerUtils.create_logger(f"app_{i}", log_file=log_file)
            logger.info(f"应用 {i} 的日志消息")
        
        # 聚合日志
        aggregated_file = os.path.join(temp_dir, "aggregated.log")
        LoggerUtils.aggregate_logs(log_files, aggregated_file)
        
        # 验证聚合结果
        assert os.path.exists(aggregated_file)
        with open(aggregated_file, 'r', encoding='utf-8') as f:
            content = f.read()
            for i in range(3):
                assert f"应用 {i} 的日志消息" in content
    
    def test_log_search_and_filter(self, log_file):
        """测试日志搜索和过滤"""
        logger = LoggerUtils.create_logger("search_test", log_file=log_file)
        
        # 记录不同类型的日志
        logger.info("用户登录成功")
        logger.warning("用户尝试访问受限资源")
        logger.error("数据库连接失败")
        logger.info("用户注销")
        
        # 搜索特定日志
        results = LoggerUtils.search_logs(log_file, pattern="用户")
        assert len(results) >= 2  # 应该找到包含"用户"的日志
        
        # 按级别过滤
        error_logs = LoggerUtils.filter_logs_by_level(log_file, "ERROR")
        assert len(error_logs) >= 1
        assert any("数据库连接失败" in log for log in error_logs)
    
    def test_log_statistics(self, log_file):
        """测试日志统计"""
        logger = LoggerUtils.create_logger("stats_test", log_file=log_file)
        
        # 记录不同级别的日志
        for _ in range(5):
            logger.info("信息日志")
        for _ in range(3):
            logger.warning("警告日志")
        for _ in range(2):
            logger.error("错误日志")
        
        # 获取统计信息
        stats = LoggerUtils.get_log_statistics(log_file)
        
        assert stats['total_lines'] >= 10
        assert stats['info_count'] >= 5
        assert stats['warning_count'] >= 3
        assert stats['error_count'] >= 2
    
    def test_log_archiving(self, temp_dir):
        """测试日志归档"""
        log_file = os.path.join(temp_dir, "archive_test.log")
        logger = LoggerUtils.create_logger("archive_test", log_file=log_file)
        
        # 记录一些日志
        for i in range(10):
            logger.info(f"归档测试日志 {i}")
        
        # 归档日志
        archive_path = LoggerUtils.archive_logs(
            log_file,
            archive_dir=temp_dir,
            compress=True
        )
        
        # 验证归档文件
        assert os.path.exists(archive_path)
        assert archive_path.endswith('.gz') or archive_path.endswith('.zip')
    
    def test_log_real_time_monitoring(self, log_file):
        """测试实时日志监控"""
        logger = LoggerUtils.create_logger("monitor_test", log_file=log_file)
        
        # 创建日志监控器
        alerts = []
        
        def alert_callback(log_entry):
            if "ERROR" in log_entry:
                alerts.append(log_entry)
        
        monitor = LoggerUtils.create_log_monitor(log_file, alert_callback)
        
        # 记录一些日志
        logger.info("正常日志")
        logger.error("这是一个错误")
        logger.info("另一条正常日志")
        
        # 等待监控器处理
        import time
        time.sleep(0.1)
        
        # 验证告警
        if hasattr(monitor, 'stop'):
            monitor.stop()
        
        assert len(alerts) >= 1
        assert any("这是一个错误" in alert for alert in alerts)
    
    def test_log_configuration_from_file(self, temp_dir):
        """测试从配置文件加载日志配置"""
        config_file = os.path.join(temp_dir, "logging_config.json")
        log_file = os.path.join(temp_dir, "config_test.log")
        
        # 创建日志配置
        config = {
            "version": 1,
            "formatters": {
                "default": {
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                }
            },
            "handlers": {
                "file": {
                    "class": "logging.FileHandler",
                    "filename": log_file,
                    "formatter": "default"
                }
            },
            "loggers": {
                "config_test": {
                    "level": "INFO",
                    "handlers": ["file"]
                }
            }
        }
        
        # 保存配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2)
        
        # 从配置文件加载
        LoggerUtils.configure_from_file(config_file)
        
        # 测试配置的日志器
        logger = logging.getLogger("config_test")
        logger.info("配置文件测试日志")
        
        # 验证日志文件
        assert os.path.exists(log_file)
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert "配置文件测试日志" in content
    
    def test_log_memory_usage(self):
        """测试日志内存使用"""
        import psutil
        import gc
        
        # 获取初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # 创建大量日志器
        loggers = []
        for i in range(100):
            logger = LoggerUtils.create_logger(f"memory_test_{i}")
            loggers.append(logger)
            
            # 记录一些日志
            for j in range(10):
                logger.info(f"内存测试日志 {i}-{j}")
        
        # 强制垃圾回收
        gc.collect()
        
        # 获取最终内存使用
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        # 验证内存使用在合理范围内（根据实际情况调整）
        assert memory_increase < 100 * 1024 * 1024  # 不应该超过100MB
    
    def test_log_thread_safety(self, log_file):
        """测试日志线程安全"""
        import threading
        import time
        
        logger = LoggerUtils.create_logger("thread_test", log_file=log_file)
        
        results = []
        errors = []
        
        def log_worker(worker_id):
            try:
                for i in range(50):
                    logger.info(f"线程 {worker_id} - 消息 {i}")
                    time.sleep(0.001)  # 短暂延迟
                results.append(f"worker_{worker_id}_completed")
            except Exception as e:
                errors.append(f"worker_{worker_id}_error: {e}")
        
        # 创建多个线程
        threads = []
        for i in range(10):
            t = threading.Thread(target=log_worker, args=(i,))
            threads.append(t)
        
        # 启动所有线程
        for t in threads:
            t.start()
        
        # 等待所有线程完成
        for t in threads:
            t.join()
        
        # 验证结果
        assert len(errors) == 0  # 不应该有错误
        assert len(results) == 10  # 所有线程都应该完成
        
        # 验证日志文件完整性
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            assert len(lines) >= 500  # 应该有足够的日志行
    
    def test_cleanup_old_logs(self, temp_dir):
        """测试清理旧日志"""
        import time
        from datetime import datetime, timedelta
        
        # 创建一些旧日志文件
        old_files = []
        for i in range(5):
            old_file = os.path.join(temp_dir, f"old_log_{i}.log")
            with open(old_file, 'w') as f:
                f.write(f"旧日志文件 {i}")
            old_files.append(old_file)
        
        # 修改文件时间为7天前
        old_time = time.time() - (7 * 24 * 60 * 60)
        for file_path in old_files:
            os.utime(file_path, (old_time, old_time))
        
        # 创建新日志文件
        new_file = os.path.join(temp_dir, "new_log.log")
        with open(new_file, 'w') as f:
            f.write("新日志文件")
        
        # 清理旧日志（保留3天内的）
        LoggerUtils.cleanup_old_logs(temp_dir, days=3)
        
        # 验证旧文件被删除，新文件保留
        for old_file in old_files:
            assert not os.path.exists(old_file)
        assert os.path.exists(new_file)