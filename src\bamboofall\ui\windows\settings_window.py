"""设置窗口类

用于管理应用程序的各种设置。
"""

from typing import Optional, Dict, Any, List
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QGroupBox,
    QLabel, QPushButton, QFrame, QComboBox, QSpinBox, QCheckBox,
    QLineEdit, QTextEdit, QSlider, QColorDialog, QFontDialog,
    QFileDialog, QListWidget, QListWidgetItem, QFormLayout,
    QScrollArea, QButtonGroup, QRadioButton, QProgressBar
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QAction, QIcon, QFont, QColor, QPalette
import os
from pathlib import Path

from .base_window import BaseWindow
from ..widgets.modern_widgets import ModernButton
from ..themes.theme_manager import get_theme_manager
from ...utils.config_utils import get_config_manager
from ...services.ai_service import AIService
from ...exceptions import handle_exceptions, ConfigurationError


class SettingsWindow(BaseWindow):
    """设置窗口类
    
    功能：
    - 通用设置
    - 编辑器设置
    - AI服务设置
    - 主题设置
    - 快捷键设置
    - 插件管理
    """
    
    # 信号
    settings_changed = pyqtSignal(str, dict)  # 设置类型, 设置数据
    theme_changed = pyqtSignal(str)  # 主题名称
    ai_settings_changed = pyqtSignal(dict)  # AI设置
    
    def __init__(self, parent=None):
        # 管理器
        self.theme_manager = get_theme_manager()
        self.config_manager = get_config_manager()
        self.ai_service = AIService()
        
        # UI组件
        self.central_widget = None
        self.settings_tabs = None
        
        # 设置页面
        self.general_page = None
        self.editor_page = None
        self.ai_page = None
        self.theme_page = None
        self.shortcuts_page = None
        self.plugins_page = None
        
        # 设置数据
        self.current_settings = {}
        self.modified_settings = set()
        
        super().__init__(parent)
        
        # 加载当前设置
        self.load_current_settings()
    
    def setup_window_properties(self):
        """设置窗口属性"""
        self.setWindowTitle("笔落 - 设置")
        self.setMinimumSize(800, 600)
        self.resize(1000, 700)
        self.setModal(True)  # 模态窗口
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(self.central_widget)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(16)
        
        # 创建标签页
        self.settings_tabs = QTabWidget()
        self.settings_tabs.setTabPosition(QTabWidget.TabPosition.North)
        
        # 创建各个设置页面
        self.setup_general_page()
        self.setup_editor_page()
        self.setup_ai_page()
        self.setup_theme_page()
        self.setup_shortcuts_page()
        self.setup_plugins_page()
        
        main_layout.addWidget(self.settings_tabs)
        
        # 创建按钮区域
        self.setup_button_area(main_layout)
    
    def setup_general_page(self):
        """设置通用设置页面"""
        self.general_page = QScrollArea()
        general_widget = QWidget()
        general_layout = QVBoxLayout(general_widget)
        general_layout.setSpacing(16)
        
        # 应用程序设置
        app_group = QGroupBox("应用程序")
        app_layout = QFormLayout(app_group)
        
        # 语言设置
        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "English"])
        self.language_combo.currentTextChanged.connect(self.on_setting_changed)
        app_layout.addRow("语言:", self.language_combo)
        
        # 启动时行为
        self.startup_combo = QComboBox()
        self.startup_combo.addItems(["显示主窗口", "恢复上次会话", "显示欢迎页面"])
        self.startup_combo.currentTextChanged.connect(self.on_setting_changed)
        app_layout.addRow("启动时:", self.startup_combo)
        
        # 自动保存间隔
        self.autosave_spin = QSpinBox()
        self.autosave_spin.setRange(10, 600)
        self.autosave_spin.setSuffix(" 秒")
        self.autosave_spin.valueChanged.connect(self.on_setting_changed)
        app_layout.addRow("自动保存间隔:", self.autosave_spin)
        
        # 最大撤销步数
        self.undo_spin = QSpinBox()
        self.undo_spin.setRange(10, 1000)
        self.undo_spin.valueChanged.connect(self.on_setting_changed)
        app_layout.addRow("最大撤销步数:", self.undo_spin)
        
        general_layout.addWidget(app_group)
        
        # 文件设置
        file_group = QGroupBox("文件")
        file_layout = QFormLayout(file_group)
        
        # 默认项目路径
        project_path_layout = QHBoxLayout()
        self.project_path_edit = QLineEdit()
        self.project_path_edit.textChanged.connect(self.on_setting_changed)
        project_path_browse_btn = QPushButton("浏览")
        project_path_browse_btn.clicked.connect(self.browse_project_path)
        project_path_layout.addWidget(self.project_path_edit)
        project_path_layout.addWidget(project_path_browse_btn)
        file_layout.addRow("默认项目路径:", project_path_layout)
        
        # 备份设置
        self.backup_enabled_check = QCheckBox("启用自动备份")
        self.backup_enabled_check.toggled.connect(self.on_setting_changed)
        file_layout.addRow(self.backup_enabled_check)
        
        self.backup_interval_spin = QSpinBox()
        self.backup_interval_spin.setRange(1, 24)
        self.backup_interval_spin.setSuffix(" 小时")
        self.backup_interval_spin.valueChanged.connect(self.on_setting_changed)
        file_layout.addRow("备份间隔:", self.backup_interval_spin)
        
        self.backup_count_spin = QSpinBox()
        self.backup_count_spin.setRange(1, 50)
        self.backup_count_spin.valueChanged.connect(self.on_setting_changed)
        file_layout.addRow("保留备份数量:", self.backup_count_spin)
        
        general_layout.addWidget(file_group)
        
        # 性能设置
        performance_group = QGroupBox("性能")
        performance_layout = QFormLayout(performance_group)
        
        # 内存缓存大小
        self.cache_size_spin = QSpinBox()
        self.cache_size_spin.setRange(64, 2048)
        self.cache_size_spin.setSuffix(" MB")
        self.cache_size_spin.valueChanged.connect(self.on_setting_changed)
        performance_layout.addRow("内存缓存大小:", self.cache_size_spin)
        
        # 启用硬件加速
        self.hardware_accel_check = QCheckBox("启用硬件加速")
        self.hardware_accel_check.toggled.connect(self.on_setting_changed)
        performance_layout.addRow(self.hardware_accel_check)
        
        general_layout.addWidget(performance_group)
        general_layout.addStretch()
        
        self.general_page.setWidget(general_widget)
        self.settings_tabs.addTab(self.general_page, "通用")
    
    def setup_editor_page(self):
        """设置编辑器设置页面"""
        self.editor_page = QScrollArea()
        editor_widget = QWidget()
        editor_layout = QVBoxLayout(editor_widget)
        editor_layout.setSpacing(16)
        
        # 字体设置
        font_group = QGroupBox("字体")
        font_layout = QFormLayout(font_group)
        
        # 编辑器字体
        editor_font_layout = QHBoxLayout()
        self.editor_font_label = QLabel("Microsoft YaHei, 14pt")
        self.editor_font_btn = QPushButton("选择字体")
        self.editor_font_btn.clicked.connect(self.choose_editor_font)
        editor_font_layout.addWidget(self.editor_font_label)
        editor_font_layout.addWidget(self.editor_font_btn)
        font_layout.addRow("编辑器字体:", editor_font_layout)
        
        # UI字体
        ui_font_layout = QHBoxLayout()
        self.ui_font_label = QLabel("Microsoft YaHei, 10pt")
        self.ui_font_btn = QPushButton("选择字体")
        self.ui_font_btn.clicked.connect(self.choose_ui_font)
        ui_font_layout.addWidget(self.ui_font_label)
        ui_font_layout.addWidget(self.ui_font_btn)
        font_layout.addRow("界面字体:", ui_font_layout)
        
        editor_layout.addWidget(font_group)
        
        # 编辑器行为
        behavior_group = QGroupBox("编辑器行为")
        behavior_layout = QFormLayout(behavior_group)
        
        # 自动换行
        self.word_wrap_check = QCheckBox("自动换行")
        self.word_wrap_check.toggled.connect(self.on_setting_changed)
        behavior_layout.addRow(self.word_wrap_check)
        
        # 显示行号
        self.line_numbers_check = QCheckBox("显示行号")
        self.line_numbers_check.toggled.connect(self.on_setting_changed)
        behavior_layout.addRow(self.line_numbers_check)
        
        # 高亮当前行
        self.highlight_line_check = QCheckBox("高亮当前行")
        self.highlight_line_check.toggled.connect(self.on_setting_changed)
        behavior_layout.addRow(self.highlight_line_check)
        
        # Tab键宽度
        self.tab_width_spin = QSpinBox()
        self.tab_width_spin.setRange(2, 8)
        self.tab_width_spin.valueChanged.connect(self.on_setting_changed)
        behavior_layout.addRow("Tab宽度:", self.tab_width_spin)
        
        # 行间距
        self.line_spacing_slider = QSlider(Qt.Orientation.Horizontal)
        self.line_spacing_slider.setRange(100, 300)
        self.line_spacing_slider.valueChanged.connect(self.on_line_spacing_changed)
        self.line_spacing_label = QLabel("1.2")
        line_spacing_layout = QHBoxLayout()
        line_spacing_layout.addWidget(self.line_spacing_slider)
        line_spacing_layout.addWidget(self.line_spacing_label)
        behavior_layout.addRow("行间距:", line_spacing_layout)
        
        editor_layout.addWidget(behavior_group)
        
        # 自动完成
        completion_group = QGroupBox("自动完成")
        completion_layout = QFormLayout(completion_group)
        
        # 启用自动完成
        self.auto_completion_check = QCheckBox("启用自动完成")
        self.auto_completion_check.toggled.connect(self.on_setting_changed)
        completion_layout.addRow(self.auto_completion_check)
        
        # 自动完成延迟
        self.completion_delay_spin = QSpinBox()
        self.completion_delay_spin.setRange(100, 2000)
        self.completion_delay_spin.setSuffix(" ms")
        self.completion_delay_spin.valueChanged.connect(self.on_setting_changed)
        completion_layout.addRow("完成延迟:", self.completion_delay_spin)
        
        # 最大建议数量
        self.max_suggestions_spin = QSpinBox()
        self.max_suggestions_spin.setRange(5, 50)
        self.max_suggestions_spin.valueChanged.connect(self.on_setting_changed)
        completion_layout.addRow("最大建议数量:", self.max_suggestions_spin)
        
        editor_layout.addWidget(completion_group)
        editor_layout.addStretch()
        
        self.editor_page.setWidget(editor_widget)
        self.settings_tabs.addTab(self.editor_page, "编辑器")
    
    def setup_ai_page(self):
        """设置AI服务设置页面"""
        self.ai_page = QScrollArea()
        ai_widget = QWidget()
        ai_layout = QVBoxLayout(ai_widget)
        ai_layout.setSpacing(16)
        
        # AI服务配置
        service_group = QGroupBox("AI服务配置")
        service_layout = QFormLayout(service_group)
        
        # API密钥
        self.api_key_edit = QLineEdit()
        self.api_key_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.api_key_edit.textChanged.connect(self.on_setting_changed)
        service_layout.addRow("API密钥:", self.api_key_edit)
        
        # 模型选择
        self.model_combo = QComboBox()
        self.model_combo.addItems(["claude-3-sonnet-20240229", "claude-3-haiku-20240307", "claude-3-opus-20240229"])
        self.model_combo.currentTextChanged.connect(self.on_setting_changed)
        service_layout.addRow("模型:", self.model_combo)
        
        # 最大令牌数
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(100, 4000)
        self.max_tokens_spin.valueChanged.connect(self.on_setting_changed)
        service_layout.addRow("最大令牌数:", self.max_tokens_spin)
        
        # 温度设置
        self.temperature_slider = QSlider(Qt.Orientation.Horizontal)
        self.temperature_slider.setRange(0, 100)
        self.temperature_slider.valueChanged.connect(self.on_temperature_changed)
        self.temperature_label = QLabel("0.7")
        temp_layout = QHBoxLayout()
        temp_layout.addWidget(self.temperature_slider)
        temp_layout.addWidget(self.temperature_label)
        service_layout.addRow("创造性 (温度):", temp_layout)
        
        # 测试连接按钮
        self.test_connection_btn = ModernButton("测试连接")
        self.test_connection_btn.clicked.connect(self.test_ai_connection)
        service_layout.addRow(self.test_connection_btn)
        
        ai_layout.addWidget(service_group)
        
        # AI功能设置
        features_group = QGroupBox("AI功能")
        features_layout = QFormLayout(features_group)
        
        # 启用AI续写
        self.ai_continue_check = QCheckBox("启用AI续写")
        self.ai_continue_check.toggled.connect(self.on_setting_changed)
        features_layout.addRow(self.ai_continue_check)
        
        # 启用AI改写
        self.ai_rewrite_check = QCheckBox("启用AI改写")
        self.ai_rewrite_check.toggled.connect(self.on_setting_changed)
        features_layout.addRow(self.ai_rewrite_check)
        
        # 启用AI润色
        self.ai_polish_check = QCheckBox("启用AI润色")
        self.ai_polish_check.toggled.connect(self.on_setting_changed)
        features_layout.addRow(self.ai_polish_check)
        
        # 启用AI角色生成
        self.ai_character_check = QCheckBox("启用AI角色生成")
        self.ai_character_check.toggled.connect(self.on_setting_changed)
        features_layout.addRow(self.ai_character_check)
        
        # 启用AI大纲生成
        self.ai_outline_check = QCheckBox("启用AI大纲生成")
        self.ai_outline_check.toggled.connect(self.on_setting_changed)
        features_layout.addRow(self.ai_outline_check)
        
        ai_layout.addWidget(features_group)
        
        # AI行为设置
        behavior_group = QGroupBox("AI行为")
        behavior_layout = QFormLayout(behavior_group)
        
        # 续写长度
        self.continue_length_spin = QSpinBox()
        self.continue_length_spin.setRange(50, 1000)
        self.continue_length_spin.setSuffix(" 字")
        self.continue_length_spin.valueChanged.connect(self.on_setting_changed)
        behavior_layout.addRow("续写长度:", self.continue_length_spin)
        
        # 自动应用建议
        self.auto_apply_check = QCheckBox("自动应用AI建议")
        self.auto_apply_check.toggled.connect(self.on_setting_changed)
        behavior_layout.addRow(self.auto_apply_check)
        
        # 显示AI思考过程
        self.show_thinking_check = QCheckBox("显示AI思考过程")
        self.show_thinking_check.toggled.connect(self.on_setting_changed)
        behavior_layout.addRow(self.show_thinking_check)
        
        ai_layout.addWidget(behavior_group)
        ai_layout.addStretch()
        
        self.ai_page.setWidget(ai_widget)
        self.settings_tabs.addTab(self.ai_page, "AI服务")
    
    def setup_theme_page(self):
        """设置主题设置页面"""
        self.theme_page = QScrollArea()
        theme_widget = QWidget()
        theme_layout = QVBoxLayout(theme_widget)
        theme_layout.setSpacing(16)
        
        # 主题选择
        theme_group = QGroupBox("主题")
        theme_group_layout = QVBoxLayout(theme_group)
        
        # 主题选项
        self.theme_button_group = QButtonGroup()
        
        self.light_theme_radio = QRadioButton("浅色主题")
        self.light_theme_radio.toggled.connect(self.on_theme_changed)
        self.theme_button_group.addButton(self.light_theme_radio)
        theme_group_layout.addWidget(self.light_theme_radio)
        
        self.dark_theme_radio = QRadioButton("深色主题")
        self.dark_theme_radio.toggled.connect(self.on_theme_changed)
        self.theme_button_group.addButton(self.dark_theme_radio)
        theme_group_layout.addWidget(self.dark_theme_radio)
        
        self.auto_theme_radio = QRadioButton("跟随系统")
        self.auto_theme_radio.toggled.connect(self.on_theme_changed)
        self.theme_button_group.addButton(self.auto_theme_radio)
        theme_group_layout.addWidget(self.auto_theme_radio)
        
        theme_layout.addWidget(theme_group)
        
        # 颜色自定义
        color_group = QGroupBox("颜色自定义")
        color_layout = QFormLayout(color_group)
        
        # 主色调
        self.primary_color_btn = QPushButton()
        self.primary_color_btn.setFixedSize(50, 30)
        self.primary_color_btn.clicked.connect(lambda: self.choose_color('primary'))
        color_layout.addRow("主色调:", self.primary_color_btn)
        
        # 强调色
        self.accent_color_btn = QPushButton()
        self.accent_color_btn.setFixedSize(50, 30)
        self.accent_color_btn.clicked.connect(lambda: self.choose_color('accent'))
        color_layout.addRow("强调色:", self.accent_color_btn)
        
        # 背景色
        self.background_color_btn = QPushButton()
        self.background_color_btn.setFixedSize(50, 30)
        self.background_color_btn.clicked.connect(lambda: self.choose_color('background'))
        color_layout.addRow("背景色:", self.background_color_btn)
        
        # 文本色
        self.text_color_btn = QPushButton()
        self.text_color_btn.setFixedSize(50, 30)
        self.text_color_btn.clicked.connect(lambda: self.choose_color('text'))
        color_layout.addRow("文本色:", self.text_color_btn)
        
        # 重置颜色按钮
        reset_colors_btn = ModernButton("重置为默认颜色")
        reset_colors_btn.clicked.connect(self.reset_colors)
        color_layout.addRow(reset_colors_btn)
        
        theme_layout.addWidget(color_group)
        
        # 界面设置
        ui_group = QGroupBox("界面")
        ui_layout = QFormLayout(ui_group)
        
        # 窗口透明度
        self.opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.opacity_slider.setRange(70, 100)
        self.opacity_slider.valueChanged.connect(self.on_opacity_changed)
        self.opacity_label = QLabel("100%")
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        ui_layout.addRow("窗口透明度:", opacity_layout)
        
        # 动画效果
        self.animations_check = QCheckBox("启用动画效果")
        self.animations_check.toggled.connect(self.on_setting_changed)
        ui_layout.addRow(self.animations_check)
        
        # 圆角边框
        self.rounded_corners_check = QCheckBox("圆角边框")
        self.rounded_corners_check.toggled.connect(self.on_setting_changed)
        ui_layout.addRow(self.rounded_corners_check)
        
        theme_layout.addWidget(ui_group)
        theme_layout.addStretch()
        
        self.theme_page.setWidget(theme_widget)
        self.settings_tabs.addTab(self.theme_page, "主题")
    
    def setup_shortcuts_page(self):
        """设置快捷键设置页面"""
        self.shortcuts_page = QWidget()
        shortcuts_layout = QVBoxLayout(self.shortcuts_page)
        shortcuts_layout.setSpacing(16)
        
        # 快捷键列表
        shortcuts_label = QLabel("快捷键设置")
        shortcuts_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        shortcuts_layout.addWidget(shortcuts_label)
        
        # 快捷键列表控件
        self.shortcuts_list = QListWidget()
        self.load_shortcuts()
        shortcuts_layout.addWidget(self.shortcuts_list)
        
        # 快捷键编辑按钮
        shortcuts_buttons = QHBoxLayout()
        
        edit_shortcut_btn = ModernButton("编辑快捷键")
        edit_shortcut_btn.clicked.connect(self.edit_shortcut)
        shortcuts_buttons.addWidget(edit_shortcut_btn)
        
        reset_shortcut_btn = ModernButton("重置快捷键")
        reset_shortcut_btn.clicked.connect(self.reset_shortcut)
        shortcuts_buttons.addWidget(reset_shortcut_btn)
        
        reset_all_shortcuts_btn = ModernButton("重置所有")
        reset_all_shortcuts_btn.clicked.connect(self.reset_all_shortcuts)
        shortcuts_buttons.addWidget(reset_all_shortcuts_btn)
        
        shortcuts_buttons.addStretch()
        shortcuts_layout.addLayout(shortcuts_buttons)
        
        self.settings_tabs.addTab(self.shortcuts_page, "快捷键")
    
    def setup_plugins_page(self):
        """设置插件管理页面"""
        self.plugins_page = QWidget()
        plugins_layout = QVBoxLayout(self.plugins_page)
        plugins_layout.setSpacing(16)
        
        # 插件列表
        plugins_label = QLabel("插件管理")
        plugins_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        plugins_layout.addWidget(plugins_label)
        
        # 插件列表控件
        self.plugins_list = QListWidget()
        self.load_plugins()
        plugins_layout.addWidget(self.plugins_list)
        
        # 插件管理按钮
        plugins_buttons = QHBoxLayout()
        
        install_plugin_btn = ModernButton("安装插件")
        install_plugin_btn.clicked.connect(self.install_plugin)
        plugins_buttons.addWidget(install_plugin_btn)
        
        remove_plugin_btn = ModernButton("移除插件")
        remove_plugin_btn.clicked.connect(self.remove_plugin)
        plugins_buttons.addWidget(remove_plugin_btn)
        
        refresh_plugins_btn = ModernButton("刷新列表")
        refresh_plugins_btn.clicked.connect(self.refresh_plugins)
        plugins_buttons.addWidget(refresh_plugins_btn)
        
        plugins_buttons.addStretch()
        plugins_layout.addLayout(plugins_buttons)
        
        self.settings_tabs.addTab(self.plugins_page, "插件")
    
    def setup_button_area(self, layout):
        """设置按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 恢复默认按钮
        restore_defaults_btn = QPushButton("恢复默认")
        restore_defaults_btn.clicked.connect(self.restore_defaults)
        button_layout.addWidget(restore_defaults_btn)
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 应用按钮
        apply_btn = QPushButton("应用")
        apply_btn.clicked.connect(self.apply_settings)
        button_layout.addWidget(apply_btn)
        
        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept_settings)
        ok_btn.setDefault(True)
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
    
    def load_current_settings(self):
        """加载当前设置"""
        # 从配置管理器加载设置
        self.current_settings = self.config_manager.get_all_settings()
        
        # 应用设置到UI控件
        self.apply_settings_to_ui()
    
    def apply_settings_to_ui(self):
        """将设置应用到UI控件"""
        # 通用设置
        if hasattr(self, 'language_combo'):
            language = self.current_settings.get('language', '简体中文')
            self.language_combo.setCurrentText(language)
        
        if hasattr(self, 'startup_combo'):
            startup = self.current_settings.get('startup_behavior', '显示主窗口')
            self.startup_combo.setCurrentText(startup)
        
        if hasattr(self, 'autosave_spin'):
            autosave = self.current_settings.get('autosave_interval', 30)
            self.autosave_spin.setValue(autosave)
        
        # 编辑器设置
        if hasattr(self, 'word_wrap_check'):
            word_wrap = self.current_settings.get('word_wrap', True)
            self.word_wrap_check.setChecked(word_wrap)
        
        # AI设置
        if hasattr(self, 'api_key_edit'):
            api_key = self.current_settings.get('ai_api_key', '')
            self.api_key_edit.setText(api_key)
        
        if hasattr(self, 'model_combo'):
            model = self.current_settings.get('ai_model', 'claude-3-sonnet-20240229')
            self.model_combo.setCurrentText(model)
        
        # 主题设置
        if hasattr(self, 'light_theme_radio'):
            theme = self.current_settings.get('theme', 'light')
            if theme == 'light':
                self.light_theme_radio.setChecked(True)
            elif theme == 'dark':
                self.dark_theme_radio.setChecked(True)
            else:
                self.auto_theme_radio.setChecked(True)
    
    def on_setting_changed(self):
        """设置变化处理"""
        sender = self.sender()
        if sender:
            self.modified_settings.add(sender.objectName() or str(sender))
    
    def on_line_spacing_changed(self, value: int):
        """行间距变化处理"""
        spacing = value / 100.0
        self.line_spacing_label.setText(f"{spacing:.1f}")
        self.on_setting_changed()
    
    def on_temperature_changed(self, value: int):
        """AI温度变化处理"""
        temp = value / 100.0
        self.temperature_label.setText(f"{temp:.1f}")
        self.on_setting_changed()
    
    def on_opacity_changed(self, value: int):
        """透明度变化处理"""
        self.opacity_label.setText(f"{value}%")
        self.on_setting_changed()
    
    def on_theme_changed(self, checked: bool):
        """主题变化处理"""
        if not checked:
            return
        
        sender = self.sender()
        if sender == self.light_theme_radio:
            theme = 'light'
        elif sender == self.dark_theme_radio:
            theme = 'dark'
        else:
            theme = 'auto'
        
        self.theme_changed.emit(theme)
        self.on_setting_changed()
    
    # 各种设置处理方法
    def browse_project_path(self):
        """浏览项目路径"""
        path = QFileDialog.getExistingDirectory(self, "选择默认项目路径")
        if path:
            self.project_path_edit.setText(path)
    
    def choose_editor_font(self):
        """选择编辑器字体"""
        font, ok = QFontDialog.getFont(self)
        if ok:
            self.editor_font_label.setText(f"{font.family()}, {font.pointSize()}pt")
            self.on_setting_changed()
    
    def choose_ui_font(self):
        """选择UI字体"""
        font, ok = QFontDialog.getFont(self)
        if ok:
            self.ui_font_label.setText(f"{font.family()}, {font.pointSize()}pt")
            self.on_setting_changed()
    
    def choose_color(self, color_type: str):
        """选择颜色"""
        color = QColorDialog.getColor(self)
        if color.isValid():
            button = getattr(self, f"{color_type}_color_btn")
            button.setStyleSheet(f"background-color: {color.name()};")
            self.on_setting_changed()
    
    def reset_colors(self):
        """重置颜色"""
        # TODO: 实现颜色重置
        self.show_message("提示", "颜色已重置为默认值")
    
    @handle_exceptions()
    def test_ai_connection(self):
        """测试AI连接"""
        try:
            api_key = self.api_key_edit.text()
            if not api_key:
                self.show_message("错误", "请先输入API密钥", "error")
                return
            
            # TODO: 实现AI连接测试
            self.show_message("成功", "AI服务连接成功")
        except Exception as e:
            self.show_message("错误", f"AI服务连接失败: {e}", "error")
    
    def load_shortcuts(self):
        """加载快捷键列表"""
        # TODO: 实现快捷键加载
        shortcuts = [
            ("新建项目", "Ctrl+N"),
            ("打开项目", "Ctrl+O"),
            ("保存", "Ctrl+S"),
            ("撤销", "Ctrl+Z"),
            ("重做", "Ctrl+Y"),
            ("查找", "Ctrl+F"),
            ("AI续写", "Ctrl+Shift+C"),
            ("专注模式", "F11")
        ]
        
        for name, shortcut in shortcuts:
            item = QListWidgetItem(f"{name}: {shortcut}")
            item.setData(Qt.ItemDataRole.UserRole, (name, shortcut))
            self.shortcuts_list.addItem(item)
    
    def edit_shortcut(self):
        """编辑快捷键"""
        # TODO: 实现快捷键编辑对话框
        self.show_message("提示", "快捷键编辑功能正在开发中")
    
    def reset_shortcut(self):
        """重置快捷键"""
        # TODO: 实现单个快捷键重置
        self.show_message("提示", "快捷键已重置")
    
    def reset_all_shortcuts(self):
        """重置所有快捷键"""
        # TODO: 实现所有快捷键重置
        self.show_message("提示", "所有快捷键已重置")
    
    def load_plugins(self):
        """加载插件列表"""
        # TODO: 实现插件加载
        plugins = [
            ("Markdown导出插件", "1.0.0", True),
            ("PDF导出插件", "1.2.0", False),
            ("语法检查插件", "0.9.0", True)
        ]
        
        for name, version, enabled in plugins:
            status = "已启用" if enabled else "已禁用"
            item = QListWidgetItem(f"{name} v{version} - {status}")
            item.setData(Qt.ItemDataRole.UserRole, (name, version, enabled))
            self.plugins_list.addItem(item)
    
    def install_plugin(self):
        """安装插件"""
        # TODO: 实现插件安装
        self.show_message("提示", "插件安装功能正在开发中")
    
    def remove_plugin(self):
        """移除插件"""
        # TODO: 实现插件移除
        self.show_message("提示", "插件移除功能正在开发中")
    
    def refresh_plugins(self):
        """刷新插件列表"""
        self.plugins_list.clear()
        self.load_plugins()
    
    def restore_defaults(self):
        """恢复默认设置"""
        reply = self.show_message("确认", "确定要恢复所有设置为默认值吗？", "question")
        if reply == self.show_message.__self__.StandardButton.Yes:
            # TODO: 实现恢复默认设置
            self.show_message("提示", "设置已恢复为默认值")
    
    @handle_exceptions()
    def apply_settings(self):
        """应用设置"""
        try:
            # 收集所有设置
            settings = self.collect_settings()
            
            # 保存设置
            for key, value in settings.items():
                self.config_manager.set(key, value)
            
            # 发出设置变化信号
            self.settings_changed.emit("all", settings)
            
            self.show_message("成功", "设置已应用")
            
        except Exception as e:
            self.show_message("错误", f"应用设置失败: {e}", "error")
    
    def collect_settings(self) -> Dict[str, Any]:
        """收集所有设置"""
        settings = {}
        
        # 通用设置
        if hasattr(self, 'language_combo'):
            settings['language'] = self.language_combo.currentText()
        if hasattr(self, 'startup_combo'):
            settings['startup_behavior'] = self.startup_combo.currentText()
        if hasattr(self, 'autosave_spin'):
            settings['autosave_interval'] = self.autosave_spin.value()
        
        # 编辑器设置
        if hasattr(self, 'word_wrap_check'):
            settings['word_wrap'] = self.word_wrap_check.isChecked()
        if hasattr(self, 'line_numbers_check'):
            settings['show_line_numbers'] = self.line_numbers_check.isChecked()
        
        # AI设置
        if hasattr(self, 'api_key_edit'):
            settings['ai_api_key'] = self.api_key_edit.text()
        if hasattr(self, 'model_combo'):
            settings['ai_model'] = self.model_combo.currentText()
        if hasattr(self, 'max_tokens_spin'):
            settings['ai_max_tokens'] = self.max_tokens_spin.value()
        
        # 主题设置
        if hasattr(self, 'light_theme_radio'):
            if self.light_theme_radio.isChecked():
                settings['theme'] = 'light'
            elif self.dark_theme_radio.isChecked():
                settings['theme'] = 'dark'
            else:
                settings['theme'] = 'auto'
        
        return settings
    
    def accept_settings(self):
        """确定并关闭"""
        self.apply_settings()
        self.accept()
    
    def reject(self):
        """取消并关闭"""
        self.close()
    
    def accept(self):
        """接受并关闭"""
        self.close()
    
    # 重写基类方法
    def on_save(self):
        """保存操作"""
        self.apply_settings()
    
    def cleanup(self):
        """清理资源"""
        # 如果有未保存的设置，询问是否保存
        if self.modified_settings:
            reply = self.show_message("确认", "有未保存的设置，是否保存？", "question")
            if reply == self.show_message.__self__.StandardButton.Yes:
                self.apply_settings()