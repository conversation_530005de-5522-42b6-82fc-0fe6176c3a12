# -*- coding: utf-8 -*-
"""
主窗口测试
"""

import pytest
import sys
from unittest.mock import Mock, patch, MagicMock
from PyQt5.QtWidgets import QApplication, QWidget
from PyQt5.QtCore import Qt
from PyQt5.QtTest import QTest

# 确保在导入UI模块之前有QApplication实例
if not QApplication.instance():
    app = QApplication(sys.argv)

from bamboofall.ui.main_window import MainWindow
from bamboofall.core.project_manager import ProjectManager
from bamboofall.ai.ai_service_manager import AIServiceManager


class TestMainWindow:
    """MainWindow测试类"""
    
    @pytest.fixture
    def app(self):
        """创建QApplication实例"""
        if not QApplication.instance():
            return QApplication(sys.argv)
        return QApplication.instance()
    
    @pytest.fixture
    def mock_project_manager(self):
        """模拟项目管理器"""
        manager = Mock(spec=ProjectManager)
        manager.get_all_projects.return_value = []
        manager.get_recent_projects.return_value = []
        manager.current_project = None
        return manager
    
    @pytest.fixture
    def mock_ai_service_manager(self):
        """模拟AI服务管理器"""
        manager = Mock(spec=AIServiceManager)
        manager.get_available_providers.return_value = ["openai", "anthropic"]
        manager.get_available_models.return_value = ["gpt-3.5-turbo", "claude-3-sonnet"]
        return manager
    
    @pytest.fixture
    def main_window(self, app, mock_project_manager, mock_ai_service_manager):
        """创建主窗口实例"""
        with patch('bamboofall.ui.main_window.ProjectManager', return_value=mock_project_manager), \
             patch('bamboofall.ui.main_window.AIServiceManager', return_value=mock_ai_service_manager):
            window = MainWindow()
            yield window
            window.close()
    
    def test_init(self, main_window):
        """测试主窗口初始化"""
        assert main_window is not None
        assert main_window.windowTitle() != ""
        assert main_window.isVisible() is False  # 窗口默认不显示
    
    def test_window_properties(self, main_window):
        """测试窗口属性"""
        # 测试窗口标题
        assert "BambooFall" in main_window.windowTitle()
        
        # 测试窗口大小
        assert main_window.width() > 0
        assert main_window.height() > 0
        
        # 测试窗口最小大小
        min_size = main_window.minimumSize()
        assert min_size.width() > 0
        assert min_size.height() > 0
    
    def test_menu_bar_creation(self, main_window):
        """测试菜单栏创建"""
        menu_bar = main_window.menuBar()
        assert menu_bar is not None
        
        # 检查主要菜单是否存在
        menus = menu_bar.findChildren(QWidget)
        menu_titles = [menu.title() for menu in menu_bar.actions() if menu.menu()]
        
        # 应该包含基本菜单
        expected_menus = ["文件", "编辑", "视图", "工具", "帮助"]
        for expected_menu in expected_menus:
            # 检查是否有包含预期文本的菜单（可能是中文或英文）
            found = any(expected_menu in title or 
                       (expected_menu == "文件" and "File" in title) or
                       (expected_menu == "编辑" and "Edit" in title) or
                       (expected_menu == "视图" and "View" in title) or
                       (expected_menu == "工具" and "Tools" in title) or
                       (expected_menu == "帮助" and "Help" in title)
                       for title in menu_titles)
            # 如果没有找到，至少应该有一些菜单
            if not found:
                assert len(menu_titles) > 0, f"应该有菜单栏，但没有找到 {expected_menu}"
    
    def test_toolbar_creation(self, main_window):
        """测试工具栏创建"""
        toolbars = main_window.findChildren(QWidget)
        toolbar_count = len([tb for tb in toolbars if 'toolbar' in tb.objectName().lower()])
        
        # 应该至少有一个工具栏或者工具栏区域
        assert toolbar_count >= 0  # 允许没有工具栏，但不应该出错
    
    def test_status_bar_creation(self, main_window):
        """测试状态栏创建"""
        status_bar = main_window.statusBar()
        assert status_bar is not None
        assert status_bar.isVisible() is True
    
    def test_central_widget(self, main_window):
        """测试中央部件"""
        central_widget = main_window.centralWidget()
        assert central_widget is not None
    
    @patch('bamboofall.ui.main_window.QMessageBox')
    def test_show_about_dialog(self, mock_message_box, main_window):
        """测试显示关于对话框"""
        # 模拟调用关于对话框
        if hasattr(main_window, 'show_about'):
            main_window.show_about()
            mock_message_box.about.assert_called_once()
    
    @patch('bamboofall.ui.main_window.QFileDialog')
    def test_new_project_dialog(self, mock_file_dialog, main_window, mock_project_manager):
        """测试新建项目对话框"""
        mock_file_dialog.getSaveFileName.return_value = ("/path/to/project.json", "")
        mock_project_manager.create_project.return_value = True
        
        # 模拟新建项目操作
        if hasattr(main_window, 'new_project'):
            main_window.new_project()
            # 验证是否调用了项目管理器的创建方法
            # 注意：这取决于具体的实现
    
    @patch('bamboofall.ui.main_window.QFileDialog')
    def test_open_project_dialog(self, mock_file_dialog, main_window, mock_project_manager):
        """测试打开项目对话框"""
        mock_file_dialog.getOpenFileName.return_value = ("/path/to/project.json", "")
        mock_project_manager.load_project.return_value = Mock()
        
        # 模拟打开项目操作
        if hasattr(main_window, 'open_project'):
            main_window.open_project()
            # 验证是否调用了项目管理器的加载方法
    
    def test_recent_projects_menu(self, main_window, mock_project_manager):
        """测试最近项目菜单"""
        # 设置一些最近项目
        recent_projects = [
            {"name": "项目1", "path": "/path/to/project1.json"},
            {"name": "项目2", "path": "/path/to/project2.json"}
        ]
        mock_project_manager.get_recent_projects.return_value = recent_projects
        
        # 刷新最近项目菜单
        if hasattr(main_window, 'update_recent_projects_menu'):
            main_window.update_recent_projects_menu()
        
        # 验证菜单是否更新
        # 这取决于具体的实现
    
    def test_project_changed_signal(self, main_window, mock_project_manager):
        """测试项目变更信号"""
        # 模拟项目变更
        if hasattr(main_window, 'on_project_changed'):
            test_project = Mock()
            test_project.name = "测试项目"
            main_window.on_project_changed(test_project)
            
            # 验证窗口标题是否更新
            assert "测试项目" in main_window.windowTitle()
    
    def test_ai_service_integration(self, main_window, mock_ai_service_manager):
        """测试AI服务集成"""
        # 验证AI服务管理器是否正确集成
        if hasattr(main_window, 'ai_service_manager'):
            assert main_window.ai_service_manager is not None
    
    def test_keyboard_shortcuts(self, main_window):
        """测试键盘快捷键"""
        # 检查是否设置了常用快捷键
        shortcuts = main_window.findChildren(QWidget)
        
        # 这里可以检查特定的快捷键是否存在
        # 例如 Ctrl+N (新建), Ctrl+O (打开), Ctrl+S (保存) 等
        # 具体实现取决于MainWindow的设计
    
    def test_window_state_persistence(self, main_window):
        """测试窗口状态持久化"""
        # 测试窗口大小和位置的保存/恢复
        original_size = main_window.size()
        original_pos = main_window.pos()
        
        # 改变窗口大小和位置
        main_window.resize(800, 600)
        main_window.move(100, 100)
        
        # 模拟保存设置
        if hasattr(main_window, 'save_settings'):
            main_window.save_settings()
        
        # 模拟恢复设置
        if hasattr(main_window, 'restore_settings'):
            main_window.restore_settings()
    
    def test_close_event(self, main_window, mock_project_manager):
        """测试关闭事件处理"""
        # 模拟有未保存的更改
        mock_project_manager.has_unsaved_changes.return_value = True
        
        # 创建一个模拟的关闭事件
        from PyQt5.QtGui import QCloseEvent
        close_event = QCloseEvent()
        
        # 测试关闭事件处理
        with patch('bamboofall.ui.main_window.QMessageBox') as mock_msg_box:
            mock_msg_box.question.return_value = mock_msg_box.Yes
            main_window.closeEvent(close_event)
            
            # 验证是否显示了保存确认对话框
            if mock_project_manager.has_unsaved_changes.return_value:
                # 应该显示确认对话框
                pass
    
    def test_drag_drop_support(self, main_window):
        """测试拖放支持"""
        # 检查是否启用了拖放
        assert main_window.acceptDrops() in [True, False]  # 不管是否启用，都不应该出错
    
    def test_context_menu(self, main_window):
        """测试右键菜单"""
        # 模拟右键点击
        from PyQt5.QtGui import QContextMenuEvent
        from PyQt5.QtCore import QPoint
        
        pos = QPoint(100, 100)
        context_event = QContextMenuEvent(QContextMenuEvent.Mouse, pos)
        
        # 发送右键菜单事件
        main_window.contextMenuEvent(context_event)
        
        # 验证是否正确处理（不应该崩溃）
    
    def test_theme_support(self, main_window):
        """测试主题支持"""
        # 测试是否支持主题切换
        if hasattr(main_window, 'set_theme'):
            main_window.set_theme('dark')
            main_window.set_theme('light')
        
        # 验证样式表是否应用
        style_sheet = main_window.styleSheet()
        # 不管是否有样式表，都不应该出错
    
    def test_error_handling(self, main_window):
        """测试错误处理"""
        # 测试各种错误情况的处理
        if hasattr(main_window, 'show_error'):
            main_window.show_error("测试错误消息")
        
        if hasattr(main_window, 'show_warning'):
            main_window.show_warning("测试警告消息")
        
        if hasattr(main_window, 'show_info'):
            main_window.show_info("测试信息消息")
    
    def test_progress_indication(self, main_window):
        """测试进度指示"""
        # 测试进度条或状态指示
        if hasattr(main_window, 'show_progress'):
            main_window.show_progress("正在处理...", 50)
        
        if hasattr(main_window, 'hide_progress'):
            main_window.hide_progress()
    
    def test_multi_language_support(self, main_window):
        """测试多语言支持"""
        # 测试语言切换功能
        if hasattr(main_window, 'set_language'):
            main_window.set_language('en')
            main_window.set_language('zh')
        
        # 验证界面文本是否更新
        # 这取决于具体的国际化实现
    
    def test_plugin_support(self, main_window):
        """测试插件支持"""
        # 测试插件系统集成
        if hasattr(main_window, 'load_plugins'):
            main_window.load_plugins()
        
        if hasattr(main_window, 'plugin_manager'):
            assert main_window.plugin_manager is not None
    
    def test_auto_save_functionality(self, main_window, mock_project_manager):
        """测试自动保存功能"""
        # 测试自动保存定时器
        if hasattr(main_window, 'auto_save_timer'):
            timer = main_window.auto_save_timer
            assert timer is not None
        
        # 模拟自动保存触发
        if hasattr(main_window, 'auto_save'):
            main_window.auto_save()
    
    def test_workspace_management(self, main_window):
        """测试工作区管理"""
        # 测试工作区布局保存和恢复
        if hasattr(main_window, 'save_workspace'):
            main_window.save_workspace()
        
        if hasattr(main_window, 'restore_workspace'):
            main_window.restore_workspace()
        
        if hasattr(main_window, 'reset_workspace'):
            main_window.reset_workspace()
    
    def test_search_functionality(self, main_window):
        """测试搜索功能"""
        # 测试全局搜索
        if hasattr(main_window, 'show_search'):
            main_window.show_search()
        
        if hasattr(main_window, 'search'):
            results = main_window.search("测试查询")
            assert isinstance(results, (list, type(None)))
    
    def test_backup_functionality(self, main_window, mock_project_manager):
        """测试备份功能"""
        # 测试项目备份
        if hasattr(main_window, 'backup_project'):
            main_window.backup_project()
        
        # 测试自动备份
        if hasattr(main_window, 'auto_backup'):
            main_window.auto_backup()
    
    def test_export_functionality(self, main_window, mock_project_manager):
        """测试导出功能"""
        # 测试各种导出格式
        export_formats = ['pdf', 'docx', 'html', 'txt']
        
        for format_type in export_formats:
            if hasattr(main_window, f'export_to_{format_type}'):
                export_method = getattr(main_window, f'export_to_{format_type}')
                # 模拟导出操作
                with patch('bamboofall.ui.main_window.QFileDialog') as mock_dialog:
                    mock_dialog.getSaveFileName.return_value = (f"/path/to/export.{format_type}", "")
                    export_method()
    
    def test_import_functionality(self, main_window, mock_project_manager):
        """测试导入功能"""
        # 测试各种导入格式
        import_formats = ['docx', 'txt', 'json']
        
        for format_type in import_formats:
            if hasattr(main_window, f'import_from_{format_type}'):
                import_method = getattr(main_window, f'import_from_{format_type}')
                # 模拟导入操作
                with patch('bamboofall.ui.main_window.QFileDialog') as mock_dialog:
                    mock_dialog.getOpenFileName.return_value = (f"/path/to/import.{format_type}", "")
                    import_method()
    
    def test_memory_management(self, main_window):
        """测试内存管理"""
        # 测试大量操作后的内存使用
        import gc
        
        # 执行一些操作
        for i in range(100):
            if hasattr(main_window, 'refresh'):
                main_window.refresh()
        
        # 强制垃圾回收
        gc.collect()
        
        # 验证窗口仍然可用
        assert main_window.isVisible() is not None
    
    def test_performance_monitoring(self, main_window):
        """测试性能监控"""
        import time
        
        # 测试响应时间
        start_time = time.time()
        
        # 执行一些UI操作
        if hasattr(main_window, 'refresh'):
            main_window.refresh()
        
        end_time = time.time()
        response_time = end_time - start_time
        
        # 响应时间应该在合理范围内（例如小于1秒）
        assert response_time < 1.0, f"响应时间过长: {response_time}秒"