[build-system]
requires = ["setuptools>=65.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "bamboofall"
version = "1.0.0"
description = "AI辅助小说创作平台 - Python版本"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Qoder AI", email = "<EMAIL>"}
]
maintainers = [
    {name = "Qoder AI", email = "<EMAIL>"}
]
keywords = ["小说创作", "AI", "写作", "PyQt6", "桌面应用"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Text Processing :: General",
    "Topic :: Multimedia :: Graphics",
    "Topic :: Desktop Environment",
    "Operating System :: OS Independent",
]
requires-python = ">=3.9"
dependencies = [
    "PyQt6>=6.6.0",
    "qdarkstyle>=3.0.0",
    "qtawesome>=1.3.0",
    "SQLAlchemy>=2.0.0",
    "alembic>=1.13.0",
    "pydantic>=2.5.0",
    "dataclasses-json>=0.6.0",
    "openai>=1.0.0",
    "anthropic>=0.8.0",
    "aiohttp>=3.9.0",
    "httpx>=0.25.0",
    "markdown>=3.5.0",
    "nltk>=3.8.0",
    "spacy>=3.7.0",
    "whoosh>=2.7.4",
    "matplotlib>=3.8.0",
    "plotly>=5.17.0",
    "networkx>=3.2.0",
    "reportlab>=4.0.0",
    "python-docx>=1.1.0",
    "ebooklib>=0.18",
    "fpdf2>=2.7.0",
    "jinja2>=3.1.0",
    "toml>=0.10.2",
    "python-dotenv>=1.0.0",
    "click>=8.1.0",
    "loguru>=0.7.0",
    "pathlib2>=2.3.7",
    "watchdog>=3.0.0",
    "asyncio-mqtt>=0.13.0",
    "trio>=0.23.0",
    "cryptography>=41.0.0",
    "keyring>=24.0.0",
]

[project.optional-dependencies]
dev = [
    "black>=23.9.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "isort>=5.12.0",
    "pylint>=3.0.0",
    "pytest>=7.4.0",
    "pytest-qt>=4.2.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=1.3.0",
    "pre-commit>=3.5.0",
    "tox>=4.11.0",
]
build = [
    "pyinstaller>=6.0.0",
    "cx-freeze>=6.15.0",
    "auto-py-to-exe>=2.40.0",
]

[project.urls]
Homepage = "https://github.com/qoder/bamboofall"
Documentation = "https://bamboofall.readthedocs.io/"
Repository = "https://github.com/qoder/bamboofall.git"
"Bug Tracker" = "https://github.com/qoder/bamboofall/issues"

[project.scripts]
bamboofall = "bamboofall.main:main"

[project.gui-scripts]
bamboofall-gui = "bamboofall.main:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]
include = ["bamboofall*"]
exclude = ["tests*"]

[tool.setuptools.package-data]
bamboofall = [
    "ui/themes/*.qss",
    "ui/icons/*.png",
    "ui/icons/*.svg",
    "resources/*",
    "templates/*",
]

# Black代码格式化配置
[tool.black]
line-length = 88
target-version = ["py39", "py310", "py311", "py312"]
include = '\.pyi?$'
extend-exclude = '''
/(
  # 排除的目录
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

# isort导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["bamboofall"]
known_third_party = ["PyQt6", "sqlalchemy", "pydantic"]

# MyPy类型检查配置
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "PyQt6.*",
    "qdarktheme.*",
    "qtawesome.*",
    "matplotlib.*",
    "plotly.*",
    "networkx.*",
    "reportlab.*",
    "ebooklib.*",
    "nltk.*",
    "spacy.*",
    "whoosh.*",
]
ignore_missing_imports = true

# Pytest测试配置
[tool.pytest.ini_options]
minversion = "6.0"
addopts = [
    "-ra",
    "--cov=bamboofall",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--strict-markers",
    "--strict-config",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "gui: marks tests as GUI tests",
]

# Coverage配置
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# Pylint配置
[tool.pylint.messages_control]
disable = [
    "missing-docstring",
    "too-few-public-methods",
    "too-many-arguments",
    "too-many-instance-attributes",
    "too-many-locals",
    "too-many-branches",
    "too-many-statements",
]

[tool.pylint.format]
max-line-length = 88