"""基础窗口类

提供所有窗口的通用功能和接口。
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, List
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QMenuBar, 
    QToolBar, QStatusBar, QMessageBox, QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QSettings
from PyQt6.QtGui import QAction, QIcon, QKeySequence, QCloseEvent
import logging

from ..themes.theme_manager import get_theme_manager
from ..widgets.modern_widgets import ModernButton
from ...utils.config_utils import get_config_manager
from ...utils.logger import LoggerMixin
from ...exceptions import (
    BambooFallError, UIExceptionHandler, handle_exceptions
)

logger = logging.getLogger(__name__)


class BaseWindow(QMain<PERSON><PERSON><PERSON>, Logger<PERSON><PERSON>in, ABC):
    """基础窗口类
    
    提供所有窗口的通用功能：
    - 主题管理
    - 异常处理
    - 配置管理
    - 窗口状态保存/恢复
    - 通用UI组件
    """
    
    # 通用信号
    window_closing = pyqtSignal()
    theme_changed = pyqtSignal(str)
    config_changed = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化管理器
        self.theme_manager = get_theme_manager()
        self.config_manager = get_config_manager()
        self.exception_handler = UIExceptionHandler()
        
        # 窗口状态
        self.is_initialized = False
        self.is_closing = False
        
        # 设置
        self.settings = QSettings()
        
        # 初始化窗口
        self._init_window()
        
    def _init_window(self):
        """初始化窗口"""
        try:
            # 设置基本属性
            self.setup_window_properties()
            
            # 创建UI
            self.setup_ui()
            
            # 设置菜单和工具栏
            self.setup_menu_bar()
            self.setup_tool_bar()
            self.setup_status_bar()
            
            # 连接信号
            self.connect_signals()
            
            # 应用主题
            self.apply_theme()
            
            # 恢复窗口状态
            self.restore_window_state()
            
            self.is_initialized = True
            self.logger.info(f"{self.__class__.__name__} 初始化完成")
            
        except Exception as e:
            self.exception_handler.handle_exception(e)
            raise
    
    @abstractmethod
    def setup_window_properties(self):
        """设置窗口属性（子类实现）"""
        pass
    
    @abstractmethod
    def setup_ui(self):
        """设置用户界面（子类实现）"""
        pass
    
    def setup_menu_bar(self):
        """设置菜单栏（可选重写）"""
        if not self.menuBar():
            return
            
        # 文件菜单
        file_menu = self.menuBar().addMenu("文件(&F)")
        
        # 新建
        new_action = QAction("新建(&N)", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.triggered.connect(self.on_new)
        file_menu.addAction(new_action)
        
        # 打开
        open_action = QAction("打开(&O)", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.triggered.connect(self.on_open)
        file_menu.addAction(open_action)
        
        # 保存
        save_action = QAction("保存(&S)", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.triggered.connect(self.on_save)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = self.menuBar().addMenu("编辑(&E)")
        
        # 撤销
        undo_action = QAction("撤销(&U)", self)
        undo_action.setShortcut(QKeySequence.StandardKey.Undo)
        undo_action.triggered.connect(self.on_undo)
        edit_menu.addAction(undo_action)
        
        # 重做
        redo_action = QAction("重做(&R)", self)
        redo_action.setShortcut(QKeySequence.StandardKey.Redo)
        redo_action.triggered.connect(self.on_redo)
        edit_menu.addAction(redo_action)
        
        # 视图菜单
        view_menu = self.menuBar().addMenu("视图(&V)")
        
        # 主题切换
        theme_menu = view_menu.addMenu("主题")
        
        light_action = QAction("浅色主题", self)
        light_action.triggered.connect(lambda: self.set_theme("light"))
        theme_menu.addAction(light_action)
        
        dark_action = QAction("深色主题", self)
        dark_action.triggered.connect(lambda: self.set_theme("dark"))
        theme_menu.addAction(dark_action)
        
        auto_action = QAction("自动主题", self)
        auto_action.triggered.connect(lambda: self.set_theme("auto"))
        theme_menu.addAction(auto_action)
        
        # 帮助菜单
        help_menu = self.menuBar().addMenu("帮助(&H)")
        
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_tool_bar(self):
        """设置工具栏（可选重写）"""
        pass
    
    def setup_status_bar(self):
        """设置状态栏（可选重写）"""
        if not self.statusBar():
            return
            
        self.statusBar().showMessage("就绪")
    
    def connect_signals(self):
        """连接信号（可选重写）"""
        # 连接主题管理器信号
        self.theme_manager.theme_changed.connect(self.on_theme_changed)
    
    def apply_theme(self):
        """应用主题"""
        try:
            current_theme = self.theme_manager.current_theme
            colors = self.theme_manager.current_colors
            fonts = self.theme_manager.FONTS
            
            # 设置窗口样式
            self.setStyleSheet(f"""
                QMainWindow {{
                    background-color: {colors['background']};
                    color: {colors['text_primary']};
                    font-family: {fonts['ui']};
                }}
                
                QMenuBar {{
                    background-color: {colors['surface']};
                    color: {colors['text_primary']};
                    border-bottom: 1px solid {colors['border']};
                    padding: 4px;
                }}
                
                QMenuBar::item {{
                    background-color: transparent;
                    padding: 6px 12px;
                    border-radius: 4px;
                }}
                
                QMenuBar::item:selected {{
                    background-color: {colors['surface_hover']};
                }}
                
                QMenu {{
                    background-color: {colors['surface']};
                    color: {colors['text_primary']};
                    border: 1px solid {colors['border']};
                    border-radius: 6px;
                    padding: 4px;
                }}
                
                QMenu::item {{
                    padding: 8px 16px;
                    border-radius: 4px;
                }}
                
                QMenu::item:selected {{
                    background-color: {colors['primary']};
                    color: white;
                }}
                
                QToolBar {{
                    background-color: {colors['surface']};
                    border: none;
                    spacing: 4px;
                    padding: 4px;
                }}
                
                QStatusBar {{
                    background-color: {colors['surface']};
                    color: {colors['text_secondary']};
                    border-top: 1px solid {colors['border']};
                    padding: 4px;
                }}
            """)
            
        except Exception as e:
            self.logger.error(f"应用主题失败: {e}")
    
    def save_window_state(self):
        """保存窗口状态"""
        try:
            window_key = f"{self.__class__.__name__}_geometry"
            self.settings.setValue(window_key, self.saveGeometry())
            
            state_key = f"{self.__class__.__name__}_state"
            self.settings.setValue(state_key, self.saveState())
            
        except Exception as e:
            self.logger.error(f"保存窗口状态失败: {e}")
    
    def restore_window_state(self):
        """恢复窗口状态"""
        try:
            window_key = f"{self.__class__.__name__}_geometry"
            geometry = self.settings.value(window_key)
            if geometry:
                self.restoreGeometry(geometry)
            
            state_key = f"{self.__class__.__name__}_state"
            state = self.settings.value(state_key)
            if state:
                self.restoreState(state)
                
        except Exception as e:
            self.logger.error(f"恢复窗口状态失败: {e}")
    
    @handle_exceptions()
    def set_theme(self, theme: str):
        """设置主题"""
        self.theme_manager.set_theme(theme)
        self.theme_changed.emit(theme)
    
    def on_theme_changed(self, theme: str):
        """主题变化处理"""
        self.apply_theme()
    
    def show_message(self, title: str, message: str, msg_type: str = "info"):
        """显示消息对话框"""
        if msg_type == "info":
            QMessageBox.information(self, title, message)
        elif msg_type == "warning":
            QMessageBox.warning(self, title, message)
        elif msg_type == "error":
            QMessageBox.critical(self, title, message)
        elif msg_type == "question":
            return QMessageBox.question(self, title, message)
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
            "笔落App - AI辅助小说创作平台\n\n"
            "版本: 1.0.0\n"
            "基于PyQt6开发")
    
    # 抽象方法 - 子类需要实现
    def on_new(self):
        """新建操作（子类实现）"""
        pass
    
    def on_open(self):
        """打开操作（子类实现）"""
        pass
    
    def on_save(self):
        """保存操作（子类实现）"""
        pass
    
    def on_undo(self):
        """撤销操作（子类实现）"""
        pass
    
    def on_redo(self):
        """重做操作（子类实现）"""
        pass
    
    def closeEvent(self, event: QCloseEvent):
        """窗口关闭事件"""
        if self.is_closing:
            event.accept()
            return
            
        try:
            # 发出关闭信号
            self.window_closing.emit()
            
            # 保存窗口状态
            self.save_window_state()
            
            # 执行清理工作
            self.cleanup()
            
            self.is_closing = True
            event.accept()
            
        except Exception as e:
            self.logger.error(f"窗口关闭时发生错误: {e}")
            event.accept()  # 即使出错也要关闭
    
    def cleanup(self):
        """清理资源（子类可重写）"""
        pass
    
    def get_window_info(self) -> Dict[str, Any]:
        """获取窗口信息"""
        return {
            'class_name': self.__class__.__name__,
            'title': self.windowTitle(),
            'size': (self.width(), self.height()),
            'position': (self.x(), self.y()),
            'is_visible': self.isVisible(),
            'is_maximized': self.isMaximized(),
            'is_minimized': self.isMinimized()
        }