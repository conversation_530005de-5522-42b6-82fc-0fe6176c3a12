# -*- coding: utf-8 -*-
"""
大纲模型测试
"""

import pytest
from datetime import datetime
from bamboofall.models.outline import Outline


class TestOutline:
    """Outline模型测试类"""
    
    def test_init_with_all_fields(self):
        """测试使用所有字段初始化"""
        outline = Outline(
            id="outline-001",
            project_id="project-001",
            title="故事大纲",
            content="这是故事的主要大纲内容...",
            level=1,
            parent_id="parent-001",
            order=1,
            status="active",
            notes="大纲笔记",
            tags=["主线", "重要"],
            characters=["张三", "李四"],
            locations=["村庄", "城市"],
            plot_points=["开端", "发展", "高潮", "结局"],
            created_at="2024-01-01T00:00:00",
            updated_at="2024-01-02T00:00:00"
        )
        
        assert outline.id == "outline-001"
        assert outline.project_id == "project-001"
        assert outline.title == "故事大纲"
        assert outline.content == "这是故事的主要大纲内容..."
        assert outline.level == 1
        assert outline.parent_id == "parent-001"
        assert outline.order == 1
        assert outline.status == "active"
        assert outline.notes == "大纲笔记"
        assert outline.tags == ["主线", "重要"]
        assert outline.characters == ["张三", "李四"]
        assert outline.locations == ["村庄", "城市"]
        assert outline.plot_points == ["开端", "发展", "高潮", "结局"]
        assert outline.created_at == "2024-01-01T00:00:00"
        assert outline.updated_at == "2024-01-02T00:00:00"
    
    def test_init_with_minimal_fields(self):
        """测试使用最少字段初始化"""
        outline = Outline(
            id="outline-minimal",
            project_id="project-001",
            title="最小大纲"
        )
        
        assert outline.id == "outline-minimal"
        assert outline.project_id == "project-001"
        assert outline.title == "最小大纲"
        assert outline.content == ""
        assert outline.level == 1
        assert outline.parent_id is None
        assert outline.order == 0
        assert outline.status == "active"
        assert outline.notes == ""
        assert outline.tags == []
        assert outline.characters == []
        assert outline.locations == []
        assert outline.plot_points == []
        assert outline.created_at is not None
        assert outline.updated_at is not None
    
    def test_auto_generated_timestamps(self):
        """测试自动生成的时间戳"""
        outline = Outline(
            id="timestamp-test",
            project_id="project-001",
            title="时间戳测试"
        )
        
        # 验证时间戳格式
        assert isinstance(outline.created_at, str)
        assert isinstance(outline.updated_at, str)
        
        # 验证时间戳可以解析
        created_dt = datetime.fromisoformat(outline.created_at.replace('Z', '+00:00'))
        updated_dt = datetime.fromisoformat(outline.updated_at.replace('Z', '+00:00'))
        
        assert isinstance(created_dt, datetime)
        assert isinstance(updated_dt, datetime)
    
    def test_to_dict(self):
        """测试转换为字典"""
        outline = Outline(
            id="dict-test",
            project_id="project-001",
            title="字典测试",
            content="测试内容",
            level=2,
            parent_id="parent-001",
            order=3,
            status="completed",
            tags=["测试", "字典"]
        )
        
        outline_dict = outline.to_dict()
        
        assert isinstance(outline_dict, dict)
        assert outline_dict["id"] == "dict-test"
        assert outline_dict["project_id"] == "project-001"
        assert outline_dict["title"] == "字典测试"
        assert outline_dict["content"] == "测试内容"
        assert outline_dict["level"] == 2
        assert outline_dict["parent_id"] == "parent-001"
        assert outline_dict["order"] == 3
        assert outline_dict["status"] == "completed"
        assert outline_dict["tags"] == ["测试", "字典"]
        assert "created_at" in outline_dict
        assert "updated_at" in outline_dict
    
    def test_from_dict(self):
        """测试从字典创建"""
        outline_data = {
            "id": "from-dict-test",
            "project_id": "project-001",
            "title": "从字典创建",
            "content": "从字典数据创建的大纲内容",
            "level": 3,
            "parent_id": "parent-002",
            "order": 2,
            "status": "draft",
            "notes": "需要完善",
            "tags": ["字典", "创建"],
            "characters": ["主角", "配角"],
            "locations": ["学校", "家"],
            "plot_points": ["相遇", "冲突", "和解"]
        }
        
        outline = Outline.from_dict(outline_data)
        
        assert outline.id == "from-dict-test"
        assert outline.project_id == "project-001"
        assert outline.title == "从字典创建"
        assert outline.content == "从字典数据创建的大纲内容"
        assert outline.level == 3
        assert outline.parent_id == "parent-002"
        assert outline.order == 2
        assert outline.status == "draft"
        assert outline.notes == "需要完善"
        assert outline.tags == ["字典", "创建"]
        assert outline.characters == ["主角", "配角"]
        assert outline.locations == ["学校", "家"]
        assert outline.plot_points == ["相遇", "冲突", "和解"]
    
    def test_from_dict_missing_fields(self):
        """测试从不完整字典创建"""
        outline_data = {
            "id": "incomplete-test",
            "project_id": "project-001",
            "title": "不完整大纲"
        }
        
        outline = Outline.from_dict(outline_data)
        
        assert outline.id == "incomplete-test"
        assert outline.project_id == "project-001"
        assert outline.title == "不完整大纲"
        assert outline.content == ""
        assert outline.level == 1
        assert outline.parent_id is None
        assert outline.order == 0
        assert outline.status == "active"
        assert outline.tags == []
        assert outline.characters == []
        assert outline.locations == []
        assert outline.plot_points == []
    
    def test_add_character(self):
        """测试添加角色"""
        outline = Outline(
            id="character-test",
            project_id="project-001",
            title="角色测试"
        )
        
        # 添加新角色
        outline.add_character("张三")
        assert "张三" in outline.characters
        
        # 添加重复角色
        outline.add_character("张三")
        assert outline.characters.count("张三") == 1
        
        # 添加多个角色
        outline.add_character("李四")
        outline.add_character("王五")
        assert len(outline.characters) == 3
        assert "李四" in outline.characters
        assert "王五" in outline.characters
    
    def test_remove_character(self):
        """测试移除角色"""
        outline = Outline(
            id="remove-char-test",
            project_id="project-001",
            title="移除角色测试",
            characters=["张三", "李四", "王五"]
        )
        
        # 移除存在的角色
        outline.remove_character("李四")
        assert "李四" not in outline.characters
        assert len(outline.characters) == 2
        
        # 移除不存在的角色
        outline.remove_character("不存在")
        assert len(outline.characters) == 2
    
    def test_add_location(self):
        """测试添加地点"""
        outline = Outline(
            id="location-test",
            project_id="project-001",
            title="地点测试"
        )
        
        # 添加新地点
        outline.add_location("村庄")
        assert "村庄" in outline.locations
        
        # 添加重复地点
        outline.add_location("村庄")
        assert outline.locations.count("村庄") == 1
        
        # 添加多个地点
        outline.add_location("森林")
        outline.add_location("城市")
        assert len(outline.locations) == 3
        assert "森林" in outline.locations
        assert "城市" in outline.locations
    
    def test_remove_location(self):
        """测试移除地点"""
        outline = Outline(
            id="remove-loc-test",
            project_id="project-001",
            title="移除地点测试",
            locations=["村庄", "森林", "城市"]
        )
        
        # 移除存在的地点
        outline.remove_location("森林")
        assert "森林" not in outline.locations
        assert len(outline.locations) == 2
        
        # 移除不存在的地点
        outline.remove_location("不存在")
        assert len(outline.locations) == 2
    
    def test_add_plot_point(self):
        """测试添加情节点"""
        outline = Outline(
            id="plot-test",
            project_id="project-001",
            title="情节测试"
        )
        
        # 添加新情节点
        outline.add_plot_point("开端")
        assert "开端" in outline.plot_points
        
        # 添加重复情节点
        outline.add_plot_point("开端")
        assert outline.plot_points.count("开端") == 1
        
        # 添加多个情节点
        outline.add_plot_point("发展")
        outline.add_plot_point("高潮")
        assert len(outline.plot_points) == 3
        assert "发展" in outline.plot_points
        assert "高潮" in outline.plot_points
    
    def test_remove_plot_point(self):
        """测试移除情节点"""
        outline = Outline(
            id="remove-plot-test",
            project_id="project-001",
            title="移除情节测试",
            plot_points=["开端", "发展", "高潮", "结局"]
        )
        
        # 移除存在的情节点
        outline.remove_plot_point("发展")
        assert "发展" not in outline.plot_points
        assert len(outline.plot_points) == 3
        
        # 移除不存在的情节点
        outline.remove_plot_point("不存在")
        assert len(outline.plot_points) == 3
    
    def test_add_tag(self):
        """测试添加标签"""
        outline = Outline(
            id="tag-test",
            project_id="project-001",
            title="标签测试"
        )
        
        # 添加新标签
        outline.add_tag("重要")
        assert "重要" in outline.tags
        
        # 添加重复标签
        outline.add_tag("重要")
        assert outline.tags.count("重要") == 1
        
        # 添加多个标签
        outline.add_tag("主线")
        outline.add_tag("核心")
        assert len(outline.tags) == 3
        assert "主线" in outline.tags
        assert "核心" in outline.tags
    
    def test_remove_tag(self):
        """测试移除标签"""
        outline = Outline(
            id="remove-tag-test",
            project_id="project-001",
            title="移除标签测试",
            tags=["重要", "主线", "核心"]
        )
        
        # 移除存在的标签
        outline.remove_tag("主线")
        assert "主线" not in outline.tags
        assert len(outline.tags) == 2
        
        # 移除不存在的标签
        outline.remove_tag("不存在")
        assert len(outline.tags) == 2
    
    def test_has_tag(self):
        """测试检查标签"""
        outline = Outline(
            id="has-tag-test",
            project_id="project-001",
            title="检查标签测试",
            tags=["重要", "主线"]
        )
        
        assert outline.has_tag("重要") is True
        assert outline.has_tag("主线") is True
        assert outline.has_tag("支线") is False
    
    def test_is_root(self):
        """测试是否为根节点"""
        root_outline = Outline(
            id="root-test",
            project_id="project-001",
            title="根节点测试",
            parent_id=None
        )
        
        child_outline = Outline(
            id="child-test",
            project_id="project-001",
            title="子节点测试",
            parent_id="root-test"
        )
        
        assert root_outline.is_root() is True
        assert child_outline.is_root() is False
    
    def test_is_leaf(self):
        """测试是否为叶子节点"""
        # 这个测试需要传入子节点列表
        parent_outline = Outline(
            id="parent-test",
            project_id="project-001",
            title="父节点测试"
        )
        
        child_outline = Outline(
            id="child-test",
            project_id="project-001",
            title="子节点测试",
            parent_id="parent-test"
        )
        
        all_outlines = [parent_outline, child_outline]
        
        assert parent_outline.is_leaf(all_outlines) is False
        assert child_outline.is_leaf(all_outlines) is True
    
    def test_get_children(self):
        """测试获取子节点"""
        parent = Outline(
            id="parent",
            project_id="project-001",
            title="父节点"
        )
        
        child1 = Outline(
            id="child1",
            project_id="project-001",
            title="子节点1",
            parent_id="parent",
            order=1
        )
        
        child2 = Outline(
            id="child2",
            project_id="project-001",
            title="子节点2",
            parent_id="parent",
            order=2
        )
        
        other_child = Outline(
            id="other-child",
            project_id="project-001",
            title="其他子节点",
            parent_id="other-parent"
        )
        
        all_outlines = [parent, child1, child2, other_child]
        children = parent.get_children(all_outlines)
        
        assert len(children) == 2
        assert children[0].id == "child1"
        assert children[1].id == "child2"
    
    def test_get_descendants(self):
        """测试获取所有后代节点"""
        root = Outline(id="root", project_id="proj1", title="根")
        child1 = Outline(id="child1", project_id="proj1", title="子1", parent_id="root")
        child2 = Outline(id="child2", project_id="proj1", title="子2", parent_id="root")
        grandchild = Outline(id="grandchild", project_id="proj1", title="孙", parent_id="child1")
        
        all_outlines = [root, child1, child2, grandchild]
        descendants = root.get_descendants(all_outlines)
        
        assert len(descendants) == 3
        descendant_ids = [d.id for d in descendants]
        assert "child1" in descendant_ids
        assert "child2" in descendant_ids
        assert "grandchild" in descendant_ids
    
    def test_get_ancestors(self):
        """测试获取所有祖先节点"""
        root = Outline(id="root", project_id="proj1", title="根")
        parent = Outline(id="parent", project_id="proj1", title="父", parent_id="root")
        child = Outline(id="child", project_id="proj1", title="子", parent_id="parent")
        
        all_outlines = [root, parent, child]
        ancestors = child.get_ancestors(all_outlines)
        
        assert len(ancestors) == 2
        ancestor_ids = [a.id for a in ancestors]
        assert "root" in ancestor_ids
        assert "parent" in ancestor_ids
    
    def test_get_depth(self):
        """测试获取节点深度"""
        root = Outline(id="root", project_id="proj1", title="根")
        parent = Outline(id="parent", project_id="proj1", title="父", parent_id="root")
        child = Outline(id="child", project_id="proj1", title="子", parent_id="parent")
        
        all_outlines = [root, parent, child]
        
        assert root.get_depth(all_outlines) == 0
        assert parent.get_depth(all_outlines) == 1
        assert child.get_depth(all_outlines) == 2
    
    def test_is_active(self):
        """测试是否为活跃状态"""
        active_outline = Outline(
            id="active-test",
            project_id="project-001",
            title="活跃测试",
            status="active"
        )
        
        completed_outline = Outline(
            id="completed-test",
            project_id="project-001",
            title="完成测试",
            status="completed"
        )
        
        assert active_outline.is_active() is True
        assert completed_outline.is_active() is False
    
    def test_is_completed(self):
        """测试是否已完成"""
        completed_outline = Outline(
            id="completed-test",
            project_id="project-001",
            title="完成测试",
            status="completed"
        )
        
        active_outline = Outline(
            id="active-test",
            project_id="project-001",
            title="活跃测试",
            status="active"
        )
        
        assert completed_outline.is_completed() is True
        assert active_outline.is_completed() is False
    
    def test_get_status_display(self):
        """测试获取状态显示"""
        active_outline = Outline(
            id="status-test1",
            project_id="project-001",
            title="状态测试1",
            status="active"
        )
        
        draft_outline = Outline(
            id="status-test2",
            project_id="project-001",
            title="状态测试2",
            status="draft"
        )
        
        completed_outline = Outline(
            id="status-test3",
            project_id="project-001",
            title="状态测试3",
            status="completed"
        )
        
        assert active_outline.get_status_display() in ["active", "活跃"]
        assert draft_outline.get_status_display() in ["draft", "草稿"]
        assert completed_outline.get_status_display() in ["completed", "已完成"]
    
    def test_validate(self):
        """测试验证"""
        # 有效大纲
        valid_outline = Outline(
            id="valid-outline",
            project_id="project-001",
            title="有效大纲"
        )
        assert valid_outline.validate() is True
        
        # 无效大纲（空标题）
        invalid_outline1 = Outline(
            id="invalid1",
            project_id="project-001",
            title=""
        )
        assert invalid_outline1.validate() is False
        
        # 无效大纲（空项目ID）
        invalid_outline2 = Outline(
            id="invalid2",
            project_id="",
            title="无效大纲2"
        )
        assert invalid_outline2.validate() is False
        
        # 无效大纲（负层级）
        invalid_outline3 = Outline(
            id="invalid3",
            project_id="project-001",
            title="无效大纲3",
            level=-1
        )
        assert invalid_outline3.validate() is False
        
        # 无效大纲（负序号）
        invalid_outline4 = Outline(
            id="invalid4",
            project_id="project-001",
            title="无效大纲4",
            order=-1
        )
        assert invalid_outline4.validate() is False
    
    def test_equality(self):
        """测试相等性比较"""
        outline1 = Outline(
            id="equal-test",
            project_id="project-001",
            title="相等测试"
        )
        
        outline2 = Outline(
            id="equal-test",
            project_id="project-001",
            title="相等测试"
        )
        
        outline3 = Outline(
            id="different-test",
            project_id="project-001",
            title="不同测试"
        )
        
        # 相同ID的大纲应该相等
        assert outline1 == outline2
        
        # 不同ID的大纲不相等
        assert outline1 != outline3
    
    def test_string_representation(self):
        """测试字符串表示"""
        outline = Outline(
            id="str-test",
            project_id="project-001",
            title="字符串测试",
            level=2
        )
        
        str_repr = str(outline)
        assert "字符串测试" in str_repr
        assert "str-test" in str_repr
    
    def test_copy(self):
        """测试复制大纲"""
        original = Outline(
            id="original",
            project_id="project-001",
            title="原始大纲",
            content="原始内容",
            level=2,
            parent_id="parent-001",
            order=1,
            characters=["张三", "李四"],
            tags=["标签1", "标签2"]
        )
        
        # 复制大纲
        copied = original.copy(
            new_id="copied",
            new_title="复制大纲",
            new_project_id="project-002",
            new_parent_id="parent-002",
            new_order=2
        )
        
        assert copied.id == "copied"
        assert copied.project_id == "project-002"
        assert copied.title == "复制大纲"
        assert copied.parent_id == "parent-002"
        assert copied.order == 2
        assert copied.content == "原始内容"
        assert copied.level == 2
        assert copied.characters == ["张三", "李四"]
        assert copied.tags == ["标签1", "标签2"]
        
        # 验证是独立的副本
        copied.add_tag("新标签")
        assert "新标签" not in original.tags
    
    def test_export_data(self):
        """测试导出数据"""
        outline = Outline(
            id="export-test",
            project_id="project-001",
            title="导出测试",
            content="导出内容",
            level=2,
            parent_id="parent-001",
            order=1,
            status="completed",
            characters=["主角"],
            tags=["重要"]
        )
        
        exported_data = outline.export_data()
        
        # 验证导出的数据包含所有必要字段
        assert exported_data["id"] == "export-test"
        assert exported_data["project_id"] == "project-001"
        assert exported_data["title"] == "导出测试"
        assert exported_data["content"] == "导出内容"
        assert exported_data["level"] == 2
        assert exported_data["parent_id"] == "parent-001"
        assert exported_data["order"] == 1
        assert exported_data["status"] == "completed"
        assert exported_data["characters"] == ["主角"]
        assert exported_data["tags"] == ["重要"]
        assert "created_at" in exported_data
        assert "updated_at" in exported_data
    
    def test_import_data(self):
        """测试导入数据"""
        import_data = {
            "id": "import-test",
            "project_id": "project-001",
            "title": "导入测试",
            "content": "导入内容",
            "level": 3,
            "parent_id": "parent-002",
            "order": 2,
            "status": "draft",
            "notes": "导入笔记",
            "tags": ["导入", "测试"],
            "characters": ["角色1", "角色2"],
            "locations": ["地点1", "地点2"],
            "plot_points": ["情节1", "情节2"],
            "created_at": "2024-01-01T00:00:00",
            "updated_at": "2024-01-02T00:00:00"
        }
        
        outline = Outline.import_data(import_data)
        
        assert outline.id == "import-test"
        assert outline.project_id == "project-001"
        assert outline.title == "导入测试"
        assert outline.content == "导入内容"
        assert outline.level == 3
        assert outline.parent_id == "parent-002"
        assert outline.order == 2
        assert outline.status == "draft"
        assert outline.notes == "导入笔记"
        assert outline.tags == ["导入", "测试"]
        assert outline.characters == ["角色1", "角色2"]
        assert outline.locations == ["地点1", "地点2"]
        assert outline.plot_points == ["情节1", "情节2"]
        assert outline.created_at == "2024-01-01T00:00:00"
        assert outline.updated_at == "2024-01-02T00:00:00"
    
    def test_get_summary(self):
        """测试获取大纲摘要"""
        outline = Outline(
            id="summary-test",
            project_id="project-001",
            title="摘要测试",
            content="这是一个很长的大纲内容，用来测试摘要功能。" * 10,
            level=2,
            status="active"
        )
        
        summary = outline.get_summary(max_length=50)
        
        assert isinstance(summary, str)
        assert len(summary) <= 50
        assert "摘要测试" in summary or "这是一个很长的大纲内容" in summary
    
    def test_update_from_dict(self):
        """测试从字典更新大纲"""
        outline = Outline(
            id="update-test",
            project_id="project-001",
            title="更新测试",
            level=1
        )
        
        update_data = {
            "content": "更新的内容",
            "level": 2,
            "status": "completed",
            "tags": ["新标签"],
            "parent_id": "new-parent"
        }
        
        outline.update_from_dict(update_data)
        
        assert outline.title == "更新测试"  # 未更新的字段保持不变
        assert outline.content == "更新的内容"
        assert outline.level == 2
        assert outline.status == "completed"
        assert outline.tags == ["新标签"]
        assert outline.parent_id == "new-parent"
    
    def test_build_tree_structure(self):
        """测试构建树形结构"""
        outlines = [
            Outline(id="root", project_id="proj1", title="根", order=1),
            Outline(id="child1", project_id="proj1", title="子1", parent_id="root", order=1),
            Outline(id="child2", project_id="proj1", title="子2", parent_id="root", order=2),
            Outline(id="grandchild", project_id="proj1", title="孙", parent_id="child1", order=1)
        ]
        
        tree = Outline.build_tree_structure(outlines)
        
        # 验证树形结构
        assert len(tree) == 1  # 只有一个根节点
        root = tree[0]
        assert root.id == "root"
        
        # 验证子节点
        children = root.get_children(outlines)
        assert len(children) == 2
        assert children[0].id == "child1"
        assert children[1].id == "child2"
        
        # 验证孙节点
        grandchildren = children[0].get_children(outlines)
        assert len(grandchildren) == 1
        assert grandchildren[0].id == "grandchild"
    
    def test_flatten_tree(self):
        """测试扁平化树形结构"""
        root = Outline(id="root", project_id="proj1", title="根")
        child1 = Outline(id="child1", project_id="proj1", title="子1", parent_id="root")
        child2 = Outline(id="child2", project_id="proj1", title="子2", parent_id="root")
        grandchild = Outline(id="grandchild", project_id="proj1", title="孙", parent_id="child1")
        
        tree_roots = [root]
        all_outlines = [root, child1, child2, grandchild]
        
        flattened = Outline.flatten_tree(tree_roots, all_outlines)
        
        # 验证扁平化结果按层级顺序排列
        assert len(flattened) == 4
        assert flattened[0].id == "root"
        # 子节点应该在父节点之后
        root_index = next(i for i, o in enumerate(flattened) if o.id == "root")
        child1_index = next(i for i, o in enumerate(flattened) if o.id == "child1")
        child2_index = next(i for i, o in enumerate(flattened) if o.id == "child2")
        grandchild_index = next(i for i, o in enumerate(flattened) if o.id == "grandchild")
        
        assert child1_index > root_index
        assert child2_index > root_index
        assert grandchild_index > child1_index