#!/usr/bin/env python3
"""
笔落App 功能测试脚本

测试核心功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

def test_imports():
    """测试核心模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试数据模型
        from bamboofall.models.project import Project, ProjectType
        print("✅ 数据模型导入成功")
        
        # 测试数据库
        from bamboofall.database.db_manager import get_database_manager
        print("✅ 数据库模块导入成功")
        
        # 测试项目管理器
        from bamboofall.core.project_manager import get_project_manager
        print("✅ 项目管理器导入成功")
        
        # 测试UI组件
        from bamboofall.ui.main_window import MainWindow
        print("✅ 主窗口导入成功")
        
        # 测试主题管理器
        from bamboofall.ui.themes.theme_manager import get_theme_manager
        print("✅ 主题管理器导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_database():
    """测试数据库连接"""
    print("\n🔍 测试数据库连接...")
    
    try:
        from bamboofall.database.db_manager import get_database_manager
        
        db_manager = get_database_manager()
        if db_manager.test_connection():
            print("✅ 数据库连接成功")
            return True
        else:
            print("❌ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False

def test_project_creation():
    """测试项目创建"""
    print("\n🔍 测试项目创建...")
    
    try:
        from bamboofall.core.project_manager import get_project_manager
        
        project_manager = get_project_manager()
        
        # 创建测试项目
        test_project = project_manager.create_project(
            name="测试项目",
            genre="fantasy",
            description="这是一个测试项目",
            author="测试用户"
        )
        
        print(f"✅ 项目创建成功: {test_project.name}")
        
        # 测试项目保存
        if project_manager.save_project(test_project):
            print("✅ 项目保存成功")
        
        # 测试项目加载
        loaded_project = project_manager.open_project(test_project.id)
        if loaded_project and loaded_project.name == test_project.name:
            print("✅ 项目加载成功")
        
        # 清理测试项目
        project_manager.delete_project(test_project.id)
        print("✅ 测试项目已清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 项目创建测试失败: {e}")
        return False

def test_ui_components():
    """测试UI组件"""
    print("\n🔍 测试UI组件...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from bamboofall.ui.main_window import MainWindow
        from bamboofall.ui.widgets.modern_widgets import ModernButton
        from bamboofall.ui.dialogs.new_project_dialog import NewProjectDialog
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 测试主窗口创建
        main_window = MainWindow()
        print("✅ 主窗口创建成功")
        
        # 测试自定义控件
        button = ModernButton("测试按钮")
        print("✅ 自定义控件创建成功")
        
        # 测试对话框
        dialog = NewProjectDialog()
        print("✅ 对话框创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🌸 笔落App 功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("数据库连接", test_database), 
        ("项目管理", test_project_creation),
        ("UI组件", test_ui_components),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用程序可以正常运行。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())