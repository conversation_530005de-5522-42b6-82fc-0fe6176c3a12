# -*- coding: utf-8 -*-
"""
大纲可视化组件

提供大纲的图形化展示，包括思维导图、流程图等视图
"""

import logging
import math
from typing import List, Optional, Dict, Any, Tuple
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGraphicsView, QGraphicsScene,
    QGraphicsItem, QGraphicsRectItem, QGraphicsTextItem, QGraphicsLineItem,
    QGraphicsEllipseItem, QPushButton, QComboBox, QLabel, QSlider,
    QToolButton, QButtonGroup, QFrame, QSplitter, QScrollArea,
    QMessageBox, QMenu, QAction
)
from PyQt6.QtCore import Qt, pyqtSignal, QRectF, QPointF, QSizeF, QTimer
from PyQt6.QtGui import (
    QPen, QBrush, QColor, QPainter, QFont, QFontMetrics, QPainterPath,
    QLinearGradient, QRadialGradient, QPolygonF, QTransform, QPixmap,
    QIcon, QPalette
)

from ...models.outline import OutlineNode, OutlineNodeType, OutlineNodeStatus
from ...core.outline_manager import get_outline_manager

logger = logging.getLogger(__name__)


class OutlineNodeItem(QGraphicsRectItem):
    """大纲节点图形项"""
    
    def __init__(self, node: OutlineNode, parent=None):
        super().__init__(parent)
        self.node = node
        self.text_item = None
        self.setup_item()
        
    def setup_item(self):
        """设置图形项"""
        # 设置基本属性
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemIsSelectable, True)
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemSendsGeometryChanges, True)
        
        # 计算节点大小
        font = QFont("Arial", 10)
        metrics = QFontMetrics(font)
        text = self.node.title or "未命名"
        text_rect = metrics.boundingRect(text)
        
        # 设置节点尺寸
        width = max(120, text_rect.width() + 20)
        height = max(40, text_rect.height() + 20)
        self.setRect(0, 0, width, height)
        
        # 创建文本项
        self.text_item = QGraphicsTextItem(text, self)
        self.text_item.setFont(font)
        self.text_item.setPos(10, 10)
        
        # 设置样式
        self.update_appearance()
        
    def update_appearance(self):
        """更新外观"""
        # 根据节点类型设置颜色
        type_colors = {
            OutlineNodeType.PART: QColor(100, 150, 200),
            OutlineNodeType.CHAPTER: QColor(150, 200, 100),
            OutlineNodeType.SCENE: QColor(200, 150, 100),
            OutlineNodeType.SECTION: QColor(200, 100, 150)
        }
        
        base_color = type_colors.get(self.node.type, QColor(150, 150, 150))
        
        # 根据状态调整透明度
        status_alpha = {
            OutlineNodeStatus.PLANNING: 0.6,
            OutlineNodeStatus.WRITING: 0.8,
            OutlineNodeStatus.COMPLETED: 1.0,
            OutlineNodeStatus.REVIEWING: 0.9
        }
        
        alpha = status_alpha.get(self.node.status, 0.7)
        base_color.setAlphaF(alpha)
        
        # 设置画刷和画笔
        brush = QBrush(base_color)
        pen = QPen(base_color.darker(150), 2)
        
        self.setBrush(brush)
        self.setPen(pen)
        
        # 设置文本颜色
        if self.text_item:
            text_color = QColor(0, 0, 0) if base_color.lightness() > 128 else QColor(255, 255, 255)
            self.text_item.setDefaultTextColor(text_color)
            
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 发送选择信号
            scene = self.scene()
            if hasattr(scene, 'node_selected'):
                scene.node_selected.emit(self.node.id)
        super().mousePressEvent(event)
        
    def mouseDoubleClickEvent(self, event):
        """鼠标双击事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            # 发送编辑信号
            scene = self.scene()
            if hasattr(scene, 'node_edit_requested'):
                scene.node_edit_requested.emit(self.node.id)
        super().mouseDoubleClickEvent(event)


class OutlineConnectionItem(QGraphicsLineItem):
    """大纲连接线图形项"""
    
    def __init__(self, start_item: OutlineNodeItem, end_item: OutlineNodeItem, parent=None):
        super().__init__(parent)
        self.start_item = start_item
        self.end_item = end_item
        self.setup_connection()
        
    def setup_connection(self):
        """设置连接线"""
        # 计算连接点
        start_rect = self.start_item.rect()
        end_rect = self.end_item.rect()
        
        start_center = self.start_item.pos() + QPointF(start_rect.width() / 2, start_rect.height() / 2)
        end_center = self.end_item.pos() + QPointF(end_rect.width() / 2, end_rect.height() / 2)
        
        # 设置线条
        self.setLine(start_center.x(), start_center.y(), end_center.x(), end_center.y())
        
        # 设置样式
        pen = QPen(QColor(100, 100, 100), 2)
        pen.setStyle(Qt.PenStyle.SolidLine)
        self.setPen(pen)
        
    def update_position(self):
        """更新位置"""
        self.setup_connection()


class OutlineGraphicsScene(QGraphicsScene):
    """大纲图形场景"""
    
    node_selected = pyqtSignal(str)  # node_id
    node_edit_requested = pyqtSignal(str)  # node_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.node_items = {}  # node_id -> OutlineNodeItem
        self.connection_items = []  # List[OutlineConnectionItem]
        
    def add_node_item(self, node: OutlineNode, pos: QPointF = None) -> OutlineNodeItem:
        """添加节点项"""
        item = OutlineNodeItem(node)
        if pos:
            item.setPos(pos)
        self.addItem(item)
        self.node_items[node.id] = item
        return item
        
    def add_connection(self, parent_id: str, child_id: str):
        """添加连接"""
        if parent_id in self.node_items and child_id in self.node_items:
            parent_item = self.node_items[parent_id]
            child_item = self.node_items[child_id]
            connection = OutlineConnectionItem(parent_item, child_item)
            self.addItem(connection)
            self.connection_items.append(connection)
            
    def update_connections(self):
        """更新所有连接"""
        for connection in self.connection_items:
            connection.update_position()
            
    def clear_all(self):
        """清空所有项目"""
        self.clear()
        self.node_items.clear()
        self.connection_items.clear()


class OutlineVisualizer(QWidget):
    """大纲可视化组件"""
    
    node_selected = pyqtSignal(str)  # node_id
    node_edit_requested = pyqtSignal(str)  # node_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_project_id = None
        self.outline_manager = get_outline_manager()
        self.layout_mode = "tree"  # tree, mindmap, flowchart
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        # 布局模式选择
        toolbar_layout.addWidget(QLabel("布局:"))
        self.layout_combo = QComboBox()
        self.layout_combo.addItems(["树形布局", "思维导图", "流程图"])
        toolbar_layout.addWidget(self.layout_combo)
        
        toolbar_layout.addSeparator()
        
        # 缩放控制
        toolbar_layout.addWidget(QLabel("缩放:"))
        self.zoom_slider = QSlider(Qt.Orientation.Horizontal)
        self.zoom_slider.setRange(25, 200)
        self.zoom_slider.setValue(100)
        self.zoom_slider.setMaximumWidth(150)
        toolbar_layout.addWidget(self.zoom_slider)
        
        self.zoom_label = QLabel("100%")
        toolbar_layout.addWidget(self.zoom_label)
        
        toolbar_layout.addSeparator()
        
        # 视图控制
        self.fit_view_btn = QPushButton("适应窗口")
        toolbar_layout.addWidget(self.fit_view_btn)
        
        self.center_view_btn = QPushButton("居中显示")
        toolbar_layout.addWidget(self.center_view_btn)
        
        self.refresh_btn = QPushButton("刷新")
        toolbar_layout.addWidget(self.refresh_btn)
        
        toolbar_layout.addStretch()
        
        layout.addLayout(toolbar_layout)
        
        # 图形视图
        self.graphics_view = QGraphicsView()
        self.graphics_scene = OutlineGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)
        
        # 设置视图属性
        self.graphics_view.setDragMode(QGraphicsView.DragMode.RubberBandDrag)
        self.graphics_view.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.graphics_view.setViewportUpdateMode(QGraphicsView.ViewportUpdateMode.FullViewportUpdate)
        
        layout.addWidget(self.graphics_view)
        
    def setup_connections(self):
        """设置信号连接"""
        self.layout_combo.currentIndexChanged.connect(self.change_layout_mode)
        self.zoom_slider.valueChanged.connect(self.update_zoom)
        self.fit_view_btn.clicked.connect(self.fit_view)
        self.center_view_btn.clicked.connect(self.center_view)
        self.refresh_btn.clicked.connect(self.refresh_view)
        
        self.graphics_scene.node_selected.connect(self.node_selected)
        self.graphics_scene.node_edit_requested.connect(self.node_edit_requested)
        
    def set_project(self, project_id: str):
        """设置当前项目"""
        self.current_project_id = project_id
        self.outline_manager.set_project(project_id)
        self.load_outline_data()
        
    def load_outline_data(self):
        """加载大纲数据"""
        if not self.current_project_id:
            return
            
        try:
            # 清空现有内容
            self.graphics_scene.clear_all()
            
            # 获取所有节点
            nodes = self.outline_manager.get_project_nodes(self.current_project_id)
            
            if not nodes:
                return
                
            # 根据布局模式排列节点
            self.arrange_nodes(nodes)
            
        except Exception as e:
            logger.error(f"加载大纲数据失败: {e}")
            QMessageBox.warning(self, "错误", f"加载大纲数据失败: {e}")
            
    def arrange_nodes(self, nodes: List[OutlineNode]):
        """排列节点"""
        if self.layout_mode == "tree":
            self.arrange_tree_layout(nodes)
        elif self.layout_mode == "mindmap":
            self.arrange_mindmap_layout(nodes)
        elif self.layout_mode == "flowchart":
            self.arrange_flowchart_layout(nodes)
            
    def arrange_tree_layout(self, nodes: List[OutlineNode]):
        """树形布局"""
        # 构建节点层次结构
        node_dict = {node.id: node for node in nodes}
        root_nodes = [node for node in nodes if not node.parent_id]
        
        # 计算布局参数
        level_height = 100
        node_spacing = 150
        
        # 递归布局节点
        def layout_node(node: OutlineNode, x: float, y: float, level: int) -> float:
            # 添加节点项
            pos = QPointF(x, y)
            item = self.graphics_scene.add_node_item(node, pos)
            
            # 布局子节点
            children = [child for child in nodes if child.parent_id == node.id]
            children.sort(key=lambda n: n.order_index or 0)
            
            child_x = x
            for child in children:
                child_y = y + level_height
                child_x = layout_node(child, child_x, child_y, level + 1)
                
                # 添加连接线
                self.graphics_scene.add_connection(node.id, child.id)
                
                child_x += node_spacing
                
            return max(x + node_spacing, child_x)
            
        # 布局根节点
        current_x = 0
        for root in root_nodes:
            current_x = layout_node(root, current_x, 0, 0)
            current_x += node_spacing * 2
            
    def arrange_mindmap_layout(self, nodes: List[OutlineNode]):
        """思维导图布局"""
        if not nodes:
            return
            
        # 找到根节点（通常是第一个或没有父节点的）
        root_nodes = [node for node in nodes if not node.parent_id]
        if not root_nodes:
            root_nodes = [nodes[0]]  # 如果没有明确的根节点，使用第一个
            
        root_node = root_nodes[0]
        node_dict = {node.id: node for node in nodes}
        
        # 中心位置
        center = QPointF(0, 0)
        
        # 添加根节点
        root_item = self.graphics_scene.add_node_item(root_node, center)
        
        # 递归布局子节点
        def layout_children(parent_node: OutlineNode, parent_pos: QPointF, level: int, angle_start: float, angle_range: float):
            children = [child for child in nodes if child.parent_id == parent_node.id]
            if not children:
                return
                
            children.sort(key=lambda n: n.order_index or 0)
            
            # 计算每个子节点的角度
            angle_step = angle_range / len(children) if len(children) > 1 else 0
            radius = 150 + level * 50  # 根据层级调整半径
            
            for i, child in enumerate(children):
                angle = angle_start + i * angle_step - angle_range / 2
                
                # 计算子节点位置
                child_x = parent_pos.x() + radius * math.cos(math.radians(angle))
                child_y = parent_pos.y() + radius * math.sin(math.radians(angle))
                child_pos = QPointF(child_x, child_y)
                
                # 添加子节点
                child_item = self.graphics_scene.add_node_item(child, child_pos)
                
                # 添加连接线
                self.graphics_scene.add_connection(parent_node.id, child.id)
                
                # 递归布局子节点的子节点
                child_angle_range = angle_range / len(children) if len(children) > 0 else 60
                layout_children(child, child_pos, level + 1, angle, child_angle_range)
                
        # 开始布局
        layout_children(root_node, center, 1, 0, 360)
        
    def arrange_flowchart_layout(self, nodes: List[OutlineNode]):
        """流程图布局"""
        # 按层级和顺序排列
        levels = {}
        for node in nodes:
            level = node.level or 0
            if level not in levels:
                levels[level] = []
            levels[level].append(node)
            
        # 排序每个层级的节点
        for level in levels:
            levels[level].sort(key=lambda n: n.order_index or 0)
            
        # 布局参数
        level_height = 120
        node_spacing = 180
        
        # 布局节点
        for level, level_nodes in levels.items():
            y = level * level_height
            total_width = len(level_nodes) * node_spacing
            start_x = -total_width / 2
            
            for i, node in enumerate(level_nodes):
                x = start_x + i * node_spacing
                pos = QPointF(x, y)
                item = self.graphics_scene.add_node_item(node, pos)
                
        # 添加连接线
        for node in nodes:
            if node.parent_id:
                self.graphics_scene.add_connection(node.parent_id, node.id)
                
    def change_layout_mode(self, index: int):
        """改变布局模式"""
        modes = ["tree", "mindmap", "flowchart"]
        self.layout_mode = modes[index]
        self.load_outline_data()
        
    def update_zoom(self, value: int):
        """更新缩放"""
        scale = value / 100.0
        transform = QTransform()
        transform.scale(scale, scale)
        self.graphics_view.setTransform(transform)
        
        self.zoom_label.setText(f"{value}%")
        
    def fit_view(self):
        """适应窗口"""
        self.graphics_view.fitInView(self.graphics_scene.itemsBoundingRect(), Qt.AspectRatioMode.KeepAspectRatio)
        
        # 更新缩放滑块
        transform = self.graphics_view.transform()
        scale = transform.m11()  # 获取X轴缩放比例
        zoom_value = int(scale * 100)
        self.zoom_slider.setValue(zoom_value)
        
    def center_view(self):
        """居中显示"""
        self.graphics_view.centerOn(self.graphics_scene.itemsBoundingRect().center())
        
    def refresh_view(self):
        """刷新视图"""
        self.load_outline_data()
        
    def export_image(self, file_path: str):
        """导出为图片"""
        try:
            # 获取场景边界
            rect = self.graphics_scene.itemsBoundingRect()
            
            # 创建图片
            pixmap = QPixmap(rect.size().toSize())
            pixmap.fill(Qt.GlobalColor.white)
            
            # 渲染场景到图片
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            self.graphics_scene.render(painter, QRectF(), rect)
            painter.end()
            
            # 保存图片
            pixmap.save(file_path)
            
            return True
            
        except Exception as e:
            logger.error(f"导出图片失败: {e}")
            return False
            
    def get_selected_node_id(self) -> Optional[str]:
        """获取选中的节点ID"""
        selected_items = self.graphics_scene.selectedItems()
        for item in selected_items:
            if isinstance(item, OutlineNodeItem):
                return item.node.id
        return None
        
    def select_node(self, node_id: str):
        """选中指定节点"""
        if node_id in self.graphics_scene.node_items:
            item = self.graphics_scene.node_items[node_id]
            self.graphics_scene.clearSelection()
            item.setSelected(True)
            self.graphics_view.centerOn(item)