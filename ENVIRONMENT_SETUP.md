# 笔落App Python版本 - 环境依赖文档

## 系统要求

### 最低系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Python版本**: Python 3.9 或更高版本
- **内存**: 最少 4GB RAM（推荐 8GB+）
- **存储空间**: 至少 2GB 可用空间
- **显卡**: 支持OpenGL 2.0或更高版本

### 推荐系统配置
- **操作系统**: Windows 11, macOS 12+, Ubuntu 20.04+
- **Python版本**: Python 3.11+
- **内存**: 16GB RAM
- **存储空间**: 5GB+ 可用空间（SSD推荐）
- **显卡**: 独立显卡（提升界面渲染性能）

## Python环境设置

### 1. Python安装

#### Windows
```bash
# 从官网下载Python安装包
https://www.python.org/downloads/windows/

# 或使用包管理器
winget install Python.Python.3.11
```

#### macOS
```bash
# 使用Homebrew安装
brew install python@3.11

# 或从官网下载
https://www.python.org/downloads/macos/
```

#### Ubuntu/Debian
```bash
# 更新包列表
sudo apt update

# 安装Python和pip
sudo apt install python3.11 python3.11-pip python3.11-venv

# 设置默认Python版本
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1
```

### 2. 虚拟环境创建

```bash
# 创建虚拟环境
python -m venv bamboofall_env

# 激活虚拟环境
# Windows:
bamboofall_env\Scripts\activate

# macOS/Linux:
source bamboofall_env/bin/activate
```

### 3. 依赖安装

#### 基础安装
```bash
# 升级pip
python -m pip install --upgrade pip

# 安装生产依赖
pip install -r requirements.txt

# 安装开发依赖（可选）
pip install -r requirements-dev.txt
```

#### 可编辑安装
```bash
# 可编辑模式安装项目
pip install -e .

# 或安装开发版本
pip install -e .[dev]
```

## 核心依赖说明

### GUI框架
- **PyQt6** (6.6.0+): 现代化GUI框架
- **qdarktheme** (2.1.0+): 主题系统支持
- **qtawesome** (1.3.0+): 图标库

### 数据库和ORM
- **SQLAlchemy** (2.0.0+): ORM框架
- **alembic** (1.13.0+): 数据库迁移工具

### AI服务集成
- **openai** (1.0.0+): OpenAI API客户端
- **anthropic** (0.8.0+): Claude API客户端
- **aiohttp** (3.9.0+): 异步HTTP客户端

### 数据处理
- **pydantic** (2.5.0+): 数据验证
- **matplotlib** (3.8.0+): 可视化图表
- **plotly** (5.17.0+): 交互式图表

### 文档导出
- **reportlab** (4.0.0+): PDF生成
- **python-docx** (1.1.0+): Word文档
- **ebooklib** (0.18+): EPUB电子书

## 系统特定依赖

### Windows
```bash
# Windows特定依赖
pip install pywin32
```

### macOS
```bash
# macOS特定依赖
pip install pyobjc-framework-Cocoa
```

### Linux
```bash
# Linux系统依赖
sudo apt install python3-dev python3-tk

# 字体依赖
sudo apt install fonts-noto-cjk
```

## 可选依赖

### 本地AI模型支持
```bash
# Ollama集成
pip install ollama

# 本地NLP模型
pip install transformers torch
```

### 高级文本处理
```bash
# NLP库
pip install spacy nltk

# 下载语言模型
python -m spacy download zh_core_web_sm
python -c "import nltk; nltk.download('punkt')"
```

### 性能优化
```bash
# 性能分析工具
pip install memory-profiler line-profiler

# 加速库
pip install numba
```

## 开发环境配置

### 代码质量工具
```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 设置pre-commit
pre-commit install

# 运行代码检查
black .
flake8 .
mypy .
```

### IDE配置

#### PyCharm
1. 打开项目目录
2. 配置Python解释器指向虚拟环境
3. 安装PyQt6插件
4. 配置代码风格为Black

#### VS Code
```json
// .vscode/settings.json
{
    "python.defaultInterpreterPath": "./bamboofall_env/bin/python",
    "python.formatting.provider": "black",
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true
}
```

## 环境变量配置

### 必需环境变量
```bash
# AI API密钥（可选，也可在应用内设置）
export OPENAI_API_KEY="your-openai-api-key"
export ANTHROPIC_API_KEY="your-anthropic-api-key"
export DEEPSEEK_API_KEY="your-deepseek-api-key"
```

### 可选环境变量
```bash
# 日志级别
export BAMBOOFALL_LOG_LEVEL="INFO"

# 数据目录
export BAMBOOFALL_DATA_DIR="/path/to/data"

# 调试模式
export BAMBOOFALL_DEBUG="false"
```

## 权限配置

### Windows
- 确保Python有写入权限到用户目录
- 如果使用Windows Defender，添加项目目录到排除列表

### macOS
- 可能需要授权应用访问文件系统
- 在"系统偏好设置 > 安全性与隐私"中允许应用运行

### Linux
- 确保用户有权限访问字体目录
- 检查X11转发设置（如果使用SSH）

## 故障排除

### 常见问题

#### 1. PyQt6安装失败
```bash
# 清理pip缓存
pip cache purge

# 升级pip和setuptools
pip install --upgrade pip setuptools

# 重新安装PyQt6
pip install --no-cache-dir PyQt6
```

#### 2. 字体显示问题
```bash
# Linux安装中文字体
sudo apt install fonts-noto-cjk fonts-wqy-zenhei

# macOS安装字体
brew install --cask font-source-han-sans
```

#### 3. 数据库权限问题
```bash
# 检查数据目录权限
ls -la ~/.bamboofall/

# 修复权限
chmod 755 ~/.bamboofall/
chmod 644 ~/.bamboofall/*.db
```

#### 4. AI API连接问题
- 检查网络连接
- 验证API密钥有效性
- 确认API配额未超限

### 日志查看
```bash
# 应用日志位置
# Windows: %APPDATA%\BambooFall\logs\
# macOS: ~/Library/Application Support/BambooFall/logs/
# Linux: ~/.bamboofall/logs/

# 查看最新日志
tail -f ~/.bamboofall/logs/bamboofall.log
```

## 性能优化建议

### 系统优化
1. 使用SSD存储提升启动速度
2. 确保足够的可用内存
3. 关闭不必要的后台程序

### 应用优化
1. 定期清理日志文件
2. 优化AI API调用频率
3. 使用本地缓存减少网络请求

## 更新和维护

### 依赖更新
```bash
# 检查过期包
pip list --outdated

# 更新特定包
pip install --upgrade package-name

# 更新所有包（谨慎使用）
pip freeze | cut -d'=' -f1 | xargs pip install --upgrade
```

### 环境备份
```bash
# 导出当前环境
pip freeze > requirements-backup.txt

# 创建环境快照
pip list --format=freeze > environment-snapshot.txt
```

---

*此文档会随着项目发展持续更新，建议定期查看最新版本。*