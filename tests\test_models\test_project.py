# -*- coding: utf-8 -*-
"""
项目模型测试
"""

import pytest
from datetime import datetime
from bamboofall.models.project import Project


class TestProject:
    """Project模型测试类"""
    
    def test_init_with_all_fields(self):
        """测试使用所有字段初始化"""
        project = Project(
            id="test-project-1",
            name="测试项目",
            description="这是一个测试项目",
            genre="科幻",
            target_words=100000,
            current_words=25000,
            status="active",
            created_at="2024-01-01T00:00:00",
            updated_at="2024-01-02T00:00:00",
            author="作者名",
            tags=["科幻", "未来"],
            settings={"theme": "dark", "auto_save": True}
        )
        
        assert project.id == "test-project-1"
        assert project.name == "测试项目"
        assert project.description == "这是一个测试项目"
        assert project.genre == "科幻"
        assert project.target_words == 100000
        assert project.current_words == 25000
        assert project.status == "active"
        assert project.created_at == "2024-01-01T00:00:00"
        assert project.updated_at == "2024-01-02T00:00:00"
        assert project.author == "作者名"
        assert project.tags == ["科幻", "未来"]
        assert project.settings == {"theme": "dark", "auto_save": True}
    
    def test_init_with_minimal_fields(self):
        """测试使用最少字段初始化"""
        project = Project(
            id="minimal-project",
            name="最小项目"
        )
        
        assert project.id == "minimal-project"
        assert project.name == "最小项目"
        assert project.description == ""
        assert project.genre == ""
        assert project.target_words == 0
        assert project.current_words == 0
        assert project.status == "active"
        assert project.created_at is not None
        assert project.updated_at is not None
        assert project.author == ""
        assert project.tags == []
        assert project.settings == {}
    
    def test_auto_generated_timestamps(self):
        """测试自动生成的时间戳"""
        project = Project(id="timestamp-test", name="时间戳测试")
        
        # 验证时间戳格式
        assert isinstance(project.created_at, str)
        assert isinstance(project.updated_at, str)
        
        # 验证时间戳可以解析
        created_dt = datetime.fromisoformat(project.created_at.replace('Z', '+00:00'))
        updated_dt = datetime.fromisoformat(project.updated_at.replace('Z', '+00:00'))
        
        assert isinstance(created_dt, datetime)
        assert isinstance(updated_dt, datetime)
    
    def test_to_dict(self):
        """测试转换为字典"""
        project = Project(
            id="dict-test",
            name="字典测试",
            description="测试描述",
            genre="奇幻",
            target_words=80000,
            current_words=15000,
            tags=["奇幻", "冒险"]
        )
        
        project_dict = project.to_dict()
        
        assert isinstance(project_dict, dict)
        assert project_dict["id"] == "dict-test"
        assert project_dict["name"] == "字典测试"
        assert project_dict["description"] == "测试描述"
        assert project_dict["genre"] == "奇幻"
        assert project_dict["target_words"] == 80000
        assert project_dict["current_words"] == 15000
        assert project_dict["tags"] == ["奇幻", "冒险"]
        assert "created_at" in project_dict
        assert "updated_at" in project_dict
    
    def test_from_dict(self):
        """测试从字典创建"""
        project_data = {
            "id": "from-dict-test",
            "name": "从字典创建",
            "description": "从字典数据创建的项目",
            "genre": "悬疑",
            "target_words": 120000,
            "current_words": 30000,
            "status": "draft",
            "author": "测试作者",
            "tags": ["悬疑", "推理"],
            "settings": {"auto_backup": True}
        }
        
        project = Project.from_dict(project_data)
        
        assert project.id == "from-dict-test"
        assert project.name == "从字典创建"
        assert project.description == "从字典数据创建的项目"
        assert project.genre == "悬疑"
        assert project.target_words == 120000
        assert project.current_words == 30000
        assert project.status == "draft"
        assert project.author == "测试作者"
        assert project.tags == ["悬疑", "推理"]
        assert project.settings == {"auto_backup": True}
    
    def test_from_dict_missing_fields(self):
        """测试从不完整字典创建"""
        project_data = {
            "id": "incomplete-test",
            "name": "不完整项目"
        }
        
        project = Project.from_dict(project_data)
        
        assert project.id == "incomplete-test"
        assert project.name == "不完整项目"
        assert project.description == ""
        assert project.target_words == 0
        assert project.tags == []
    
    def test_update_progress(self):
        """测试更新进度"""
        project = Project(
            id="progress-test",
            name="进度测试",
            target_words=100000,
            current_words=25000
        )
        
        # 更新字数
        project.update_progress(35000)
        
        assert project.current_words == 35000
        
        # 验证进度计算
        progress = project.get_progress()
        assert progress == 0.35  # 35000 / 100000
    
    def test_get_progress(self):
        """测试获取进度"""
        # 正常进度
        project1 = Project(
            id="progress1",
            name="进度测试1",
            target_words=100000,
            current_words=25000
        )
        assert project1.get_progress() == 0.25
        
        # 超过目标
        project2 = Project(
            id="progress2",
            name="进度测试2",
            target_words=100000,
            current_words=120000
        )
        assert project2.get_progress() == 1.2
        
        # 目标为0
        project3 = Project(
            id="progress3",
            name="进度测试3",
            target_words=0,
            current_words=1000
        )
        assert project3.get_progress() == 0.0
    
    def test_is_completed(self):
        """测试是否完成"""
        # 未完成
        project1 = Project(
            id="incomplete",
            name="未完成项目",
            target_words=100000,
            current_words=50000
        )
        assert project1.is_completed() is False
        
        # 已完成
        project2 = Project(
            id="complete",
            name="已完成项目",
            target_words=100000,
            current_words=100000
        )
        assert project2.is_completed() is True
        
        # 超额完成
        project3 = Project(
            id="over-complete",
            name="超额完成项目",
            target_words=100000,
            current_words=120000
        )
        assert project3.is_completed() is True
    
    def test_add_tag(self):
        """测试添加标签"""
        project = Project(id="tag-test", name="标签测试")
        
        # 添加新标签
        project.add_tag("科幻")
        assert "科幻" in project.tags
        
        # 添加重复标签
        project.add_tag("科幻")
        assert project.tags.count("科幻") == 1
        
        # 添加多个标签
        project.add_tag("未来")
        project.add_tag("机器人")
        assert len(project.tags) == 3
        assert "未来" in project.tags
        assert "机器人" in project.tags
    
    def test_remove_tag(self):
        """测试移除标签"""
        project = Project(
            id="remove-tag-test",
            name="移除标签测试",
            tags=["科幻", "未来", "机器人"]
        )
        
        # 移除存在的标签
        project.remove_tag("未来")
        assert "未来" not in project.tags
        assert len(project.tags) == 2
        
        # 移除不存在的标签
        project.remove_tag("不存在")
        assert len(project.tags) == 2
    
    def test_update_setting(self):
        """测试更新设置"""
        project = Project(
            id="setting-test",
            name="设置测试",
            settings={"theme": "light", "auto_save": False}
        )
        
        # 更新现有设置
        project.update_setting("theme", "dark")
        assert project.settings["theme"] == "dark"
        
        # 添加新设置
        project.update_setting("font_size", 14)
        assert project.settings["font_size"] == 14
        
        # 验证其他设置未受影响
        assert project.settings["auto_save"] is False
    
    def test_get_setting(self):
        """测试获取设置"""
        project = Project(
            id="get-setting-test",
            name="获取设置测试",
            settings={"theme": "dark", "auto_save": True}
        )
        
        # 获取存在的设置
        assert project.get_setting("theme") == "dark"
        assert project.get_setting("auto_save") is True
        
        # 获取不存在的设置（使用默认值）
        assert project.get_setting("font_size", 12) == 12
        assert project.get_setting("non_existent") is None
    
    def test_validate(self):
        """测试验证"""
        # 有效项目
        valid_project = Project(
            id="valid-project",
            name="有效项目",
            target_words=100000
        )
        assert valid_project.validate() is True
        
        # 无效项目（空名称）
        invalid_project1 = Project(
            id="invalid1",
            name=""
        )
        assert invalid_project1.validate() is False
        
        # 无效项目（负目标字数）
        invalid_project2 = Project(
            id="invalid2",
            name="无效项目2",
            target_words=-1000
        )
        assert invalid_project2.validate() is False
    
    def test_equality(self):
        """测试相等性比较"""
        project1 = Project(
            id="equal-test",
            name="相等测试",
            description="测试描述"
        )
        
        project2 = Project(
            id="equal-test",
            name="相等测试",
            description="测试描述"
        )
        
        project3 = Project(
            id="different-test",
            name="不同测试"
        )
        
        # 相同ID的项目应该相等
        assert project1 == project2
        
        # 不同ID的项目不相等
        assert project1 != project3
    
    def test_string_representation(self):
        """测试字符串表示"""
        project = Project(
            id="str-test",
            name="字符串测试",
            genre="科幻"
        )
        
        str_repr = str(project)
        assert "字符串测试" in str_repr
        assert "str-test" in str_repr
    
    def test_copy(self):
        """测试复制项目"""
        original = Project(
            id="original",
            name="原始项目",
            description="原始描述",
            tags=["标签1", "标签2"],
            settings={"setting1": "value1"}
        )
        
        # 复制项目
        copied = original.copy(new_id="copied", new_name="复制项目")
        
        assert copied.id == "copied"
        assert copied.name == "复制项目"
        assert copied.description == "原始描述"
        assert copied.tags == ["标签1", "标签2"]
        assert copied.settings == {"setting1": "value1"}
        
        # 验证是独立的副本
        copied.add_tag("新标签")
        assert "新标签" not in original.tags
    
    def test_export_data(self):
        """测试导出数据"""
        project = Project(
            id="export-test",
            name="导出测试",
            description="导出描述",
            genre="奇幻",
            target_words=100000,
            current_words=25000,
            tags=["奇幻", "冒险"],
            settings={"theme": "dark"}
        )
        
        exported_data = project.export_data()
        
        # 验证导出的数据包含所有必要字段
        assert exported_data["id"] == "export-test"
        assert exported_data["name"] == "导出测试"
        assert exported_data["description"] == "导出描述"
        assert exported_data["genre"] == "奇幻"
        assert exported_data["target_words"] == 100000
        assert exported_data["current_words"] == 25000
        assert exported_data["tags"] == ["奇幻", "冒险"]
        assert exported_data["settings"] == {"theme": "dark"}
        assert "created_at" in exported_data
        assert "updated_at" in exported_data
    
    def test_import_data(self):
        """测试导入数据"""
        import_data = {
            "id": "import-test",
            "name": "导入测试",
            "description": "导入描述",
            "genre": "悬疑",
            "target_words": 80000,
            "current_words": 20000,
            "tags": ["悬疑", "推理"],
            "settings": {"auto_save": True},
            "created_at": "2024-01-01T00:00:00",
            "updated_at": "2024-01-02T00:00:00"
        }
        
        project = Project.import_data(import_data)
        
        assert project.id == "import-test"
        assert project.name == "导入测试"
        assert project.description == "导入描述"
        assert project.genre == "悬疑"
        assert project.target_words == 80000
        assert project.current_words == 20000
        assert project.tags == ["悬疑", "推理"]
        assert project.settings == {"auto_save": True}
        assert project.created_at == "2024-01-01T00:00:00"
        assert project.updated_at == "2024-01-02T00:00:00"