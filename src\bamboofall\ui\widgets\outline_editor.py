# -*- coding: utf-8 -*-
"""
高级大纲编辑器组件

提供可视化的大纲编辑功能，包括树形结构编辑、拖拽排序、节点操作等
"""

import logging
from typing import List, Optional, Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTreeWidget, QTreeWidgetItem,
    QPushButton, QToolButton, QMenu, QAction, QSplitter, QTextEdit,
    QLineEdit, QComboBox, QSpinBox, QLabel, QFrame, QGroupBox,
    QFormLayout, QCheckBox, QProgressBar, QTabWidget, QScrollArea,
    QMessageBox, QInputDialog, QDialog, QDialogButtonBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QMimeData, QPoint
from PyQt6.QtGui import QIcon, QFont, QColor, QPalette, QDrag, QPixmap, QPainter

from ...models.outline import OutlineNode, OutlineNodeType, OutlineNodeStatus
from ...core.outline_manager import get_outline_manager
from ...exceptions.exceptions import ValidationError
from ..dialogs.node_edit_dialog import NodeEditDialog

logger = logging.getLogger(__name__)


class OutlineTreeWidget(QTreeWidget):
    """支持拖拽的大纲树组件"""
    
    node_moved = pyqtSignal(str, str, int)  # node_id, new_parent_id, new_index
    node_selected = pyqtSignal(str)  # node_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_drag_drop()
        
    def setup_ui(self):
        """设置UI"""
        self.setHeaderLabels(["标题", "类型", "状态", "字数", "进度"])
        self.setDragDropMode(QTreeWidget.DragDropMode.InternalMove)
        self.setSelectionMode(QTreeWidget.SelectionMode.SingleSelection)
        self.setAlternatingRowColors(True)
        self.setRootIsDecorated(True)
        self.setExpandsOnDoubleClick(False)
        
        # 设置列宽
        self.setColumnWidth(0, 200)
        self.setColumnWidth(1, 80)
        self.setColumnWidth(2, 80)
        self.setColumnWidth(3, 60)
        self.setColumnWidth(4, 80)
        
    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.setAcceptDrops(True)
        self.setDragEnabled(True)
        self.setDefaultDropAction(Qt.DropAction.MoveAction)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            item = self.itemAt(event.pos())
            if item:
                node_id = item.data(0, Qt.ItemDataRole.UserRole)
                if node_id:
                    self.node_selected.emit(node_id)
        super().mousePressEvent(event)
        
    def dropEvent(self, event):
        """拖拽放置事件"""
        source_item = self.currentItem()
        if not source_item:
            return
            
        target_item = self.itemAt(event.pos())
        if not target_item:
            return
            
        source_node_id = source_item.data(0, Qt.ItemDataRole.UserRole)
        target_node_id = target_item.data(0, Qt.ItemDataRole.UserRole)
        
        if source_node_id and target_node_id and source_node_id != target_node_id:
            # 计算新的索引位置
            new_index = target_item.parent().indexOfChild(target_item) if target_item.parent() else self.indexOfTopLevelItem(target_item)
            self.node_moved.emit(source_node_id, target_node_id, new_index)
            
        super().dropEvent(event)


class NodeDetailWidget(QWidget):
    """节点详情编辑组件"""
    
    node_updated = pyqtSignal(str)  # node_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_node = None
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)
        
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("节点标题")
        basic_layout.addRow("标题:", self.title_edit)
        
        self.type_combo = QComboBox()
        for node_type in OutlineNodeType:
            self.type_combo.addItem(node_type.value, node_type)
        basic_layout.addRow("类型:", self.type_combo)
        
        self.status_combo = QComboBox()
        for status in OutlineNodeStatus:
            self.status_combo.addItem(status.value, status)
        basic_layout.addRow("状态:", self.status_combo)
        
        layout.addWidget(basic_group)
        
        # 内容编辑
        content_group = QGroupBox("内容")
        content_layout = QVBoxLayout(content_group)
        
        self.content_edit = QTextEdit()
        self.content_edit.setPlaceholderText("节点内容描述...")
        content_layout.addWidget(self.content_edit)
        
        layout.addWidget(content_group)
        
        # 统计信息组
        stats_group = QGroupBox("统计信息")
        stats_layout = QFormLayout(stats_group)
        
        self.estimated_words_spin = QSpinBox()
        self.estimated_words_spin.setRange(0, 999999)
        self.estimated_words_spin.setSuffix(" 字")
        stats_layout.addRow("预计字数:", self.estimated_words_spin)
        
        self.actual_words_spin = QSpinBox()
        self.actual_words_spin.setRange(0, 999999)
        self.actual_words_spin.setSuffix(" 字")
        stats_layout.addRow("实际字数:", self.actual_words_spin)
        
        self.progress_bar = QProgressBar()
        stats_layout.addRow("完成进度:", self.progress_bar)
        
        layout.addWidget(stats_group)
        
        # 关联信息组
        relation_group = QGroupBox("关联信息")
        relation_layout = QFormLayout(relation_group)
        
        self.chapter_edit = QLineEdit()
        self.chapter_edit.setPlaceholderText("关联章节ID")
        relation_layout.addRow("关联章节:", self.chapter_edit)
        
        self.characters_edit = QLineEdit()
        self.characters_edit.setPlaceholderText("角色ID列表，用逗号分隔")
        relation_layout.addRow("相关角色:", self.characters_edit)
        
        layout.addWidget(relation_group)
        
        # 备注
        notes_group = QGroupBox("备注")
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("备注信息...")
        self.notes_edit.setMaximumHeight(100)
        notes_layout.addWidget(self.notes_edit)
        
        layout.addWidget(notes_group)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("保存")
        self.save_btn.setEnabled(False)
        button_layout.addWidget(self.save_btn)
        
        self.reset_btn = QPushButton("重置")
        self.reset_btn.setEnabled(False)
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
    def setup_connections(self):
        """设置信号连接"""
        self.title_edit.textChanged.connect(self.on_content_changed)
        self.type_combo.currentTextChanged.connect(self.on_content_changed)
        self.status_combo.currentTextChanged.connect(self.on_content_changed)
        self.content_edit.textChanged.connect(self.on_content_changed)
        self.estimated_words_spin.valueChanged.connect(self.on_content_changed)
        self.actual_words_spin.valueChanged.connect(self.on_content_changed)
        self.chapter_edit.textChanged.connect(self.on_content_changed)
        self.characters_edit.textChanged.connect(self.on_content_changed)
        self.notes_edit.textChanged.connect(self.on_content_changed)
        
        self.save_btn.clicked.connect(self.save_node)
        self.reset_btn.clicked.connect(self.reset_form)
        
    def load_node(self, node: OutlineNode):
        """加载节点数据"""
        self.current_node = node
        
        # 阻止信号触发
        self.blockSignals(True)
        
        self.title_edit.setText(node.title or "")
        
        # 设置类型
        type_index = self.type_combo.findData(node.type)
        if type_index >= 0:
            self.type_combo.setCurrentIndex(type_index)
            
        # 设置状态
        status_index = self.status_combo.findData(node.status)
        if status_index >= 0:
            self.status_combo.setCurrentIndex(status_index)
            
        self.content_edit.setPlainText(node.content or "")
        self.estimated_words_spin.setValue(node.estimated_word_count or 0)
        self.actual_words_spin.setValue(node.actual_word_count or 0)
        
        # 更新进度条
        progress = node.get_progress()
        self.progress_bar.setValue(int(progress * 100))
        
        self.chapter_edit.setText(node.chapter_id or "")
        self.characters_edit.setText(",".join(node.character_ids) if node.character_ids else "")
        self.notes_edit.setPlainText(node.notes or "")
        
        # 恢复信号
        self.blockSignals(False)
        
        # 重置按钮状态
        self.save_btn.setEnabled(False)
        self.reset_btn.setEnabled(False)
        
    def on_content_changed(self):
        """内容变化处理"""
        if self.current_node:
            self.save_btn.setEnabled(True)
            self.reset_btn.setEnabled(True)
            
    def save_node(self):
        """保存节点"""
        if not self.current_node:
            return
            
        try:
            # 更新节点数据
            self.current_node.title = self.title_edit.text().strip()
            self.current_node.type = self.type_combo.currentData()
            self.current_node.status = self.status_combo.currentData()
            self.current_node.content = self.content_edit.toPlainText().strip()
            self.current_node.estimated_word_count = self.estimated_words_spin.value()
            self.current_node.actual_word_count = self.actual_words_spin.value()
            self.current_node.chapter_id = self.chapter_edit.text().strip() or None
            
            # 处理角色ID列表
            characters_text = self.characters_edit.text().strip()
            if characters_text:
                self.current_node.character_ids = [id.strip() for id in characters_text.split(",") if id.strip()]
            else:
                self.current_node.character_ids = []
                
            self.current_node.notes = self.notes_edit.toPlainText().strip()
            
            # 更新时间戳
            self.current_node.update_timestamp()
            
            # 保存到数据库
            outline_manager = get_outline_manager()
            outline_manager.update_node(self.current_node)
            
            # 发送更新信号
            self.node_updated.emit(self.current_node.id)
            
            # 重置按钮状态
            self.save_btn.setEnabled(False)
            self.reset_btn.setEnabled(False)
            
            QMessageBox.information(self, "成功", "节点已保存")
            
        except Exception as e:
            logger.error(f"保存节点失败: {e}")
            QMessageBox.warning(self, "错误", f"保存失败: {e}")
            
    def reset_form(self):
        """重置表单"""
        if self.current_node:
            self.load_node(self.current_node)


class OutlineEditor(QWidget):
    """高级大纲编辑器主组件"""
    
    outline_changed = pyqtSignal()
    node_selected = pyqtSignal(str)  # node_id
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_project_id = None
        self.current_outline_id = None
        self.outline_manager = get_outline_manager()
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        
        self.add_node_btn = QPushButton("添加节点")
        self.add_node_btn.setIcon(QIcon(":/icons/add.png"))
        toolbar_layout.addWidget(self.add_node_btn)
        
        self.edit_node_btn = QPushButton("编辑节点")
        self.edit_node_btn.setIcon(QIcon(":/icons/edit.png"))
        self.edit_node_btn.setEnabled(False)
        toolbar_layout.addWidget(self.edit_node_btn)
        
        self.delete_node_btn = QPushButton("删除节点")
        self.delete_node_btn.setIcon(QIcon(":/icons/delete.png"))
        self.delete_node_btn.setEnabled(False)
        toolbar_layout.addWidget(self.delete_node_btn)
        
        toolbar_layout.addSeparator()
        
        self.expand_all_btn = QPushButton("展开全部")
        toolbar_layout.addWidget(self.expand_all_btn)
        
        self.collapse_all_btn = QPushButton("折叠全部")
        toolbar_layout.addWidget(self.collapse_all_btn)
        
        toolbar_layout.addStretch()
        
        # 搜索框
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索节点...")
        self.search_edit.setMaximumWidth(200)
        toolbar_layout.addWidget(QLabel("搜索:"))
        toolbar_layout.addWidget(self.search_edit)
        
        layout.addLayout(toolbar_layout)
        
        # 主要内容区域
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：大纲树
        self.outline_tree = OutlineTreeWidget()
        splitter.addWidget(self.outline_tree)
        
        # 右侧：节点详情
        self.node_detail = NodeDetailWidget()
        splitter.addWidget(self.node_detail)
        
        # 设置分割比例
        splitter.setSizes([400, 300])
        
        layout.addWidget(splitter)
        
    def setup_connections(self):
        """设置信号连接"""
        self.add_node_btn.clicked.connect(self.add_node)
        self.edit_node_btn.clicked.connect(self.edit_node)
        self.delete_node_btn.clicked.connect(self.delete_node)
        self.expand_all_btn.clicked.connect(self.outline_tree.expandAll)
        self.collapse_all_btn.clicked.connect(self.outline_tree.collapseAll)
        
        self.search_edit.textChanged.connect(self.search_nodes)
        
        self.outline_tree.node_selected.connect(self.on_node_selected)
        self.outline_tree.node_moved.connect(self.on_node_moved)
        self.outline_tree.itemSelectionChanged.connect(self.on_selection_changed)
        
        self.node_detail.node_updated.connect(self.on_node_updated)
        
    def set_project(self, project_id: str):
        """设置当前项目"""
        self.current_project_id = project_id
        self.outline_manager.set_project(project_id)
        self.load_outline_tree()
        
    def set_outline(self, outline_id: str):
        """设置当前大纲"""
        self.current_outline_id = outline_id
        self.load_outline_tree()
        
    def load_outline_tree(self):
        """加载大纲树"""
        if not self.current_project_id:
            return
            
        try:
            self.outline_tree.clear()
            
            # 获取所有节点
            nodes = self.outline_manager.get_project_nodes(self.current_project_id)
            
            # 构建树结构
            node_items = {}
            root_nodes = []
            
            # 创建所有节点项
            for node in nodes:
                item = self.create_tree_item(node)
                node_items[node.id] = item
                
                if not node.parent_id:
                    root_nodes.append(item)
                    
            # 建立父子关系
            for node in nodes:
                if node.parent_id and node.parent_id in node_items:
                    parent_item = node_items[node.parent_id]
                    child_item = node_items[node.id]
                    parent_item.addChild(child_item)
                    
            # 添加根节点到树
            for root_item in root_nodes:
                self.outline_tree.addTopLevelItem(root_item)
                
            # 展开第一级
            self.outline_tree.expandToDepth(0)
            
        except Exception as e:
            logger.error(f"加载大纲树失败: {e}")
            QMessageBox.warning(self, "错误", f"加载大纲失败: {e}")
            
    def create_tree_item(self, node: OutlineNode) -> QTreeWidgetItem:
        """创建树节点项"""
        item = QTreeWidgetItem()
        item.setText(0, node.title or "未命名")
        item.setText(1, node.type.value)
        item.setText(2, node.status.value)
        item.setText(3, str(node.actual_word_count or 0))
        
        # 进度
        progress = node.get_progress()
        item.setText(4, f"{progress:.0%}")
        
        # 存储节点ID
        item.setData(0, Qt.ItemDataRole.UserRole, node.id)
        
        # 设置图标和颜色
        self.update_item_appearance(item, node)
        
        return item
        
    def update_item_appearance(self, item: QTreeWidgetItem, node: OutlineNode):
        """更新项目外观"""
        # 根据类型设置图标
        type_icons = {
            OutlineNodeType.PART: ":/icons/part.png",
            OutlineNodeType.CHAPTER: ":/icons/chapter.png",
            OutlineNodeType.SCENE: ":/icons/scene.png",
            OutlineNodeType.SECTION: ":/icons/section.png"
        }
        
        icon_path = type_icons.get(node.type, ":/icons/node.png")
        item.setIcon(0, QIcon(icon_path))
        
        # 根据状态设置颜色
        status_colors = {
            OutlineNodeStatus.PLANNING: QColor(100, 100, 100),
            OutlineNodeStatus.WRITING: QColor(0, 100, 200),
            OutlineNodeStatus.COMPLETED: QColor(0, 150, 0),
            OutlineNodeStatus.REVIEWING: QColor(200, 100, 0)
        }
        
        color = status_colors.get(node.status, QColor(0, 0, 0))
        item.setForeground(0, color)
        
    def on_node_selected(self, node_id: str):
        """节点选择处理"""
        try:
            node = self.outline_manager.get_node(node_id)
            if node:
                self.node_detail.load_node(node)
                self.node_selected.emit(node_id)
                
        except Exception as e:
            logger.error(f"加载节点详情失败: {e}")
            
    def on_selection_changed(self):
        """选择变化处理"""
        current_item = self.outline_tree.currentItem()
        has_selection = current_item is not None
        
        self.edit_node_btn.setEnabled(has_selection)
        self.delete_node_btn.setEnabled(has_selection)
        
    def on_node_moved(self, node_id: str, new_parent_id: str, new_index: int):
        """节点移动处理"""
        try:
            self.outline_manager.move_node(node_id, new_parent_id, new_index)
            self.load_outline_tree()
            self.outline_changed.emit()
            
        except Exception as e:
            logger.error(f"移动节点失败: {e}")
            QMessageBox.warning(self, "错误", f"移动节点失败: {e}")
            
    def on_node_updated(self, node_id: str):
        """节点更新处理"""
        self.load_outline_tree()
        self.outline_changed.emit()
        
    def add_node(self):
        """添加节点"""
        if not self.current_project_id:
            return
            
        try:
            # 获取当前选中的父节点
            parent_id = None
            current_item = self.outline_tree.currentItem()
            if current_item:
                parent_id = current_item.data(0, Qt.ItemDataRole.UserRole)
                
            # 创建新节点
            node = self.outline_manager.create_node(
                project_id=self.current_project_id,
                title="新节点",
                parent_id=parent_id
            )
            
            # 刷新树
            self.load_outline_tree()
            self.outline_changed.emit()
            
            # 选中新节点
            self.select_node(node.id)
            
        except Exception as e:
            logger.error(f"添加节点失败: {e}")
            QMessageBox.warning(self, "错误", f"添加节点失败: {e}")
            
    def edit_node(self):
        """编辑节点"""
        current_item = self.outline_tree.currentItem()
        if not current_item:
            return
            
        node_id = current_item.data(0, Qt.ItemDataRole.UserRole)
        if node_id:
            try:
                node = self.outline_manager.get_node(node_id)
                if node:
                    dialog = NodeEditDialog(node, self)
                    if dialog.exec() == QDialog.DialogCode.Accepted:
                        self.load_outline_tree()
                        self.outline_changed.emit()
                        
            except Exception as e:
                logger.error(f"编辑节点失败: {e}")
                QMessageBox.warning(self, "错误", f"编辑节点失败: {e}")
                
    def delete_node(self):
        """删除节点"""
        current_item = self.outline_tree.currentItem()
        if not current_item:
            return
            
        node_id = current_item.data(0, Qt.ItemDataRole.UserRole)
        if not node_id:
            return
            
        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除", 
            "确定要删除这个节点吗？\n注意：子节点也会被一起删除。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.outline_manager.delete_node(node_id)
                self.load_outline_tree()
                self.outline_changed.emit()
                
            except Exception as e:
                logger.error(f"删除节点失败: {e}")
                QMessageBox.warning(self, "错误", f"删除节点失败: {e}")
                
    def search_nodes(self, keyword: str):
        """搜索节点"""
        if not keyword.strip():
            # 显示所有节点
            for i in range(self.outline_tree.topLevelItemCount()):
                self.show_item_recursive(self.outline_tree.topLevelItem(i), True)
            return
            
        # 隐藏所有节点，然后显示匹配的
        for i in range(self.outline_tree.topLevelItemCount()):
            self.show_item_recursive(self.outline_tree.topLevelItem(i), False)
            
        # 搜索并显示匹配的节点
        self.search_and_show_items(keyword.lower())
        
    def show_item_recursive(self, item: QTreeWidgetItem, show: bool):
        """递归显示/隐藏项目"""
        item.setHidden(not show)
        for i in range(item.childCount()):
            self.show_item_recursive(item.child(i), show)
            
    def search_and_show_items(self, keyword: str):
        """搜索并显示匹配的项目"""
        for i in range(self.outline_tree.topLevelItemCount()):
            self.search_item_recursive(self.outline_tree.topLevelItem(i), keyword)
            
    def search_item_recursive(self, item: QTreeWidgetItem, keyword: str) -> bool:
        """递归搜索项目"""
        # 检查当前项目是否匹配
        matches = keyword in item.text(0).lower()
        
        # 检查子项目
        child_matches = False
        for i in range(item.childCount()):
            if self.search_item_recursive(item.child(i), keyword):
                child_matches = True
                
        # 如果当前项目或子项目匹配，则显示
        should_show = matches or child_matches
        item.setHidden(not should_show)
        
        return should_show
        
    def select_node(self, node_id: str):
        """选中指定节点"""
        def find_item_recursive(item: QTreeWidgetItem) -> Optional[QTreeWidgetItem]:
            if item.data(0, Qt.ItemDataRole.UserRole) == node_id:
                return item
            for i in range(item.childCount()):
                result = find_item_recursive(item.child(i))
                if result:
                    return result
            return None
            
        # 在所有顶级项目中搜索
        for i in range(self.outline_tree.topLevelItemCount()):
            item = find_item_recursive(self.outline_tree.topLevelItem(i))
            if item:
                self.outline_tree.setCurrentItem(item)
                self.outline_tree.scrollToItem(item)
                break